<template>
  <view class="env-switcher">
    <view class="env-title">当前环境</view>
    <view class="env-options">
      <view 
        class="env-option" 
        :class="{ active: isDev }"
      >
        <text>{{ isDev ? '开发环境' : '生产环境' }}</text>
        <text class="env-url">{{ apiBaseUrl }}</text>
      </view>
    </view>
    <view class="env-tip">
      <text>环境变量在构建时确定，要切换环境请使用不同的构建命令：</text>
      <view class="env-commands">
        <text class="command">开发环境: npm run dev:h5</text>
        <text class="command">生产环境: npm run build:h5</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue';
import { getConfig } from '../../api/config.js';

export default {
  name: 'EnvSwitcher',
  setup() {
    const apiBaseUrl = ref('');
    const isDev = ref(false);
    
    onMounted(() => {
      // 获取当前环境URL
      const config = getConfig();
      apiBaseUrl.value = config.baseUrl;
      
      // 判断当前是否为开发环境
      isDev.value = import.meta.env.MODE === 'development';
    });
    
    return {
      apiBaseUrl,
      isDev
    };
  }
}
</script>

<style>
.env-switcher {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.env-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.env-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.env-option {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  border: 2rpx solid transparent;
}

.env-option.active {
  border-color: #007AFF;
  background-color: rgba(0, 122, 255, 0.1);
}

.env-url {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

.env-tip {
  margin-top: 30rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.env-commands {
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.command {
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 10rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}
</style>