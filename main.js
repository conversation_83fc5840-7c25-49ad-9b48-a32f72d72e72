import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import request from './api/request'

Vue.config.productionTip = false
App.mpType = 'app'

// 将请求模块挂载到Vue原型上，方便在组件中使用
Vue.prototype.$request = request.request;
Vue.prototype.$http = request.http;

// 导入配置
import { BASE_URL } from './config/index.js';

// 全局混入，方便在所有组件中使用
Vue.mixin({
  data() {
    return {
      baseURL: BASE_URL
    }
  }
});

const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import request from './api/request'

export function createApp() {
  const app = createSSRApp(App)

  // 全局属性，在Vue 3中使用app.config.globalProperties替代Vue.prototype
  app.config.globalProperties.$request = request.request;
  app.config.globalProperties.$http = request.http;
  app.config.globalProperties.baseURL = BASE_URL;

  return {
    app
  }
}
// #endif