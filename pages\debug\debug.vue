<template>
  <view class="debug-container">
    <view class="debug-header">
      <text class="debug-title">应用调试信息</text>
    </view>
    
    <view class="debug-content">
      <view class="debug-item">
        <text class="debug-label">配置信息:</text>
        <text class="debug-value">{{ configInfo }}</text>
      </view>
      
      <view class="debug-item">
        <text class="debug-label">Cookie状态:</text>
        <text class="debug-value">{{ cookieStatus }}</text>
      </view>
      
      <view class="debug-item">
        <text class="debug-label">网络状态:</text>
        <text class="debug-value">{{ networkStatus }}</text>
      </view>
      
      <view class="debug-item">
        <text class="debug-label">页面路径:</text>
        <text class="debug-value">{{ currentPath }}</text>
      </view>
    </view>
    
    <view class="debug-actions">
      <button class="debug-btn" @click="testConfig">测试配置</button>
      <button class="debug-btn" @click="testCookie">测试Cookie</button>
      <button class="debug-btn" @click="clearData">清除数据</button>
      <button class="debug-btn" @click="goToLogin">去登录页</button>
      <button class="debug-btn" @click="goToIndex">去首页</button>
    </view>
    
    <view class="debug-logs">
      <text class="debug-label">调试日志:</text>
      <view class="log-item" v-for="(log, index) in logs" :key="index">
        <text class="log-text">{{ log }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { BASE_URL } from '@/config/index.js';

const configInfo = ref('');
const cookieStatus = ref('');
const networkStatus = ref('');
const currentPath = ref('');
const logs = ref([]);

const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString();
  logs.value.unshift(`[${timestamp}] ${message}`);
  if (logs.value.length > 10) {
    logs.value.pop();
  }
};

const testConfig = () => {
  configInfo.value = `BASE_URL: ${BASE_URL}`;
  addLog('配置测试完成');
};

const testCookie = async () => {
  const authorization = uni.getStorageSync('authorization');
  if (!authorization) {
    cookieStatus.value = '未找到Cookie';
    addLog('未找到Cookie');
    return;
  }
  
  try {
    addLog('开始测试Cookie...');
    const res = await uni.request({
      url: BASE_URL + 'user',
      method: 'GET',
      header: {
        'Cookie': `Authorization=${authorization}`,
        'Content-Type': 'application/json'
      },
      withCredentials: true
    });
    
    cookieStatus.value = `状态码: ${res.statusCode}, 响应: ${JSON.stringify(res.data)}`;
    addLog(`Cookie测试完成: ${res.statusCode}`);
  } catch (error) {
    cookieStatus.value = `错误: ${error.message}`;
    addLog(`Cookie测试失败: ${error.message}`);
  }
};

const clearData = () => {
  uni.removeStorageSync('authorization');
  cookieStatus.value = '数据已清除';
  addLog('本地数据已清除');
};

const goToLogin = () => {
  uni.reLaunch({
    url: '/pages/login/login'
  });
};

const goToIndex = () => {
  uni.switchTab({
    url: '/pages/tabbar/index/index'
  });
};

onMounted(() => {
  // 获取当前页面路径
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  currentPath.value = currentPage ? currentPage.route : '未知';
  
  // 检查网络状态
  uni.getNetworkType({
    success: (res) => {
      networkStatus.value = res.networkType;
    }
  });
  
  addLog('调试页面加载完成');
  testConfig();
});
</script>

<style>
.debug-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.debug-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.debug-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.debug-content {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.debug-item {
  margin-bottom: 20rpx;
  padding: 10rpx;
  border-bottom: 1px solid #eee;
}

.debug-label {
  font-weight: bold;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.debug-value {
  color: #333;
  word-break: break-all;
}

.debug-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.debug-btn {
  flex: 1;
  min-width: 140rpx;
  height: 70rpx;
  background-color: #007aff;
  color: white;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.debug-logs {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item {
  margin-bottom: 10rpx;
  padding: 5rpx;
  background-color: #f8f8f8;
  border-radius: 5rpx;
}

.log-text {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
}
</style>
