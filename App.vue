<script>
	import { BASE_URL } from '@/config/index.js';

	export default {
		globalData: {
			isLogin: false,
			userInfo: null
		},
		onLaunch: function() {
			console.log('App Launch')
			// 启动逻辑由startup页面处理
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 自动登录检查
			async checkAutoLogin() {
				const authorization = uni.getStorageSync('authorization');

				if (authorization) {
					console.log('检测到Cookie，验证有效性...');
					try {
						const res = await uni.request({
							url: BASE_URL + 'user',
							method: 'GET',
							header: {
								'Cookie': `Authorization=${authorization}`,
								'Content-Type': 'application/json'
							},
							withCredentials: true
						});

						console.log('Cookie验证结果:', res);

						// 检查返回的code是否为0
						if (res.data && res.data.code === 0) {
							// Cookie有效，跳转到首页
							console.log('Cookie有效，跳转到首页');
							this.globalData.isLogin = true;
							this.globalData.userInfo = res.data.data;

							uni.reLaunch({
								url: '/pages/tabbar/index/index'
							});
						} else {
							// Cookie无效，跳转到登录页
							console.log('Cookie无效，跳转到登录页');
							this.clearLoginData();
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					} catch (error) {
						console.log('Cookie验证失败:', error);
						// 验证失败，跳转到登录页
						this.clearLoginData();
						uni.reLaunch({
							url: '/pages/login/login'
						});
					}
				} else {
					// 没有Cookie，跳转到登录页
					console.log('未找到Cookie，跳转到登录页');
					this.globalData.isLogin = false;
					uni.reLaunch({
						url: '/pages/login/login'
					});
				}
			},

			// 清除登录数据
			clearLoginData() {
				uni.removeStorageSync('authorization');
				this.globalData.isLogin = false;
			},

			// 全局检查登录状态方法
			checkLogin() {
				const authorization = uni.getStorageSync('authorization');
				if (!authorization) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					uni.navigateTo({
						url: '/pages/login/login'
					});
					return false;
				}
				return true;
			},

			// 退出登录方法
			logout() {
				this.clearLoginData();
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import url('./static/iconfont.css');
</style>
