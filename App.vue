<script>
	export default {
		globalData: {
			isLogin: false
		},
		onLaunch: function() {
			console.log('App Launch')
			// 检查用户是否已登录
			const authorization = uni.getStorageSync('authorization');
			if (authorization) {
				// 用户已登录
				console.log('用户已登录');
				this.globalData.isLogin = true;
				// 如果当前页面是登录页，则跳转到首页
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				if (currentPage && currentPage.route && currentPage.route.includes('login')) {
					uni.switchTab({
						url: '/pages/tabbar/index/index'
					});
				}
			} else {
				// 用户未登录，可以在这里处理跳转到登录页面的逻辑
				console.log('用户未登录');
				this.globalData.isLogin = false;
			}
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 全局检查登录状态方法
			checkLogin() {
				const authorization = uni.getStorageSync('authorization');
				if (!authorization) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					uni.navigateTo({
						url: '/pages/login/login'
					});
					return false;
				}
				return true;
			},
			// 退出登录方法
			logout() {
				uni.removeStorageSync('authorization');
				this.globalData.isLogin = false;
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import url('./static/iconfont.css');
</style>
