<script>
	import { BASE_URL } from '@/config/index.js';

	export default {
		globalData: {
			isLogin: false
		},
		onLaunch: function() {
			console.log('App Launch')
			// 检查用户是否已登录
			this.checkAutoLogin();
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 自动登录检查
			async checkAutoLogin() {
				const authorization = uni.getStorageSync('authorization');
				if (authorization) {
					console.log('检测到本地token，验证有效性...');
					try {
						// 调用用户信息接口验证token有效性
						const res = await uni.request({
							url: BASE_URL + 'user',
							method: 'GET',
							header: {
								'Authorization': authorization,
								'Content-Type': 'application/json'
							}
						});

						console.log('Token验证结果:', res);

						// 检查返回的code是否为0（成功）
						if (res.data && res.data.code === 0) {
							// Token有效，设置登录状态
							console.log('Token有效，自动登录成功');
							this.globalData.isLogin = true;

							// 如果当前页面是登录页，则跳转到首页
							const pages = getCurrentPages();
							const currentPage = pages[pages.length - 1];
							if (currentPage && currentPage.route && currentPage.route.includes('login')) {
								uni.switchTab({
									url: '/pages/tabbar/index/index'
								});
							}
						} else {
							// Token无效，清除本地存储
							console.log('Token无效，清除本地存储');
							this.clearLoginData();
						}
					} catch (error) {
						console.log('Token验证失败:', error);
						// 验证失败，清除本地存储
						this.clearLoginData();
					}
				} else {
					// 没有token，用户未登录
					console.log('用户未登录');
					this.globalData.isLogin = false;
				}
			},

			// 清除登录数据
			clearLoginData() {
				uni.removeStorageSync('authorization');
				this.globalData.isLogin = false;
			},

			// 全局检查登录状态方法
			checkLogin() {
				const authorization = uni.getStorageSync('authorization');
				if (!authorization) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					uni.navigateTo({
						url: '/pages/login/login'
					});
					return false;
				}
				return true;
			},

			// 退出登录方法
			logout() {
				this.clearLoginData();
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import url('./static/iconfont.css');
</style>
