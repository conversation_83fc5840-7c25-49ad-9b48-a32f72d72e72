<script>
	import { BASE_URL } from '@/config/index.js';
	import { healthCheck } from '@/utils/app-validator.js';

	export default {
		globalData: {
			isLogin: false,
			userInfo: null
		},
		onLaunch: function() {
			console.log('App Launch')
			// 执行应用健康检查
			this.performHealthCheck();
			// 检查自动登录
			this.checkAutoLogin();
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 应用健康检查
			async performHealthCheck() {
				try {
					const result = await healthCheck();
					if (!result.overall.config) {
						console.error('配置验证失败，请检查config/index.js');
						uni.showToast({
							title: '应用配置错误',
							icon: 'none',
							duration: 3000
						});
					}
				} catch (error) {
					console.error('健康检查失败:', error);
				}
			},

			// 自动登录检查
			async checkAutoLogin() {
				const authorization = uni.getStorageSync('authorization');

				if (authorization) {
					console.log('检测到本地cookie，验证有效性...');
					try {
						// 使用Cookie方式调用用户信息接口验证token有效性
						const res = await uni.request({
							url: BASE_URL + 'user',
							method: 'GET',
							header: {
								'Cookie': `Authorization=${authorization}`,
								'Content-Type': 'application/json'
							},
							withCredentials: true
						});

						console.log('Cookie验证结果:', res);

						// 检查状态码和返回的code
						if (res.statusCode === 200 && res.data && res.data.code === 0) {
							// Cookie有效，设置登录状态
							console.log('Cookie有效，自动登录成功，用户信息:', res.data.data);
							this.globalData.isLogin = true;
							this.globalData.userInfo = res.data.data; // 保存用户信息

							// 延迟跳转到首页，避免路由冲突
							setTimeout(() => {
								uni.switchTab({
									url: '/pages/tabbar/index/index'
								});
							}, 500);
						} else {
							// Cookie无效，清除数据，停留在登录页
							console.log('Cookie无效，清除本地数据');
							this.clearLoginData();
						}
					} catch (error) {
						console.log('Cookie验证请求失败:', error);
						// 网络错误，清除数据，停留在登录页
						this.clearLoginData();
					}
				} else {
					// 没有cookie，停留在登录页
					console.log('未找到cookie，停留在登录页');
					this.globalData.isLogin = false;
				}
			},

			// 清除登录数据
			clearLoginData() {
				uni.removeStorageSync('authorization');
				this.globalData.isLogin = false;
			},

			// 全局检查登录状态方法
			checkLogin() {
				const authorization = uni.getStorageSync('authorization');
				if (!authorization) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					uni.navigateTo({
						url: '/pages/login/login'
					});
					return false;
				}
				return true;
			},

			// 退出登录方法
			logout() {
				this.clearLoginData();
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import url('./static/iconfont.css');
</style>
