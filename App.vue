<script>
	import { BASE_URL } from '@/config/index.js';

	export default {
		globalData: {
			isLogin: false,
			userInfo: null
		},
		onLaunch: function() {
			console.log('App Launch')
			// 启动逻辑由startup页面处理
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {

			// 清除登录数据
			clearLoginData() {
				uni.removeStorageSync('authorization');
				this.globalData.isLogin = false;
			},

			// 全局检查登录状态方法
			checkLogin() {
				const authorization = uni.getStorageSync('authorization');
				if (!authorization) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					uni.navigateTo({
						url: '/pages/login/login'
					});
					return false;
				}
				return true;
			},

			// 退出登录方法
			logout() {
				this.clearLoginData();
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import url('./static/iconfont.css');
</style>
