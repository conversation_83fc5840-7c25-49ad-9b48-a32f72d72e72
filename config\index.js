/**
 * 应用配置文件
 * 用于统一管理API地址和其他配置
 */

// 开发环境配置
const DEV_CONFIG = {
  BASE_URL: 'http://localhost:8080/',
  API_TIMEOUT: 10000,
  ENV: 'development'
};

// 生产环境配置
const PROD_CONFIG = {
  BASE_URL: 'http://**************:8080/',
  API_TIMEOUT: 15000,
  ENV: 'production'
};

// 根据环境变量或手动设置选择配置
// 在打包时可以通过修改这里来切换环境
const IS_DEV = false; // 设置为 true 使用开发环境，false 使用生产环境

// 当前使用的配置
const CONFIG = IS_DEV ? DEV_CONFIG : PROD_CONFIG;

// 导出配置
export const BASE_URL = CONFIG.BASE_URL;
export const API_TIMEOUT = CONFIG.API_TIMEOUT;
export const ENV = CONFIG.ENV;

// 导出完整配置对象
export default CONFIG;

// 环境切换函数（可在开发时使用）
export const switchEnv = (isDev = false) => {
  console.log(`切换到${isDev ? '开发' : '生产'}环境: ${isDev ? DEV_CONFIG.BASE_URL : PROD_CONFIG.BASE_URL}`);
  return isDev ? DEV_CONFIG : PROD_CONFIG;
};
