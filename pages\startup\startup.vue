<template>
  <view class="startup-container">
    <view class="logo-area">
      <image class="logo" src="/static/pet/rabbit.png"></image>
      <text class="app-title">GuiYuan</text>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue';
import { BASE_URL } from '@/config/index.js';

onMounted(async () => {
  console.log('启动页面加载');
  
  // 检查Cookie并跳转
  await checkCookieAndNavigate();
});

const checkCookieAndNavigate = async () => {
  const authorization = uni.getStorageSync('authorization');
  
  if (authorization) {
    console.log('检测到Cookie，验证中...');
    
    try {
      const res = await uni.request({
        url: BASE_URL + 'user',
        method: 'GET',
        header: {
          'Cookie': `Authorization=${authorization}`,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });
      
      console.log('Cookie验证结果:', res);
      
      if (res.data && res.data.code === 0) {
        // Cookie有效，跳转到首页
        console.log('Cookie有效，跳转到首页');
        uni.reLaunch({
          url: '/pages/tabbar/index/index'
        });
      } else {
        // Cookie无效，跳转到登录页
        console.log('Cookie无效，跳转到登录页');
        uni.removeStorageSync('authorization');
        uni.reLaunch({
          url: '/pages/login/login'
        });
      }
    } catch (error) {
      console.log('Cookie验证失败:', error);
      // 验证失败，跳转到登录页
      uni.removeStorageSync('authorization');
      uni.reLaunch({
        url: '/pages/login/login'
      });
    }
  } else {
    // 没有Cookie，跳转到登录页
    console.log('未找到Cookie，跳转到登录页');
    uni.reLaunch({
      url: '/pages/login/login'
    });
  }
};
</script>

<style>
.startup-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.logo-area {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 200rpx;
  height: 200rpx;
  border-radius: 30rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 40rpx;
}

.app-title {
  font-size: 60rpx;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}
</style>
