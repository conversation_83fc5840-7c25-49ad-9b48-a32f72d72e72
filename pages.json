{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/splash/splash",
			"style": {
				"navigationBarTitleText": "启动页",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "账号登陆"
			}
		},
		{
			"path": "pages/tabbar/index/index",
			"style": {
				"navigationBarTitleText": "桂圆可爱"
			}
		},
		{
			"path": "pages/tabbar/home/<USER>",
			"style": {
				"navigationBarTitleText": "个人主页"
			}
		},
		{
			"path": "pages/tabbar/message/message",
			"style": {
				"navigationBarTitleText": "我的消息"
			}
		},
		{
			"path": "pages/tabbar/daily/daily",
			"style": {
				"navigationBarTitleText": "发布日常"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		    "color": "#F0AD4E",
		    "selectedColor": "#3cc51f",
		    "borderStyle": "black",
		    "backgroundColor": "#F8F8F8",
		    "list": [{
		        "pagePath": "pages/tabbar/index/index",
		        "iconPath": "/static/index/index.png",
		        "selectedIconPath": "/static/index/indexselect.png",
		        "text": "首页"
		    },{
		        "pagePath": "pages/tabbar/daily/daily",
		        "iconPath": "/static/daily/daily.png",
		        "selectedIconPath": "/static/daily/dailyselect.png",
		        "text": "日常"
		    },{
		        "pagePath": "pages/tabbar/message/message",
				"iconPath": "/static/message/message.png",
				"selectedIconPath": "/static/message/messageselect.png",
		        "text": "消息"
		    },{
		        "pagePath": "pages/tabbar/home/<USER>",
				"iconPath": "/static/home/<USER>",
				"selectedIconPath": "/static/home/<USER>",
		        "text": "主页"
		    }]
		}
}