{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "A uni-app project", "main": "main.js", "scripts": {"dev:app": "uni -p app", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "pre-build-check": "node scripts/pre-build-check.js", "build:app": "npm run pre-build-check && uni build -p app", "build:custom": "npm run pre-build-check && uni build -p", "build:h5": "npm run pre-build-check && uni build", "build:h5:ssr": "npm run pre-build-check && uni build --ssr", "build:mp-alipay": "npm run pre-build-check && uni build -p mp-alipay", "build:mp-baidu": "npm run pre-build-check && uni build -p mp-baidu", "build:mp-kuaishou": "npm run pre-build-check && uni build -p mp-kuaishou", "build:mp-lark": "npm run pre-build-check && uni build -p mp-lark", "build:mp-qq": "npm run pre-build-check && uni build -p mp-qq", "build:mp-toutiao": "npm run pre-build-check && uni build -p mp-toutiao", "build:mp-weixin": "npm run pre-build-check && uni build -p mp-weixin", "build:quickapp-webview": "npm run pre-build-check && uni build -p quickapp-webview", "build:quickapp-webview-huawei": "npm run pre-build-check && uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "npm run pre-build-check && uni build -p quickapp-webview-union"}, "keywords": ["uni-app", "vue"], "author": "", "license": "ISC", "dependencies": {"@dcloudio/uni-app": "3.0.0-3080720230703001", "@dcloudio/uni-app-plus": "3.0.0-3080720230703001", "@dcloudio/uni-h5": "3.0.0-3080720230703001", "@dcloudio/uni-mp-alipay": "3.0.0-3080720230703001", "@dcloudio/uni-mp-baidu": "3.0.0-3080720230703001", "@dcloudio/uni-mp-weixin": "3.0.0-3080720230703001", "vue": "^3.2.45"}, "devDependencies": {"@dcloudio/types": "3.3.2", "@dcloudio/uni-automator": "3.0.0-3080720230703001", "@dcloudio/uni-cli-shared": "3.0.0-3080720230703001", "@dcloudio/uni-stacktracey": "3.0.0-3080720230703001", "@dcloudio/vite-plugin-uni": "3.0.0-3080720230703001", "sass": "^1.32.0", "vite": "4.1.4"}}