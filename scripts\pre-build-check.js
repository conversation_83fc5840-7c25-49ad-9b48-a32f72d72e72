/**
 * 打包前检查脚本
 * 确保所有配置正确，避免打包后出现问题
 */

const fs = require('fs');
const path = require('path');

// 检查文件是否存在
function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

// 读取文件内容
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    return null;
  }
}

// 检查配置文件
function checkConfigFile() {
  const configPath = path.join(__dirname, '../config/index.js');
  
  if (!checkFileExists(configPath)) {
    return {
      valid: false,
      message: 'config/index.js 文件不存在'
    };
  }
  
  const content = readFile(configPath);
  if (!content) {
    return {
      valid: false,
      message: '无法读取 config/index.js 文件'
    };
  }
  
  // 检查是否包含必要的配置
  const requiredConfigs = ['BASE_URL', 'API_TIMEOUT', 'ENV'];
  const missingConfigs = requiredConfigs.filter(config => !content.includes(config));
  
  if (missingConfigs.length > 0) {
    return {
      valid: false,
      message: `config/index.js 缺少配置: ${missingConfigs.join(', ')}`
    };
  }
  
  // 检查生产环境配置
  if (content.includes('IS_DEV = true')) {
    return {
      valid: false,
      message: '当前配置为开发环境，打包前请设置 IS_DEV = false'
    };
  }
  
  return {
    valid: true,
    message: '配置文件检查通过'
  };
}

// 检查页面文件中的硬编码URL
function checkHardcodedUrls() {
  const pagePaths = [
    '../pages/login/login.vue',
    '../pages/tabbar/home/<USER>',
    '../pages/tabbar/index/index.vue',
    '../pages/tabbar/daily/daily.vue',
    '../pages/tabbar/message/message.vue'
  ];
  
  const issues = [];
  
  pagePaths.forEach(pagePath => {
    const fullPath = path.join(__dirname, pagePath);
    if (checkFileExists(fullPath)) {
      const content = readFile(fullPath);
      if (content) {
        // 检查是否包含硬编码的URL
        const hardcodedUrls = content.match(/http:\/\/[0-9.]+:[0-9]+/g);
        if (hardcodedUrls) {
          issues.push({
            file: pagePath,
            urls: hardcodedUrls
          });
        }
      }
    }
  });
  
  return {
    valid: issues.length === 0,
    issues: issues,
    message: issues.length === 0 ? '未发现硬编码URL' : `发现硬编码URL: ${issues.length} 个文件`
  };
}

// 检查必要文件
function checkRequiredFiles() {
  const requiredFiles = [
    '../config/index.js',
    '../api/request.js',
    '../utils/app-validator.js',
    '../pages.json',
    '../manifest.json'
  ];
  
  const missingFiles = requiredFiles.filter(file => {
    const fullPath = path.join(__dirname, file);
    return !checkFileExists(fullPath);
  });
  
  return {
    valid: missingFiles.length === 0,
    missingFiles: missingFiles,
    message: missingFiles.length === 0 ? '所有必要文件存在' : `缺少文件: ${missingFiles.join(', ')}`
  };
}

// 主检查函数
function runPreBuildCheck() {
  console.log('=== 打包前检查开始 ===');
  
  const checks = [
    { name: '配置文件检查', fn: checkConfigFile },
    { name: '硬编码URL检查', fn: checkHardcodedUrls },
    { name: '必要文件检查', fn: checkRequiredFiles }
  ];
  
  let allPassed = true;
  const results = [];
  
  checks.forEach(check => {
    console.log(`\n执行 ${check.name}...`);
    const result = check.fn();
    results.push({ name: check.name, ...result });
    
    if (result.valid) {
      console.log(`✅ ${result.message}`);
    } else {
      console.log(`❌ ${result.message}`);
      allPassed = false;
      
      // 打印详细信息
      if (result.issues) {
        result.issues.forEach(issue => {
          console.log(`   - ${issue.file}: ${issue.urls.join(', ')}`);
        });
      }
      if (result.missingFiles) {
        result.missingFiles.forEach(file => {
          console.log(`   - ${file}`);
        });
      }
    }
  });
  
  console.log('\n=== 打包前检查完成 ===');
  
  if (allPassed) {
    console.log('🎉 所有检查通过，可以进行打包！');
    process.exit(0);
  } else {
    console.log('⚠️  发现问题，请修复后再进行打包！');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runPreBuildCheck();
}

module.exports = {
  runPreBuildCheck,
  checkConfigFile,
  checkHardcodedUrls,
  checkRequiredFiles
};
