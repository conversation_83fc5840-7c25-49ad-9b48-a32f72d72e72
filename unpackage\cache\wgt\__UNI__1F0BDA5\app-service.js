if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...n){uni.__log__?uni.__log__(e,t,...n):console[e].apply(console,[...n,t])}const n=(t=>(n,s=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,n,s)})("onLoad"),s="/static/pet/rabbit.png",o={BASE_URL:"http://124.223.80.197:8080/",API_TIMEOUT:15e3,ENV:"production"},r=o.BASE_URL,a=o.API_TIMEOUT,i={__name:"startup",setup(n){e.onMounted((async()=>{t("log","at pages/startup/startup.vue:15","启动页面加载"),await o()}));const o=async()=>{const e=uni.getStorageSync("authorization");if(e){t("log","at pages/startup/startup.vue:25","检测到Cookie，验证中...");try{const n=await uni.request({url:r+"user",method:"GET",header:{Cookie:`Authorization=${e}`,"Content-Type":"application/json"},withCredentials:!0});t("log","at pages/startup/startup.vue:38","Cookie验证结果:",n),n.data&&0===n.data.code?(t("log","at pages/startup/startup.vue:42","Cookie有效，跳转到首页"),uni.reLaunch({url:"/pages/tabbar/index/index"})):(t("log","at pages/startup/startup.vue:48","Cookie无效，跳转到登录页"),uni.removeStorageSync("authorization"),uni.reLaunch({url:"/pages/login/login"}))}catch(n){t("log","at pages/startup/startup.vue:55","Cookie验证失败:",n),uni.removeStorageSync("authorization"),uni.reLaunch({url:"/pages/login/login"})}}else t("log","at pages/startup/startup.vue:64","未找到Cookie，跳转到登录页"),uni.reLaunch({url:"/pages/login/login"})};return(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"startup-container"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("image",{class:"logo",src:s}),e.createElementVNode("text",{class:"app-title"},"GuiYuan")])]))}},c={__name:"login",setup(n){const o=e.ref(""),a=e.ref(""),i=e.ref(!1),c=e.ref(60),l=()=>{o.value=o.value.replace(/\D/g,""),o.value.length>11&&(o.value=o.value.slice(0,11))};e.onMounted((async()=>{t("log","at pages/login/login.vue:52","登录页面加载完成");if(getApp().globalData.isLogin)return t("log","at pages/login/login.vue:57","用户已登录，跳转到首页"),void uni.switchTab({url:"/pages/tabbar/index/index"})}));const u=()=>{o.value?11===o.value.length?i.value||((()=>{i.value=!0,c.value=60;const e=setInterval((()=>{c.value--,c.value<=0&&(clearInterval(e),i.value=!1)}),1e3)})(),uni.request({url:r+"user",method:"POST",data:{phone:o.value},header:{"Content-Type":"application/json"}}).then((e=>{t("log","at pages/login/login.vue:114",e),uni.showToast({title:"验证码已发送",icon:"success"})})).catch((e=>{t("log","at pages/login/login.vue:120",e),uni.showToast({title:"发送失败，请重试",icon:"none"}),i.value=!1}))):uni.showToast({title:"请输入11位手机号",icon:"none"}):uni.showToast({title:"请输入手机号",icon:"none"})},d=()=>{o.value&&a.value?11===o.value.length?6===a.value.length?uni.request({url:r+"user",method:"POST",data:{phone:o.value,code:a.value},header:{"Content-Type":"application/json"}}).then((e=>{t("log","at pages/login/login.vue:167",e),uni.setStorageSync("authorization",e.data.data.authorization),t("log","at pages/login/login.vue:171","保存的authorization:",uni.getStorageSync("authorization")),uni.reLaunch({url:"/pages/tabbar/index/index",success:function(){setTimeout((()=>{uni.showToast({title:"登录成功",icon:"success"})}),200)},fail:function(e){t("error","at pages/login/login.vue:186","跳转到首页失败:",e)}})})).catch((e=>{t("log","at pages/login/login.vue:190",e),uni.showToast({title:"登录失败，请重试",icon:"none"})})):uni.showToast({title:"验证码格式错误",icon:"none"}):uni.showToast({title:"请输入11位手机号",icon:"none"}):uni.showToast({title:"请输入手机号和验证码",icon:"none"})};return(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"login-page"},[e.createElementVNode("image",{class:"bg-image",src:s,mode:"aspectFill"}),e.createElementVNode("view",{class:"login-container"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("image",{class:"logo",src:s}),e.createElementVNode("text",{class:"app-title"},"GuiYuan")]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-item"},[e.withDirectives(e.createElementVNode("input",{type:"number",maxlength:"11","onUpdate:modelValue":n[0]||(n[0]=e=>o.value=e),placeholder:"请输入手机号","placeholder-class":"placeholder",onInput:l},null,544),[[e.vModelText,o.value]])]),e.createElementVNode("view",{class:"code-container"},[e.createElementVNode("view",{class:"input-item code-input"},[e.withDirectives(e.createElementVNode("input",{type:"number",maxlength:"6","onUpdate:modelValue":n[1]||(n[1]=e=>a.value=e),placeholder:"请输入验证码","placeholder-class":"placeholder"},null,512),[[e.vModelText,a.value]])]),e.createElementVNode("button",{class:"send-code-btn",onClick:u,disabled:i.value},e.toDisplayString(i.value?`${c.value}s后重试`:"发送验证码"),9,["disabled"])]),e.createElementVNode("button",{class:"login-btn",onClick:d},"登录")])])]))}},l="/static/user/avatar.jpg",u={__name:"index",setup(o){const a=e.ref([]),i=e.ref(1),c=e.ref(10),u=e.ref(0),d=e.ref(0),h=e.ref(!1),p=e.ref(!1),g=e=>{var t,n;const s=320+.8*(((null==(t=e.title)?void 0:t.length)||0)+((null==(n=e.content)?void 0:n.length)||0)),o=100*Math.random()-50;return Math.max(240,Math.min(500,s+o))+"rpx"},f=e.computed((()=>a.value.filter(((e,t)=>t%2==0)))),m=e.computed((()=>a.value.filter(((e,t)=>t%2==1)))),y=async(e=1,n=!1)=>{if(!h.value){h.value=!0;try{const s=uni.getStorageSync("authorization"),o=await uni.request({url:r+"recommend",method:"GET",header:{Cookie:`Authorization=${s}`},data:{pageNum:e,pageSize:c.value},withCredentials:!0});if(t("log","at pages/tabbar/index/index.vue:162","推荐列表响应:",o),o.data&&o.data.data){const e=o.data.data;u.value=e.total,d.value=e.pages;const t=(e.records||[]).map((e=>({...e,content:e.content||"这是一段随机生成的内容，用来展示瀑布流布局效果。内容长度不同，会影响卡片高度。"})));a.value=n?t:[...a.value,...t],p.value=i.value>=d.value}}catch(s){t("error","at pages/tabbar/index/index.vue:191","获取推荐列表失败:",s)}finally{h.value=!1}}},_=()=>{p.value||h.value||(i.value++,y(i.value))},v=e=>{uni.navigateTo({url:`/pages/recommend/detail?id=${e.id}`})};return e.onMounted((async()=>{await y(1,!0)})),n((()=>{const e=getApp();t("log","at pages/tabbar/index/index.vue:224","当前页面authorization值:",uni.getStorageSync("authorization")),e.checkLogin()&&(t("log","at pages/tabbar/index/index.vue:229","登录成功"),y(1,!0))})),(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("image",{class:"logo",src:s}),e.createElementVNode("text",{class:"title"},"推荐")]),e.createElementVNode("view",{class:"search-box"},[e.createElementVNode("text",{class:"search-placeholder"},"搜索")])]),e.createElementVNode("scroll-view",{class:"waterfall","scroll-y":"",onScrolltolower:_},[e.createElementVNode("view",{class:"waterfall-wrapper"},[e.createElementVNode("view",{class:"waterfall-column left-column"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(f.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"waterfall-item",key:n,onClick:e=>v(t),style:e.normalizeStyle({marginBottom:10+Math.floor(15*Math.random())+"rpx"})},[e.createElementVNode("view",{class:"media-wrapper",style:e.normalizeStyle({height:g(t)})},[e.createElementVNode("image",{class:"item-image",src:t.img,mode:"aspectFill"},null,8,["src"])],4),e.createElementVNode("view",{class:"item-content"},[e.createElementVNode("text",{class:"item-title",style:e.normalizeStyle({"-webkit-line-clamp":n%2==0?2:1})},e.toDisplayString(t.title),5),e.createElementVNode("text",{class:"item-desc",style:e.normalizeStyle({"-webkit-line-clamp":n%5==0?4:n%3==0?3:2})},e.toDisplayString(t.content),5),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("image",{class:"user-avatar",src:l}),e.createElementVNode("text",{class:"user-name"},"用户"+e.toDisplayString(t.userid),1)]),e.createElementVNode("view",{class:"like-info"},[e.createElementVNode("image",{class:"icon-rabbit",src:s})])])])],12,["onClick"])))),128))]),e.createElementVNode("view",{class:"waterfall-column right-column"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(m.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"waterfall-item",key:n,onClick:e=>v(t),style:e.normalizeStyle({marginBottom:10+Math.floor(15*Math.random())+"rpx"})},[e.createElementVNode("view",{class:"media-wrapper",style:e.normalizeStyle({height:g(t)})},[e.createElementVNode("image",{class:"item-image",src:t.img,mode:"aspectFill"},null,8,["src"]),n%7==0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"floating-title"},[e.createElementVNode("text",null,e.toDisplayString(t.title),1)])):e.createCommentVNode("",!0)],4),e.createElementVNode("view",{class:"item-content"},[e.createElementVNode("text",{class:"item-title",style:e.normalizeStyle({"-webkit-line-clamp":n%2==0?1:2})},e.toDisplayString(t.title),5),e.createElementVNode("text",{class:"item-desc",style:e.normalizeStyle({"-webkit-line-clamp":n%4==0?3:2})},e.toDisplayString(t.content),5),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("image",{class:"user-avatar",src:l}),e.createElementVNode("text",{class:"user-name"},"用户"+e.toDisplayString(t.userid),1)]),e.createElementVNode("view",{class:"like-info"},[e.createElementVNode("image",{class:"icon-rabbit",src:s})])])])],12,["onClick"])))),128))])]),h.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading"},[e.createElementVNode("text",{class:"loading-text"},"加载中...")])):e.createCommentVNode("",!0),p.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-more"},[e.createElementVNode("text",{class:"no-more-text"},"没有更多了~")])):e.createCommentVNode("",!0)],32)]))}},d={__name:"home",setup(n){const s=e.ref({}),o=e.ref({nickname:"",gender:"",birthday:""}),a=["男","女","其他"],i=async()=>{try{const e=await uni.request({url:r+"user",method:"GET",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`},withCredentials:!0});e.data&&e.data.data&&(s.value=e.data.data,o.value={id:s.value.id,nickname:s.value.nickname,gender:s.value.gender,birthday:s.value.birthday})}catch(e){t("error","at pages/tabbar/home/<USER>","获取用户信息失败:",e)}},c=async()=>{var e;try{const n=await uni.request({url:r+"user",method:"PUT",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`,"Content-Type":"application/json"},data:JSON.stringify(o.value),withCredentials:!0});if(n.data&&0===n.data.code)uni.showToast({title:"保存成功",icon:"success"}),i();else{const s=(null==(e=n.data)?void 0:e.message)||"保存失败";t("error","at pages/tabbar/home/<USER>","保存设置失败:",s),uni.showToast({title:s,icon:"none"})}}catch(n){t("error","at pages/tabbar/home/<USER>","保存设置失败:",n),uni.showToast({title:n.message||"保存失败",icon:"none",duration:2e3})}},l=e=>new Promise(((n,s)=>{uni.getFileSystemManager().readFile({filePath:e,encoding:"base64",success:e=>{const o={avatar:`data:image/jpeg;base64,${e.data}`,type:"avatar"};uni.request({url:r+"user",method:"PUT",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`,"Content-Type":"application/json"},data:o,success:e=>{try{const t=e.data;if(t&&0===t.code)uni.showToast({title:"头像更新成功",icon:"success"}),i(),n(t);else{const e=(null==t?void 0:t.message)||"头像更新失败";uni.showToast({title:e,icon:"none",duration:2e3}),s(new Error(e))}}catch(o){t("error","at pages/tabbar/home/<USER>","处理响应数据失败:",o),uni.showToast({title:"处理响应数据失败",icon:"none",duration:2e3}),s(o)}},fail:e=>{t("error","at pages/tabbar/home/<USER>","上传头像失败:",e),uni.showToast({title:"上传头像失败",icon:"none",duration:2e3}),s(e)}})},fail:e=>{t("error","at pages/tabbar/home/<USER>","读取图片失败:",e),uni.showToast({title:"读取图片失败",icon:"none",duration:2e3}),s(e)}})})),u=async()=>{try{const e=await uni.request({url:r+"user",method:"DELETE",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`},withCredentials:!0});e.data&&0===e.data.code?(uni.removeStorageSync("authorization"),uni.reLaunch({url:"/pages/login/login"})):uni.showToast({title:e.data.message||"退出失败",icon:"none"})}catch(e){t("error","at pages/tabbar/home/<USER>","退出登录失败:",e),uni.showToast({title:"退出失败",icon:"none"})}},d=()=>{uni.showActionSheet({itemList:["从相册选择","拍照"],success:e=>{0===e.tapIndex?h():1===e.tapIndex&&p()}})},h=async()=>{try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album"]});e.tempFilePaths.length>0&&await l(e.tempFilePaths[0])}catch(e){t("error","at pages/tabbar/home/<USER>","选择头像失败:",e)}},p=async()=>{try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["camera"]});e.tempFilePaths.length>0&&await l(e.tempFilePaths[0])}catch(e){t("error","at pages/tabbar/home/<USER>","拍照失败:",e)}},g=e=>{o.value.gender=a[e.detail.value]},f=e=>{o.value.birthday=e.detail.value};return e.onMounted((()=>{i()})),(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"user-card"},[e.createElementVNode("view",{class:"avatar-container"},[e.createElementVNode("image",{class:"avatar",src:s.value.avatar||"/static/user/avatar.jpg"},null,8,["src"]),e.createElementVNode("button",{class:"edit-avatar-btn",onClick:d},"更换头像")]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"nickname"},e.toDisplayString(s.value.nickname||"未设置昵称"),1),e.createElementVNode("text",{class:"username"},"ID: "+e.toDisplayString(s.value.id),1)])]),e.createElementVNode("view",{class:"settings-form"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"昵称"),e.withDirectives(e.createElementVNode("input",{class:"input","onUpdate:modelValue":n[0]||(n[0]=e=>o.value.nickname=e),placeholder:"请输入昵称"},null,512),[[e.vModelText,o.value.nickname]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"性别"),e.createElementVNode("picker",{class:"picker",mode:"selector",range:a,onChange:g},[e.createElementVNode("text",null,e.toDisplayString(o.value.gender||"请选择性别"),1)],32)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"生日"),e.createElementVNode("picker",{class:"picker",mode:"date",onChange:f},[e.createElementVNode("text",null,e.toDisplayString(o.value.birthday||"请选择生日"),1)],32)]),e.createElementVNode("button",{class:"save-btn",onClick:c},"保存设置"),e.createElementVNode("button",{class:"logout-btn",onClick:u},"退出登录")])]))}};const h=((e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n})({data:()=>({}),methods:{}},[["render",function(t,n,s,o,r,a){return e.openBlock(),e.createElementBlock("view")}]]),p={__name:"daily",setup(s){const o=getApp(),a=uni.getStorageSync("authorization"),i=e.ref([]),c=e.ref(!1),l=e.reactive({id:null,userId:null,title:"",content:"",image:""}),u=async()=>{try{const n=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],maxSize:5242880});if(!n||!n.tempFilePaths||!n.tempFilePaths.length)throw new Error("选择图片失败");const s=n.tempFilePaths[0],o=await uni.uploadFile({url:r+"daily/upload",filePath:s,name:"file",header:{Cookie:`Authorization=${a}`,"Content-Type":"multipart/form-data"},timeout:6e4,formData:{type:"image"}});if(!o||!o.statusCode||200!==o.statusCode)throw new Error(`图片上传失败: ${(null==o?void 0:o.errMsg)||"未知错误"}`);try{const e=JSON.parse(o.data);if(0!==e.code||!e.data)throw new Error(e.message||"图片上传失败");l.image=e.data}catch(e){throw t("error","at pages/tabbar/daily/daily.vue:105","解析上传响应失败:",e),new Error("服务器响应格式错误")}}catch(n){t("error","at pages/tabbar/daily/daily.vue:109","图片处理失败:",n),uni.showToast({title:n.message||"图片处理失败",icon:"none",duration:2e3})}},d=e=>{uni.previewImage({urls:[e]})},h=async()=>{try{const e=await uni.request({url:r+"daily",method:"GET",header:{Cookie:`Authorization=${a}`},data:{userId:o.globalData.userId}});0===e.data.code?i.value=e.data.data:uni.showToast({title:e.data.message||"获取日记失败",icon:"none"})}catch(e){t("error","at pages/tabbar/daily/daily.vue:142","获取日记列表失败:",e),uni.showToast({title:"获取日记列表失败",icon:"none"})}},p=()=>{g(),c.value=!1},g=()=>{l.id=null,l.title="",l.content="",l.image=""};return n((()=>{o.checkLogin()&&h()})),(n,s)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[i.value&&i.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"daily-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.value,((n,s)=>(e.openBlock(),e.createElementBlock("view",{class:"daily-item",key:s},[e.createElementVNode("view",{class:"daily-header"},[e.createElementVNode("text",{class:"daily-title"},e.toDisplayString(n.title),1),e.createElementVNode("view",{class:"daily-actions"},[e.createElementVNode("button",{class:"action-btn edit",onClick:e=>{return t=n,Object.assign(l,t),void(c.value=!0);var t}},"编辑",8,["onClick"]),e.createElementVNode("button",{class:"action-btn delete",onClick:e=>(async e=>{try{const[t,n]=await uni.showModal({title:"确认删除",content:"确定要删除这条日记吗？"});if(n.confirm){const t=await uni.request({url:r+"daily",method:"DELETE",header:{Cookie:`Authorization=${a}`},data:{id:e,userId:o.globalData.userId}});0===t.data.code?(uni.showToast({title:"删除成功",icon:"success"}),h()):uni.showToast({title:t.data.message||"删除失败",icon:"none"})}}catch(n){t("error","at pages/tabbar/daily/daily.vue:259","删除日记失败:",n),uni.showToast({title:"删除失败",icon:"none"})}})(n.id)},"删除",8,["onClick"])])]),e.createElementVNode("view",{class:"daily-content"},e.toDisplayString(n.content),1),n.image?(e.openBlock(),e.createElementBlock("image",{key:0,src:n.image,class:"daily-image",mode:"aspectFill",onClick:e=>d(n.image)},null,8,["src","onClick"])):e.createCommentVNode("",!0)])))),128))])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"form-header"},[e.createElementVNode("text",{class:"form-title"},e.toDisplayString(c.value?"编辑日记":"新建日记"),1)]),e.withDirectives(e.createElementVNode("input",{class:"input-field","onUpdate:modelValue":s[0]||(s[0]=e=>l.title=e),placeholder:"请输入标题"},null,512),[[e.vModelText,l.title]]),e.withDirectives(e.createElementVNode("textarea",{class:"textarea-field","onUpdate:modelValue":s[1]||(s[1]=e=>l.content=e),placeholder:"写下你的想法..."},null,512),[[e.vModelText,l.content]]),e.createElementVNode("view",{class:"image-upload"},[l.image?(e.openBlock(),e.createElementBlock("image",{key:0,src:l.image,class:"preview-image",mode:"aspectFill",onClick:s[2]||(s[2]=e=>d(l.image))},null,8,["src"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"upload-placeholder",onClick:u},[e.createElementVNode("text",{class:"iconfont icon-camera"}),e.createElementVNode("text",null,"点击添加图片")]))]),e.createElementVNode("view",{class:"form-actions"},[e.createElementVNode("button",{class:"submit-btn",onClick:s[3]||(s[3]=e=>c.value?(async()=>{if(l.title&&l.content)try{const e=await uni.request({url:r+"daily",method:"PUT",header:{Cookie:`Authorization=${a}`},data:l});0===e.data.code?(uni.showToast({title:"更新成功",icon:"success"}),h(),g(),c.value=!1):uni.showToast({title:e.data.message||"更新失败",icon:"none"})}catch(e){t("error","at pages/tabbar/daily/daily.vue:222","更新日记失败:",e),uni.showToast({title:"更新失败",icon:"none"})}else uni.showToast({title:"请填写标题和内容",icon:"none"})})():(async()=>{if(l.title&&l.content)try{l.userId=o.globalData.userId;const e=await uni.request({url:r+"daily",method:"POST",header:{Cookie:`Authorization=${a}`},data:l});0===e.data.code?(uni.showToast({title:"发布成功",icon:"success"}),h(),g()):uni.showToast({title:e.data.message||"发布失败",icon:"none"})}catch(e){t("error","at pages/tabbar/daily/daily.vue:182","添加日记失败:",e),uni.showToast({title:"发布失败",icon:"none"})}else uni.showToast({title:"请填写标题和内容",icon:"none"})})())},e.toDisplayString(c.value?"更新":"发布"),1),c.value?(e.openBlock(),e.createElementBlock("button",{key:0,class:"cancel-btn",onClick:p},"取消")):e.createCommentVNode("",!0)])])]))}};__definePage("pages/startup/startup",i),__definePage("pages/login/login",c),__definePage("pages/tabbar/index/index",u),__definePage("pages/tabbar/home/<USER>",d),__definePage("pages/tabbar/message/message",h),__definePage("pages/tabbar/daily/daily",p);const g={globalData:{isLogin:!1,userInfo:null},onLaunch:function(){t("log","at App.vue:10","App Launch")},onShow:function(){t("log","at App.vue:14","App Show")},onHide:function(){t("log","at App.vue:17","App Hide")},methods:{async checkAutoLogin(){const e=uni.getStorageSync("authorization");if(e){t("log","at App.vue:25","检测到Cookie，验证有效性...");try{const n=await uni.request({url:r+"user",method:"GET",header:{Cookie:`Authorization=${e}`,"Content-Type":"application/json"},withCredentials:!0});t("log","at App.vue:37","Cookie验证结果:",n),n.data&&0===n.data.code?(t("log","at App.vue:42","Cookie有效，跳转到首页"),this.globalData.isLogin=!0,this.globalData.userInfo=n.data.data,uni.reLaunch({url:"/pages/tabbar/index/index"})):(t("log","at App.vue:51","Cookie无效，跳转到登录页"),this.clearLoginData(),uni.reLaunch({url:"/pages/login/login"}))}catch(n){t("log","at App.vue:58","Cookie验证失败:",n),this.clearLoginData(),uni.reLaunch({url:"/pages/login/login"})}}else t("log","at App.vue:67","未找到Cookie，跳转到登录页"),this.globalData.isLogin=!1,uni.reLaunch({url:"/pages/login/login"})},clearLoginData(){uni.removeStorageSync("authorization"),this.globalData.isLogin=!1},checkLogin:()=>!!uni.getStorageSync("authorization")||(uni.showToast({title:"请先登录",icon:"none"}),uni.navigateTo({url:"/pages/login/login"}),!1),logout(){this.clearLoginData(),uni.reLaunch({url:"/pages/login/login"})}}},f={baseURL:r,timeout:a,header:{"Content-Type":"application/json"}},m=(e={})=>(e=(e=>{e.url=(e.baseURL||f.baseURL)+(e.url||""),e.header={...f.header,...e.header||{}},e.timeout=e.timeout||f.timeout;const n=uni.getStorageSync("authorization");return n&&(e.header.Authorization=n),t("log","at api/request.js:32","请求拦截器 ==>",{url:e.url,method:e.method,data:e.data,header:e.header}),e})(e),new Promise(((n,s)=>{uni.request({...e,success:o=>{((e,n)=>{var s;return t("log","at api/request.js:50","响应拦截器 ==>",{url:n.url,data:e,statusCode:e.statusCode}),e.statusCode>=200&&e.statusCode<300?Promise.resolve(e.data):401===e.statusCode?(uni.removeStorageSync("authorization"),uni.showToast({title:"登录已过期，请重新登录",icon:"none"}),uni.reLaunch({url:"/pages/login/login"}),Promise.reject(e)):(uni.showToast({title:(null==(s=e.data)?void 0:s.message)||`请求失败(${e.statusCode})`,icon:"none"}),Promise.reject(e))})(o,e).then(n).catch(s)},fail:n=>{((e,n)=>(t("error","at api/request.js:88","请求错误 ==>",{url:n.url,error:e}),uni.showToast({title:"网络异常，请稍后重试",icon:"none"}),Promise.reject(e)))(n,e).catch(s)}})}))),y={get:(e,t={},n={})=>m({url:e,data:t,method:"GET",...n}),post:(e,t={},n={})=>m({url:e,data:t,method:"POST",...n}),put:(e,t={},n={})=>m({url:e,data:t,method:"PUT",...n}),delete:(e,t={},n={})=>m({url:e,data:t,method:"DELETE",...n}),upload:(e,t,n={},s={})=>{const o={url:(s.baseURL||f.baseURL)+e,filePath:t,name:s.name||"file",formData:n,header:s.header||{}},r=uni.getStorageSync("authorization");return r&&(o.header.Authorization=r),new Promise(((e,t)=>{uni.uploadFile({...o,success:n=>{if(n.statusCode>=200&&n.statusCode<300)try{const t=JSON.parse(n.data);e(t)}catch(Le){e(n.data)}else t(n)},fail:t})}))}},_={config:f,request:m,http:y},v={pages:[{path:"pages/startup/startup",style:{navigationBarTitleText:"",navigationStyle:"custom"}},{path:"pages/login/login",style:{navigationBarTitleText:"账号登陆"}},{path:"pages/tabbar/index/index",style:{navigationBarTitleText:"桂圆可爱"}},{path:"pages/tabbar/home/<USER>",style:{navigationBarTitleText:"个人主页"}},{path:"pages/tabbar/message/message",style:{navigationBarTitleText:"我的消息"}},{path:"pages/tabbar/daily/daily",style:{navigationBarTitleText:"发布日常"}}],globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},tabBar:{color:"#F0AD4E",selectedColor:"#3cc51f",borderStyle:"black",backgroundColor:"#F8F8F8",list:[{pagePath:"pages/tabbar/index/index",iconPath:"/static/index/index.png",selectedIconPath:"/static/index/indexselect.png",text:"首页"},{pagePath:"pages/tabbar/daily/daily",iconPath:"/static/daily/daily.png",selectedIconPath:"/static/daily/dailyselect.png",text:"日常"},{pagePath:"pages/tabbar/message/message",iconPath:"/static/message/message.png",selectedIconPath:"/static/message/messageselect.png",text:"消息"},{pagePath:"pages/tabbar/home/<USER>",iconPath:"/static/home/<USER>",selectedIconPath:"/static/home/<USER>",text:"主页"}]}};function w(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var k=w((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),s={},o=s.lib={},r=o.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},a=o.WordArray=r.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,n=e.words,s=this.sigBytes,o=e.sigBytes;if(this.clamp(),s%4)for(var r=0;r<o;r++){var a=n[r>>>2]>>>24-r%4*8&255;t[s+r>>>2]|=a<<24-(s+r)%4*8}else for(r=0;r<o;r+=4)t[s+r>>>2]=n[r>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=r.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,s=[],o=function(t){var n=987654321,s=4294967295;return function(){var o=((n=36969*(65535&n)+(n>>16)&s)<<16)+(t=18e3*(65535&t)+(t>>16)&s)&s;return o/=4294967296,(o+=.5)*(e.random()>.5?1:-1)}},r=0;r<t;r+=4){var i=o(4294967296*(n||e.random()));n=987654071*i(),s.push(4294967296*i()|0)}return new a.init(s,t)}}),i=s.enc={},c=i.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],o=0;o<n;o++){var r=t[o>>>2]>>>24-o%4*8&255;s.push((r>>>4).toString(16)),s.push((15&r).toString(16))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s+=2)n[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new a.init(n,t/2)}},l=i.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],o=0;o<n;o++){var r=t[o>>>2]>>>24-o%4*8&255;s.push(String.fromCharCode(r))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s++)n[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new a.init(n,t)}},u=i.Utf8={stringify:function(e){try{return decodeURIComponent(escape(l.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return l.parse(unescape(encodeURIComponent(e)))}},d=o.BufferedBlockAlgorithm=r.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=u.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,s=n.words,o=n.sigBytes,r=this.blockSize,i=o/(4*r),c=(i=t?e.ceil(i):e.max((0|i)-this._minBufferSize,0))*r,l=e.min(4*c,o);if(c){for(var u=0;u<c;u+=r)this._doProcessBlock(s,u);var d=s.splice(0,c);n.sigBytes-=l}return new a.init(d,l)},clone:function(){var e=r.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=d.extend({cfg:r.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}});var h=s.algo={};return s}(Math),n)})),S=k,b=(w((function(e,t){var n;e.exports=(n=S,function(e){var t=n,s=t.lib,o=s.WordArray,r=s.Hasher,a=t.algo,i=[];!function(){for(var t=0;t<64;t++)i[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=a.MD5=r.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var s=t+n,o=e[s];e[s]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var r=this._hash.words,a=e[t+0],c=e[t+1],p=e[t+2],g=e[t+3],f=e[t+4],m=e[t+5],y=e[t+6],_=e[t+7],v=e[t+8],w=e[t+9],k=e[t+10],S=e[t+11],b=e[t+12],T=e[t+13],I=e[t+14],A=e[t+15],P=r[0],E=r[1],x=r[2],C=r[3];P=l(P,E,x,C,a,7,i[0]),C=l(C,P,E,x,c,12,i[1]),x=l(x,C,P,E,p,17,i[2]),E=l(E,x,C,P,g,22,i[3]),P=l(P,E,x,C,f,7,i[4]),C=l(C,P,E,x,m,12,i[5]),x=l(x,C,P,E,y,17,i[6]),E=l(E,x,C,P,_,22,i[7]),P=l(P,E,x,C,v,7,i[8]),C=l(C,P,E,x,w,12,i[9]),x=l(x,C,P,E,k,17,i[10]),E=l(E,x,C,P,S,22,i[11]),P=l(P,E,x,C,b,7,i[12]),C=l(C,P,E,x,T,12,i[13]),x=l(x,C,P,E,I,17,i[14]),P=u(P,E=l(E,x,C,P,A,22,i[15]),x,C,c,5,i[16]),C=u(C,P,E,x,y,9,i[17]),x=u(x,C,P,E,S,14,i[18]),E=u(E,x,C,P,a,20,i[19]),P=u(P,E,x,C,m,5,i[20]),C=u(C,P,E,x,k,9,i[21]),x=u(x,C,P,E,A,14,i[22]),E=u(E,x,C,P,f,20,i[23]),P=u(P,E,x,C,w,5,i[24]),C=u(C,P,E,x,I,9,i[25]),x=u(x,C,P,E,g,14,i[26]),E=u(E,x,C,P,v,20,i[27]),P=u(P,E,x,C,T,5,i[28]),C=u(C,P,E,x,p,9,i[29]),x=u(x,C,P,E,_,14,i[30]),P=d(P,E=u(E,x,C,P,b,20,i[31]),x,C,m,4,i[32]),C=d(C,P,E,x,v,11,i[33]),x=d(x,C,P,E,S,16,i[34]),E=d(E,x,C,P,I,23,i[35]),P=d(P,E,x,C,c,4,i[36]),C=d(C,P,E,x,f,11,i[37]),x=d(x,C,P,E,_,16,i[38]),E=d(E,x,C,P,k,23,i[39]),P=d(P,E,x,C,T,4,i[40]),C=d(C,P,E,x,a,11,i[41]),x=d(x,C,P,E,g,16,i[42]),E=d(E,x,C,P,y,23,i[43]),P=d(P,E,x,C,w,4,i[44]),C=d(C,P,E,x,b,11,i[45]),x=d(x,C,P,E,A,16,i[46]),P=h(P,E=d(E,x,C,P,p,23,i[47]),x,C,a,6,i[48]),C=h(C,P,E,x,_,10,i[49]),x=h(x,C,P,E,I,15,i[50]),E=h(E,x,C,P,m,21,i[51]),P=h(P,E,x,C,b,6,i[52]),C=h(C,P,E,x,g,10,i[53]),x=h(x,C,P,E,k,15,i[54]),E=h(E,x,C,P,c,21,i[55]),P=h(P,E,x,C,v,6,i[56]),C=h(C,P,E,x,A,10,i[57]),x=h(x,C,P,E,y,15,i[58]),E=h(E,x,C,P,T,21,i[59]),P=h(P,E,x,C,f,6,i[60]),C=h(C,P,E,x,S,10,i[61]),x=h(x,C,P,E,p,15,i[62]),E=h(E,x,C,P,w,21,i[63]),r[0]=r[0]+P|0,r[1]=r[1]+E|0,r[2]=r[2]+x|0,r[3]=r[3]+C|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,o=8*t.sigBytes;n[o>>>5]|=128<<24-o%32;var r=e.floor(s/4294967296),a=s;n[15+(o+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),n[14+(o+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(n.length+1),this._process();for(var i=this._hash,c=i.words,l=0;l<4;l++){var u=c[l];c[l]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return i},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,n,s,o,r,a){var i=e+(t&n|~t&s)+o+a;return(i<<r|i>>>32-r)+t}function u(e,t,n,s,o,r,a){var i=e+(t&s|n&~s)+o+a;return(i<<r|i>>>32-r)+t}function d(e,t,n,s,o,r,a){var i=e+(t^n^s)+o+a;return(i<<r|i>>>32-r)+t}function h(e,t,n,s,o,r,a){var i=e+(n^(t|~s))+o+a;return(i<<r|i>>>32-r)+t}t.MD5=r._createHelper(c),t.HmacMD5=r._createHmacHelper(c)}(Math),n.MD5)})),w((function(e,t){var n,s,o;e.exports=(s=(n=S).lib.Base,o=n.enc.Utf8,void(n.algo.HMAC=s.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=o.parse(t));var n=e.blockSize,s=4*n;t.sigBytes>s&&(t=e.finalize(t)),t.clamp();for(var r=this._oKey=t.clone(),a=this._iKey=t.clone(),i=r.words,c=a.words,l=0;l<n;l++)i[l]^=1549556828,c[l]^=909522486;r.sigBytes=a.sigBytes=s,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})))})),w((function(e,t){e.exports=S.HmacMD5}))),T=w((function(e,t){e.exports=S.enc.Utf8})),I=w((function(e,t){var n,s,o;e.exports=(o=(s=n=S).lib.WordArray,s.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,s=this._map;e.clamp();for(var o=[],r=0;r<n;r+=3)for(var a=(t[r>>>2]>>>24-r%4*8&255)<<16|(t[r+1>>>2]>>>24-(r+1)%4*8&255)<<8|t[r+2>>>2]>>>24-(r+2)%4*8&255,i=0;i<4&&r+.75*i<n;i++)o.push(s.charAt(a>>>6*(3-i)&63));var c=s.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,n=this._map,s=this._reverseMap;if(!s){s=this._reverseMap=[];for(var r=0;r<n.length;r++)s[n.charCodeAt(r)]=r}var a=n.charAt(64);if(a){var i=e.indexOf(a);-1!==i&&(t=i)}return function(e,t,n){for(var s=[],r=0,a=0;a<t;a++)if(a%4){var i=n[e.charCodeAt(a-1)]<<a%4*2,c=n[e.charCodeAt(a)]>>>6-a%4*2;s[r>>>2]|=(i|c)<<24-r%4*8,r++}return o.create(s,r)}(e,t,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},n.enc.Base64)}));const A="uni_id_token",P="uni_id_token_expired",E="FUNCTION",x="OBJECT",C="CLIENT_DB",N="pending",L="rejected";function O(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function U(e){return"object"===O(e)}function D(e){return"function"==typeof e}function R(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}const M="REJECTED",q="NOT_PENDING";class F{constructor({createPromise:e,retryRule:t=M}={}){this.createPromise=e,this.status=null,this.promise=null,this.retryRule=t}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case M:return this.status===L;case q:return this.status!==N}}exec(){return this.needRetry?(this.status=N,this.promise=this.createPromise().then((e=>(this.status="fulfilled",Promise.resolve(e))),(e=>(this.status=L,Promise.reject(e)))),this.promise):this.promise}}function j(e){return e&&"string"==typeof e?JSON.parse(e):e}const V=j([]);j("");const B=j('[{"provider":"aliyun","spaceName":"suakitsu","spaceId":"mp-2e6e8eae-0872-49fd-b869-9ea34baed9f1","clientSecret":"nJUVHv9y7feXJzGSaPlaVg==","endpoint":"https://api.next.bspapp.com"}]')||[];let $="";try{$="__UNI__1F0BDA5"}catch(Le){}let K,z={};function H(e,t={}){var n,s;return n=z,s=e,Object.prototype.hasOwnProperty.call(n,s)||(z[e]=t),z[e]}z=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={};const J=["invoke","success","fail","complete"],W=H("_globalUniCloudInterceptor");function G(e,t){W[e]||(W[e]={}),U(t)&&Object.keys(t).forEach((n=>{J.indexOf(n)>-1&&function(e,t,n){let s=W[e][t];s||(s=W[e][t]=[]),-1===s.indexOf(n)&&D(n)&&s.push(n)}(e,n,t[n])}))}function Q(e,t){W[e]||(W[e]={}),U(t)?Object.keys(t).forEach((n=>{J.indexOf(n)>-1&&function(e,t,n){const s=W[e][t];if(!s)return;const o=s.indexOf(n);o>-1&&s.splice(o,1)}(e,n,t[n])})):delete W[e]}function Y(e,t){return e&&0!==e.length?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function X(e,t){return W[e]&&W[e][t]||[]}function Z(e){G("callObject",e)}const ee=H("_globalUniCloudListener"),te="response",ne="needLogin",se="refreshToken",oe="clientdb",re="cloudfunction",ae="cloudobject";function ie(e){return ee[e]||(ee[e]=[]),ee[e]}function ce(e,t){const n=ie(e);n.includes(t)||n.push(t)}function le(e,t){const n=ie(e),s=n.indexOf(t);-1!==s&&n.splice(s,1)}function ue(e,t){const n=ie(e);for(let s=0;s<n.length;s++)(0,n[s])(t)}let de,he=!1;function pe(){return de||(de=new Promise((e=>{he&&e(),function t(){if("function"==typeof getCurrentPages){const t=getCurrentPages();t&&t[0]&&(he=!0,e())}he||setTimeout((()=>{t()}),30)}()})),de)}function ge(e){const t={};for(const n in e){const s=e[n];D(s)&&(t[n]=R(s))}return t}class fe extends Error{constructor(e){super(e.message),this.errMsg=e.message||e.errMsg||"unknown system error",this.code=this.errCode=e.code||e.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=e.subject||e.errSubject,this.cause=e.cause,this.requestId=e.requestId}toJson(e=0){if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}var me={request:e=>uni.request(e),uploadFile:e=>uni.uploadFile(e),setStorageSync:(e,t)=>uni.setStorageSync(e,t),getStorageSync:e=>uni.getStorageSync(e),removeStorageSync:e=>uni.removeStorageSync(e),clearStorageSync:()=>uni.clearStorageSync(),connectSocket:e=>uni.connectSocket(e)};function ye(e){return e&&ye(e.__v_raw)||e}function _e(){return{token:me.getStorageSync(A)||me.getStorageSync("uniIdToken"),tokenExpired:me.getStorageSync(P)}}function ve({token:e,tokenExpired:t}={}){e&&me.setStorageSync(A,e),t&&me.setStorageSync(P,t)}let we,ke;function Se(){return we||(we=uni.getSystemInfoSync()),we}function be(){let e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;const{scene:n,channel:s}=uni.getLaunchOptionsSync();e=s,t=n}}catch(n){}return{channel:e,scene:t}}let Te={};function Ie(){const e=uni.getLocale&&uni.getLocale()||"en";if(ke)return{...Te,...ke,locale:e,LOCALE:e};const t=Se(),{deviceId:n,osName:s,uniPlatform:o,appId:r}=t,a=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(const i in t)Object.hasOwnProperty.call(t,i)&&-1===a.indexOf(i)&&delete t[i];return ke={PLATFORM:o,OS:s,APPID:r,DEVICEID:n,...be(),...t},{...Te,...ke,locale:e,LOCALE:e}}var Ae=function(e,t){let n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),b(n,t).toString()},Pe=function(e,t){return new Promise(((n,s)=>{t(Object.assign(e,{complete(e){e||(e={});const t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){const n=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",o=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return s(new fe({code:n,message:o,requestId:t}))}const o=e.data;if(o.error)return s(new fe({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,n(o)}}))}))},Ee=function(e){return I.stringify(T.parse(e))},xe={init(e){const t=new class{constructor(e){["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),this.config=Object.assign({},{endpoint:0===e.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=me,this._getAccessTokenPromiseHub=new F({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((e=>{if(!e.result||!e.result.accessToken)throw new fe({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(e.result.accessToken)})),retryRule:q})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return Pe(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then((()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch((t=>new Promise(((e,n)=>{!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((()=>this.getAccessToken())).then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)})))):this.getAccessToken().then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)}))))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=Ae(t.data,this.config.clientSecret),t}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),s={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,s["x-basement-token"]=this.accessToken),s["x-serverless-sign"]=Ae(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:s}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request({...this.setupRequest(t),timeout:e.timeout})}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:n,filePath:s,fileType:o,onUploadProgress:r}){return new Promise(((a,i)=>{const c=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:s,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success(e){e&&e.statusCode<400?a(e):i(new fe({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){i(new fe({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof r&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{r({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:n="image",cloudPathAsRealPath:s=!1,onUploadProgress:o,config:r}){if("string"!==O(t))throw new fe({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new fe({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new fe({code:"INVALID_PARAM",message:"cloudPath不合法"});const a=r&&r.envType||this.config.envType;if(s&&("/"!==t[0]&&(t="/"+t),t.indexOf("\\")>-1))throw new fe({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const i=(await this.getOSSUploadOptionsFromPath({env:a,filename:s?t.split("/").pop():t,fileId:s?t:void 0})).result,c="https://"+i.cdnDomain+"/"+i.ossPath,{securityToken:l,accessKeyId:u,signature:d,host:h,ossPath:p,id:g,policy:f,ossCallbackUrl:m}=i,y={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:u,Signature:d,host:h,id:g,key:p,policy:f,success_action_status:200};if(l&&(y["x-oss-security-token"]=l),m){const e=JSON.stringify({callbackUrl:m,callbackBody:JSON.stringify({fileId:g,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});y.callback=Ee(e)}const _={url:"https://"+i.host,formData:y,fileName:"file",name:"file",filePath:e,fileType:n};if(await this.uploadFileToOSS(Object.assign({},_,{onUploadProgress:o})),m)return{success:!0,filePath:e,fileID:c};if((await this.reportOSSUpload({id:g})).success)return{success:!0,filePath:e,fileID:c};throw new fe({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise(((t,n)=>{Array.isArray(e)&&0!==e.length||n(new fe({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map((e=>({fileID:e,tempFileURL:e})))})}))}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||0===e.length)throw new fe({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map((e=>e.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}}(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}};const Ce="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var Ne,Le;(Le=Ne||(Ne={})).local="local",Le.none="none",Le.session="session";var Oe=function(){},Ue=w((function(e,t){var n;e.exports=(n=S,function(e){var t=n,s=t.lib,o=s.WordArray,r=s.Hasher,a=t.algo,i=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),s=2;s<=n;s++)if(!(t%s))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var s=2,o=0;o<64;)t(s)&&(o<8&&(i[o]=n(e.pow(s,.5))),c[o]=n(e.pow(s,1/3)),o++),s++}();var l=[],u=a.SHA256=r.extend({_doReset:function(){this._hash=new o.init(i.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,s=n[0],o=n[1],r=n[2],a=n[3],i=n[4],u=n[5],d=n[6],h=n[7],p=0;p<64;p++){if(p<16)l[p]=0|e[t+p];else{var g=l[p-15],f=(g<<25|g>>>7)^(g<<14|g>>>18)^g>>>3,m=l[p-2],y=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[p]=f+l[p-7]+y+l[p-16]}var _=s&o^s&r^o&r,v=(s<<30|s>>>2)^(s<<19|s>>>13)^(s<<10|s>>>22),w=h+((i<<26|i>>>6)^(i<<21|i>>>11)^(i<<7|i>>>25))+(i&u^~i&d)+c[p]+l[p];h=d,d=u,u=i,i=a+w|0,a=r,r=o,o=s,s=w+(v+_)|0}n[0]=n[0]+s|0,n[1]=n[1]+o|0,n[2]=n[2]+r|0,n[3]=n[3]+a|0,n[4]=n[4]+i|0,n[5]=n[5]+u|0,n[6]=n[6]+d|0,n[7]=n[7]+h|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,o=8*t.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(o+64>>>9<<4)]=e.floor(s/4294967296),n[15+(o+64>>>9<<4)]=s,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=r._createHelper(u),t.HmacSHA256=r._createHmacHelper(u)}(Math),n.SHA256)})),De=Ue,Re=w((function(e,t){e.exports=S.HmacSHA256}));const Me=()=>{let e;if(!Promise){e=()=>{},e.promise={};const t=()=>{throw new fe({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}const t=new Promise(((t,n)=>{e=(e,s)=>e?n(e):t(s)}));return e.promise=t,e};function qe(e){return void 0===e}function Fe(e){return"[object Null]"===Object.prototype.toString.call(e)}function je(e=""){return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function Ve(e=32){let t="";for(let n=0;n<e;n++)t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(Math.floor(62*Math.random()));return t}var Be;!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Be||(Be={}));const $e={adapter:null,runtime:void 0},Ke=["anonymousUuidKey"];class ze extends Oe{constructor(){super(),$e.adapter.root.tcbObject||($e.adapter.root.tcbObject={})}setItem(e,t){$e.adapter.root.tcbObject[e]=t}getItem(e){return $e.adapter.root.tcbObject[e]}removeItem(e){delete $e.adapter.root.tcbObject[e]}clear(){delete $e.adapter.root.tcbObject}}function He(e,t){switch(e){case"local":return t.localStorage||new ze;case"none":return new ze;default:return t.sessionStorage||new ze}}class Je{constructor(e){if(!this._storage){this._persistence=$e.adapter.primaryStorage||e.persistence,this._storage=He(this._persistence,$e.adapter);const t=`access_token_${e.env}`,n=`access_token_expire_${e.env}`,s=`refresh_token_${e.env}`,o=`anonymous_uuid_${e.env}`,r=`login_type_${e.env}`,a="device_id",i=`token_type_${e.env}`,c=`user_info_${e.env}`;this.keys={accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s,anonymousUuidKey:o,loginTypeKey:r,userInfoKey:c,deviceIdKey:a,tokenTypeKey:i}}}updatePersistence(e){if(e===this._persistence)return;const t="local"===this._persistence;this._persistence=e;const n=He(e,$e.adapter);for(const s in this.keys){const e=this.keys[s];if(t&&Ke.includes(s))continue;const o=this._storage.getItem(e);qe(o)||Fe(o)||(n.setItem(e,o),this._storage.removeItem(e))}this._storage=n}setStore(e,t,n){if(!this._storage)return;const s={version:n||"localCachev1",content:t},o=JSON.stringify(s);try{this._storage.setItem(e,o)}catch(r){throw r}}getStore(e,t){try{if(!this._storage)return}catch(s){return""}t=t||"localCachev1";const n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}removeStore(e){this._storage.removeItem(e)}}const We={},Ge={};function Qe(e){return We[e]}class Ye{constructor(e,t){this.data=t||null,this.name=e}}class Xe extends Ye{constructor(e,t){super("error",{error:e,data:t}),this.error=e}}const Ze=new class{constructor(){this._listeners={}}on(e,t){return n=e,s=t,(o=this._listeners)[n]=o[n]||[],o[n].push(s),this;var n,s,o}off(e,t){return function(e,t,n){if(n&&n[e]){const s=n[e].indexOf(t);-1!==s&&n[e].splice(s,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof Xe)return console.error(e.error),this;const n="string"==typeof e?new Ye(e,t||{}):e,s=n.name;if(this._listens(s)){n.target=this;const e=this._listeners[s]?[...this._listeners[s]]:[];for(const t of e)t.call(this,n)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function et(e,t){Ze.on(e,t)}function tt(e,t={}){Ze.fire(e,t)}function nt(e,t){Ze.off(e,t)}const st="loginStateChanged",ot="loginStateExpire",rt="loginTypeChanged",at="anonymousConverted",it="refreshAccessToken";var ct;!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(ct||(ct={}));class lt{constructor(){this._fnPromiseMap=new Map}async run(e,t){let n=this._fnPromiseMap.get(e);return n||(n=new Promise((async(n,s)=>{try{await this._runIdlePromise();const s=t();n(await s)}catch(o){s(o)}finally{this._fnPromiseMap.delete(e)}})),this._fnPromiseMap.set(e,n)),n}_runIdlePromise(){return Promise.resolve()}}class ut{constructor(e){this._singlePromise=new lt,this._cache=Qe(e.env),this._baseURL=`https://${e.env}.ap-shanghai.tcb-api.tencentcloudapi.com`,this._reqClass=new $e.adapter.reqClass({timeout:e.timeout,timeoutMsg:`请求在${e.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]})}_getDeviceId(){if(this._deviceID)return this._deviceID;const{deviceIdKey:e}=this._cache.keys;let t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=Ve(),this._cache.setStore(e,t)),this._deviceID=t,t}async _request(e,t,n={}){const s={"x-request-id":Ve(),"x-device-id":this._getDeviceId()};if(n.withAccessToken){const{tokenTypeKey:e}=this._cache.keys,t=await this.getAccessToken(),n=this._cache.getStore(e);s.authorization=`${n} ${t}`}return this._reqClass["get"===n.method?"get":"post"]({url:`${this._baseURL}${e}`,data:t,headers:s})}async _fetchAccessToken(){const{loginTypeKey:e,accessTokenKey:t,accessTokenExpireKey:n,tokenTypeKey:s}=this._cache.keys,o=this._cache.getStore(e);if(o&&o!==ct.ANONYMOUS)throw new fe({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});const r=await this._singlePromise.run("fetchAccessToken",(async()=>(await this._request("/auth/v1/signin/anonymously",{},{method:"post"})).data)),{access_token:a,expires_in:i,token_type:c}=r;return this._cache.setStore(s,c),this._cache.setStore(t,a),this._cache.setStore(n,Date.now()+1e3*i),a}isAccessTokenExpired(e,t){let n=!0;return e&&t&&(n=t<Date.now()),n}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),s=this._cache.getStore(t);return this.isAccessTokenExpired(n,s)?this._fetchAccessToken():n}async refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,loginTypeKey:n}=this._cache.keys;return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.setStore(n,ct.ANONYMOUS),this.getAccessToken()}async getUserInfo(){return this._singlePromise.run("getUserInfo",(async()=>(await this._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"})).data))}}const dt=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],ht={"X-SDK-Version":"1.3.5"};function pt(e,t,n){const s=e[t];e[t]=function(t){const o={},r={};n.forEach((n=>{const{data:s,headers:a}=n.call(e,t);Object.assign(o,s),Object.assign(r,a)}));const a=t.data;return a&&(()=>{var e;if(e=a,"[object FormData]"!==Object.prototype.toString.call(e))t.data={...a,...o};else for(const t in o)a.append(t,o[t])})(),t.headers={...t.headers||{},...r},s.call(e,t)}}function gt(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...ht,"x-seqid":e}}}class ft{constructor(e={}){var t;this.config=e,this._reqClass=new $e.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=Qe(this.config.env),this._localCache=(t=this.config.env,Ge[t]),this.oauth=new ut(this.config),pt(this._reqClass,"post",[gt]),pt(this._reqClass,"upload",[gt]),pt(this._reqClass,"download",[gt])}async post(e){return await this._reqClass.post(e)}async upload(e){return await this._reqClass.upload(e)}async download(e){return await this._reqClass.download(e)}async refreshAccessToken(){let e,t;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{e=await this._refreshAccessTokenPromise}catch(n){t=n}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,t)throw t;return e}async _refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n,loginTypeKey:s,anonymousUuidKey:o}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(t);let r=this._cache.getStore(n);if(!r)throw new fe({message:"未登录CloudBase"});const a={refresh_token:r},i=await this.request("auth.fetchAccessTokenWithRefreshToken",a);if(i.data.code){const{code:e}=i.data;if("SIGN_PARAM_INVALID"===e||"REFRESH_TOKEN_EXPIRED"===e||"INVALID_REFRESH_TOKEN"===e){if(this._cache.getStore(s)===ct.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===e){const e=this._cache.getStore(o),t=this._cache.getStore(n),s=await this.send("auth.signInAnonymously",{anonymous_uuid:e,refresh_token:t});return this.setRefreshToken(s.refresh_token),this._refreshAccessToken()}tt(ot),this._cache.removeStore(n)}throw new fe({code:i.data.code,message:`刷新access token失败：${i.data.code}`})}if(i.data.access_token)return tt(it),this._cache.setStore(e,i.data.access_token),this._cache.setStore(t,i.data.access_token_expire+Date.now()),{accessToken:i.data.access_token,accessTokenExpire:i.data.access_token_expire};i.data.refresh_token&&(this._cache.removeStore(n),this._cache.setStore(n,i.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n}=this._cache.keys;if(!this._cache.getStore(n))throw new fe({message:"refresh token不存在，登录状态异常"});let s=this._cache.getStore(e),o=this._cache.getStore(t),r=!0;return this._shouldRefreshAccessTokenHook&&!(await this._shouldRefreshAccessTokenHook(s,o))&&(r=!1),(!s||!o||o<Date.now())&&r?this.refreshAccessToken():{accessToken:s,accessTokenExpire:o}}async request(e,t,n){const s=`x-tcb-trace_${this.config.env}`;let o="application/x-www-form-urlencoded";const r={action:e,env:this.config.env,dataVersion:"2019-08-16",...t};let a;if(-1===dt.indexOf(e)&&(this._cache.keys,r.access_token=await this.oauth.getAccessToken()),"storage.uploadFile"===e){a=new FormData;for(let e in a)a.hasOwnProperty(e)&&void 0!==a[e]&&a.append(e,r[e]);o="multipart/form-data"}else{o="application/json",a={};for(let e in r)void 0!==r[e]&&(a[e]=r[e])}let i={headers:{"content-type":o}};n&&n.timeout&&(i.timeout=n.timeout),n&&n.onUploadProgress&&(i.onUploadProgress=n.onUploadProgress);const c=this._localCache.getStore(s);c&&(i.headers["X-TCB-Trace"]=c);const{parse:l,inQuery:u,search:d}=t;let h={env:this.config.env};l&&(h.parse=!0),u&&(h={...u,...h});let p=function(e,t,n={}){const s=/\?/.test(t);let o="";for(let r in n)""===o?!s&&(t+="?"):o+="&",o+=`${r}=${encodeURIComponent(n[r])}`;return/^http(s)?\:\/\//.test(t+=o)?t:`${e}${t}`}(Ce,"//tcb-api.tencentcloudapi.com/web",h);d&&(p+=d);const g=await this.post({url:p,data:a,...i}),f=g.header&&g.header["x-tcb-trace"];if(f&&this._localCache.setStore(s,f),200!==Number(g.status)&&200!==Number(g.statusCode)||!g.data)throw new fe({code:"NETWORK_ERROR",message:"network request error"});return g}async send(e,t={},n={}){const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(("ACCESS_TOKEN_DISABLED"===s.data.code||"ACCESS_TOKEN_EXPIRED"===s.data.code)&&-1===dt.indexOf(e)){await this.oauth.refreshAccessToken();const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(s.data.code)throw new fe({code:s.data.code,message:je(s.data.message)});return s.data}if(s.data.code)throw new fe({code:s.data.code,message:je(s.data.message)});return s.data}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}}const mt={};function yt(e){return mt[e]}class _t{constructor(e){this.config=e,this._cache=Qe(e.env),this._request=yt(e.env)}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}setAccessToken(e,t){const{accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys;this._cache.setStore(n,e),this._cache.setStore(s,t)}async refreshUserInfo(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e)}}class vt{constructor(e){if(!e)throw new fe({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=e,this._cache=Qe(this._envId),this._request=yt(this._envId),this.setUserInfo()}linkWithTicket(e){if("string"!=typeof e)throw new fe({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}linkWithRedirect(e){e.signInWithRedirect()}updatePassword(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}updateEmail(e){return this._request.send("auth.updateEmail",{newEmail:e})}updateUsername(e){if("string"!=typeof e)throw new fe({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}async getLinkedUidList(){const{data:e}=await this._request.send("auth.getLinkedUidList",{});let t=!1;const{users:n}=e;return n.forEach((e=>{e.wxOpenId&&e.wxPublicId&&(t=!0)})),{users:n,hasPrimaryUid:t}}setPrimaryUid(e){return this._request.send("auth.setPrimaryUid",{uid:e})}unlink(e){return this._request.send("auth.unlink",{platform:e})}async update(e){const{nickName:t,gender:n,avatarUrl:s,province:o,country:r,city:a}=e,{data:i}=await this._request.send("auth.updateUserInfo",{nickName:t,gender:n,avatarUrl:s,province:o,country:r,city:a});this.setLocalUserInfo(i)}async refresh(){const e=await this._request.oauth.getUserInfo();return this.setLocalUserInfo(e),e}setUserInfo(){const{userInfoKey:e}=this._cache.keys,t=this._cache.getStore(e);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((e=>{this[e]=t[e]})),this.location={country:t.country,province:t.province,city:t.city}}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e),this.setUserInfo()}}class wt{constructor(e){if(!e)throw new fe({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Qe(e);const{refreshTokenKey:t,accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys,o=this._cache.getStore(t),r=this._cache.getStore(n),a=this._cache.getStore(s);this.credential={refreshToken:o,accessToken:r,accessTokenExpire:a},this.user=new vt(e)}get isAnonymousAuth(){return this.loginType===ct.ANONYMOUS}get isCustomAuth(){return this.loginType===ct.CUSTOM}get isWeixinAuth(){return this.loginType===ct.WECHAT||this.loginType===ct.WECHAT_OPEN||this.loginType===ct.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class kt extends _t{async signIn(){this._cache.updatePersistence("local"),await this._request.oauth.getAccessToken(),tt(st),tt(rt,{env:this.config.env,loginType:ct.ANONYMOUS,persistence:"local"});const e=new wt(this.config.env);return await e.user.refresh(),e}async linkAndRetrieveDataWithTicket(e){const{anonymousUuidKey:t,refreshTokenKey:n}=this._cache.keys,s=this._cache.getStore(t),o=this._cache.getStore(n),r=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:s,refresh_token:o,ticket:e});if(r.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(r.refresh_token),await this._request.refreshAccessToken(),tt(at,{env:this.config.env}),tt(rt,{loginType:ct.CUSTOM,persistence:"local"}),{credential:{refreshToken:r.refresh_token}};throw new fe({message:"匿名转化失败"})}_setAnonymousUUID(e){const{anonymousUuidKey:t,loginTypeKey:n}=this._cache.keys;this._cache.removeStore(t),this._cache.setStore(t,e),this._cache.setStore(n,ct.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class St extends _t{async signIn(e){if("string"!=typeof e)throw new fe({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:t}=this._cache.keys,n=await this._request.send("auth.signInWithTicket",{ticket:e,refresh_token:this._cache.getStore(t)||""});if(n.refresh_token)return this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),tt(st),tt(rt,{env:this.config.env,loginType:ct.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new wt(this.config.env);throw new fe({message:"自定义登录失败"})}}class bt extends _t{async signIn(e,t){if("string"!=typeof e)throw new fe({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:"EMAIL",email:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token:r,access_token_expire:a}=s;if(o)return this.setRefreshToken(o),r&&a?this.setAccessToken(r,a):await this._request.refreshAccessToken(),await this.refreshUserInfo(),tt(st),tt(rt,{env:this.config.env,loginType:ct.EMAIL,persistence:this.config.persistence}),new wt(this.config.env);throw s.code?new fe({code:s.code,message:`邮箱登录失败: ${s.message}`}):new fe({message:"邮箱登录失败"})}async activate(e){return this._request.send("auth.activateEndUserMail",{token:e})}async resetPasswordWithToken(e,t){return this._request.send("auth.resetPasswordWithToken",{token:e,newPassword:t})}}class Tt extends _t{async signIn(e,t){if("string"!=typeof e)throw new fe({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof t&&(t="",console.warn("password is empty"));const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:ct.USERNAME,username:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token_expire:r,access_token:a}=s;if(o)return this.setRefreshToken(o),a&&r?this.setAccessToken(a,r):await this._request.refreshAccessToken(),await this.refreshUserInfo(),tt(st),tt(rt,{env:this.config.env,loginType:ct.USERNAME,persistence:this.config.persistence}),new wt(this.config.env);throw s.code?new fe({code:s.code,message:`用户名密码登录失败: ${s.message}`}):new fe({message:"用户名密码登录失败"})}}class It{constructor(e){this.config=e,this._cache=Qe(e.env),this._request=yt(e.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),et(rt,this._onLoginTypeChanged)}get currentUser(){const e=this.hasLoginState();return e&&e.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new kt(this.config)}customAuthProvider(){return new St(this.config)}emailAuthProvider(){return new bt(this.config)}usernameAuthProvider(){return new Tt(this.config)}async signInAnonymously(){return new kt(this.config).signIn()}async signInWithEmailAndPassword(e,t){return new bt(this.config).signIn(e,t)}signInWithUsernameAndPassword(e,t){return new Tt(this.config).signIn(e,t)}async linkAndRetrieveDataWithTicket(e){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new kt(this.config)),et(at,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(e)}async signOut(){if(this.loginType===ct.ANONYMOUS)throw new fe({message:"匿名用户不支持登出操作"});const{refreshTokenKey:e,accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,s=this._cache.getStore(e);if(!s)return;const o=await this._request.send("auth.logout",{refresh_token:s});return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.removeStore(n),tt(st),tt(rt,{env:this.config.env,loginType:ct.NULL,persistence:this.config.persistence}),o}async signUpWithEmailAndPassword(e,t){return this._request.send("auth.signUpWithEmailAndPassword",{email:e,password:t})}async sendPasswordResetEmail(e){return this._request.send("auth.sendPasswordResetEmail",{email:e})}onLoginStateChanged(e){et(st,(()=>{const t=this.hasLoginState();e.call(this,t)}));const t=this.hasLoginState();e.call(this,t)}onLoginStateExpired(e){et(ot,e.bind(this))}onAccessTokenRefreshed(e){et(it,e.bind(this))}onAnonymousConverted(e){et(at,e.bind(this))}onLoginTypeChanged(e){et(rt,(()=>{const t=this.hasLoginState();e.call(this,t)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),s=this._cache.getStore(t);return this._request.oauth.isAccessTokenExpired(n,s)?null:new wt(this.config.env)}async isUsernameRegistered(e){if("string"!=typeof e)throw new fe({code:"PARAM_ERROR",message:"username must be a string"});const{data:t}=await this._request.send("auth.isUsernameRegistered",{username:e});return t&&t.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(e){return new St(this.config).signIn(e)}shouldRefreshAccessToken(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((e=>e.code?e:{...e.data,requestId:e.seqId}))}getAuthHeader(){const{refreshTokenKey:e,accessTokenKey:t}=this._cache.keys,n=this._cache.getStore(e);return{"x-cloudbase-credentials":this._cache.getStore(t)+"/@@/"+n}}_onAnonymousConverted(e){const{env:t}=e.data;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(e){const{loginType:t,persistence:n,env:s}=e.data;s===this.config.env&&(this._cache.updatePersistence(n),this._cache.setStore(this._cache.keys.loginTypeKey,t))}}const At=function(e,t){t=t||Me();const n=yt(this.config.env),{cloudPath:s,filePath:o,onUploadProgress:r,fileType:a="image"}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{const{data:{url:i,authorization:c,token:l,fileId:u,cosFileId:d},requestId:h}=e,p={key:s,signature:c,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":l};n.upload({url:i,data:p,file:o,name:s,fileType:a,onUploadProgress:r}).then((e=>{201===e.statusCode?t(null,{fileID:u,requestId:h}):t(new fe({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${e.data}`}))})).catch((e=>{t(e)}))})).catch((e=>{t(e)})),t.promise},Pt=function(e,t){t=t||Me();const n=yt(this.config.env),{cloudPath:s}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{t(null,e)})).catch((e=>{t(e)})),t.promise},Et=function({fileList:e},t){if(t=t||Me(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let s of e)if(!s||"string"!=typeof s)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const n={fileid_list:e};return yt(this.config.env).send("storage.batchDeleteFile",n).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},xt=function({fileList:e},t){t=t||Me(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let n=[];for(let o of e)"object"==typeof o?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),n.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?n.push({fileid:o}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const s={file_list:n};return yt(this.config.env).send("storage.batchGetDownloadUrl",s).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},Ct=async function({fileID:e},t){const n=(await xt.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if("SUCCESS"!==n.code)return t?t(n):new Promise((e=>{e(n)}));const s=yt(this.config.env);let o=n.download_url;if(o=encodeURI(o),!t)return s.download({url:o});t(await s.download({url:o}))},Nt=function({name:e,data:t,query:n,parse:s,search:o,timeout:r},a){const i=a||Me();let c;try{c=t?JSON.stringify(t):""}catch(u){return Promise.reject(u)}if(!e)return Promise.reject(new fe({code:"PARAM_ERROR",message:"函数名不能为空"}));const l={inQuery:n,parse:s,search:o,function_name:e,request_data:c};return yt(this.config.env).send("functions.invokeFunction",l,{timeout:r}).then((e=>{if(e.code)i(null,e);else{let n=e.data.response_data;if(s)i(null,{result:n,requestId:e.requestId});else try{n=JSON.parse(e.data.response_data),i(null,{result:n,requestId:e.requestId})}catch(t){i(new fe({message:"response data must be json"}))}}return i.promise})).catch((e=>{i(e)})),i.promise},Lt={timeout:15e3,persistence:"session"},Ot=6e5,Ut={};class Dt{constructor(e){this.config=e||this.config,this.authObj=void 0}init(e){switch($e.adapter||(this.requestClient=new $e.adapter.reqClass({timeout:e.timeout||5e3,timeoutMsg:`请求在${(e.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...Lt,...e},!0){case this.config.timeout>Ot:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=Ot;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new Dt(this.config)}auth({persistence:e}={}){if(this.authObj)return this.authObj;const t=e||$e.adapter.primaryStorage||Lt.persistence;var n;return t!==this.config.persistence&&(this.config.persistence=t),function(e){const{env:t}=e;We[t]=new Je(e),Ge[t]=new Je({...e,persistence:"local"})}(this.config),n=this.config,mt[n.env]=new ft(n),this.authObj=new It(this.config),this.authObj}on(e,t){return et.apply(this,[e,t])}off(e,t){return nt.apply(this,[e,t])}callFunction(e,t){return Nt.apply(this,[e,t])}deleteFile(e,t){return Et.apply(this,[e,t])}getTempFileURL(e,t){return xt.apply(this,[e,t])}downloadFile(e,t){return Ct.apply(this,[e,t])}uploadFile(e,t){return At.apply(this,[e,t])}getUploadMetadata(e,t){return Pt.apply(this,[e,t])}registerExtension(e){Ut[e.name]=e}async invokeExtension(e,t){const n=Ut[e];if(!n)throw new fe({message:`扩展${e} 必须先注册`});return await n.invoke(t,this)}useAdapters(e){const{adapter:t,runtime:n}=function(e){const t=(n=e,"[object Array]"===Object.prototype.toString.call(n)?e:[e]);var n;for(const s of t){const{isMatch:e,genAdapter:t,runtime:n}=s;if(e())return{adapter:t(),runtime:n}}}(e)||{};t&&($e.adapter=t),n&&($e.runtime=n)}}var Rt=new Dt;function Mt(e,t,n){void 0===n&&(n={});var s=/\?/.test(t),o="";for(var r in n)""===o?!s&&(t+="?"):o+="&",o+=r+"="+encodeURIComponent(n[r]);return/^http(s)?:\/\//.test(t+=o)?t:""+e+t}class qt{get(e){const{url:t,data:n,headers:s,timeout:o}=e;return new Promise(((e,r)=>{me.request({url:Mt("https:",t),data:n,method:"GET",header:s,timeout:o,success(t){e(t)},fail(e){r(e)}})}))}post(e){const{url:t,data:n,headers:s,timeout:o}=e;return new Promise(((e,r)=>{me.request({url:Mt("https:",t),data:n,method:"POST",header:s,timeout:o,success(t){e(t)},fail(e){r(e)}})}))}upload(e){return new Promise(((t,n)=>{const{url:s,file:o,data:r,headers:a,fileType:i}=e,c=me.uploadFile({url:Mt("https:",s),name:"file",formData:Object.assign({},r),filePath:o,fileType:i,header:a,success(e){const n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&r.success_action_status&&(n.statusCode=parseInt(r.success_action_status,10)),t(n)},fail(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((t=>{e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}const Ft={setItem(e,t){me.setStorageSync(e,t)},getItem:e=>me.getStorageSync(e),removeItem(e){me.removeStorageSync(e)},clear(){me.clearStorageSync()}};var jt={genAdapter:function(){return{root:{},reqClass:qt,localStorage:Ft,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Rt.useAdapters(jt);const Vt=Rt,Bt=Vt.init;Vt.init=function(e){e.env=e.spaceId;const t=Bt.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const n=t.auth;return t.auth=function(e){const t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((e=>{var n;t[e]=(n=t[e],function(e){e=e||{};const{success:t,fail:s,complete:o}=ge(e);if(!(t||s||o))return n.call(this,e);n.call(this,e).then((e=>{t&&t(e),o&&o(e)}),(e=>{s&&s(e),o&&o(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var $t=Vt;async function Kt(e,t){const n=`http://${e}:${t}/system/ping`;try{const e=await(s={url:n,timeout:500},new Promise(((e,t)=>{me.request({...s,success(t){e(t)},fail(e){t(e)}})})));return!(!e.data||0!==e.data.code)}catch(o){return!1}var s}const zt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"};var Ht={init(e){const t=new class{constructor(e){if(["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),!e.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},e),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=me}async request(e,t=!0){return e=this.setupRequest(e),Promise.resolve().then((()=>Pe(e,this.adapter.request)))}requestLocal(e){return new Promise(((t,n)=>{this.adapter.request(Object.assign(e,{complete(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){const t=e.data&&e.data.code||"SYS_ERR",s=e.data&&e.data.message||"request:fail";return n(new fe({code:t,message:s}))}t({success:!0,result:e.data})}}))}))}setupRequest(e){const t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=Ae(t,this.config.clientSecret);const s=Ie();n["x-client-info"]=encodeURIComponent(JSON.stringify(s));const{token:o}=_e();return n["x-client-token"]=o,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}async setupLocalRequest(e){const t=Ie(),{token:n}=_e(),s=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:t,token:n}),{address:o,servePort:r}=this.__dev__&&this.__dev__.debugInfo||{},{address:a}=await async function(e,t){let n;for(let s=0;s<e.length;s++){const o=e[s];if(await Kt(o,t)){n=o;break}}return{address:n,port:t}}(o,r);return{url:`http://${a}:${r}/${zt[e.method]}`,method:"POST",data:s,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))}}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}getUploadFileOptions(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}reportUploadFile(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}uploadFile({filePath:e,cloudPath:t,fileType:n="image",onUploadProgress:s}){if(!t)throw new fe({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let o;return this.getUploadFileOptions({cloudPath:t}).then((t=>{const{url:r,formData:a,name:i}=t.result;return o=t.result.fileUrl,new Promise(((t,o)=>{const c=this.adapter.uploadFile({url:r,formData:a,name:i,filePath:e,fileType:n,success(e){e&&e.statusCode<400?t(e):o(new fe({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){o(new fe({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((()=>this.reportUploadFile({cloudPath:t}))).then((t=>new Promise(((n,s)=>{t.success?n({success:!0,filePath:e,fileID:o}):s(new fe({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(t).then((e=>{if(e.success)return e.result;throw new fe({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||0===e.length)throw new fe({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(n).then((e=>{if(e.success)return{fileList:e.result.fileList.map((e=>({fileID:e.fileID,tempFileURL:e.tempFileURL})))};throw new fe({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Jt=w((function(e,t){e.exports=S.enc.Hex}));function Wt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Gt(e="",t={}){const{data:n,functionName:s,method:o,headers:r,signHeaderKeys:a=[],config:i}=t,c=String(Date.now()),l=Wt(),u=Object.assign({},r,{"x-from-app-id":i.spaceAppId,"x-from-env-id":i.spaceId,"x-to-env-id":i.spaceId,"x-from-instance-id":c,"x-from-function-name":s,"x-client-timestamp":c,"x-alipay-source":"client","x-request-id":l,"x-alipay-callid":l,"x-trace-id":l}),d=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(a),[h="",p=""]=e.split("?")||[],g=function(e){const t="HMAC-SHA256",n=e.signedHeaders.join(";"),s=e.signedHeaders.map((t=>`${t.toLowerCase()}:${e.headers[t]}\n`)).join(""),o=De(e.body).toString(Jt),r=`${e.method.toUpperCase()}\n${e.path}\n${e.query}\n${s}\n${n}\n${o}\n`,a=De(r).toString(Jt),i=`${t}\n${e.timestamp}\n${a}\n`,c=Re(i,e.secretKey).toString(Jt);return`${t} Credential=${e.secretId}, SignedHeaders=${n}, Signature=${c}`}({path:h,query:p,method:o,headers:u,timestamp:c,body:JSON.stringify(n),secretId:i.accessKey,secretKey:i.secretKey,signedHeaders:d.sort()});return{url:`${i.endpoint}${e}`,headers:Object.assign({},u,{Authorization:g})}}function Qt({url:e,data:t,method:n="POST",headers:s={},timeout:o}){return new Promise(((r,a)=>{me.request({url:e,method:n,data:"object"==typeof t?JSON.stringify(t):t,header:s,dataType:"json",timeout:o,complete:(e={})=>{const t=s["x-trace-id"]||"";if(!e.statusCode||e.statusCode>=400){const{message:n,errMsg:s,trace_id:o}=e.data||{};return a(new fe({code:"SYS_ERR",message:n||s||"request:fail",requestId:o||t}))}r({status:e.statusCode,data:e.data,headers:e.header,requestId:t})}})}))}function Yt(e,t){const{path:n,data:s,method:o="GET"}=e,{url:r,headers:a}=Gt(n,{functionName:"",data:s,method:o,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return Qt({url:r,data:s,method:o,headers:a}).then((e=>{const t=e.data||{};if(!t.success)throw new fe({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((e=>{throw new fe({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Xt(e=""){const t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new fe({code:"INVALID_PARAM",message:"fileID不合法"});const s=t.substring(0,n),o=t.substring(n+1);return s!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),o}function Zt(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}class en{constructor(e){this.config=e}signedURL(e,t={}){const n=`/ws/function/${e}`,s=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),o=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Wt(),timestamp:""+Date.now()}),r=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return o[e]?"".concat(e,"=").concat(o[e]):null})).filter(Boolean).join("&"),`host:${s}`].join("\n"),a=["HMAC-SHA256",De(r).toString(Jt)].join("\n"),i=Re(a,this.config.secretKey).toString(Jt),c=Object.keys(o).map((e=>`${e}=${encodeURIComponent(o[e])}`)).join("&");return`${this.config.wsEndpoint}${n}?${c}&signature=${i}`}}var tn={init:e=>{e.provider="alipay";const t=new class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),e.endpoint){if("string"!=typeof e.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`,wsEndpoint:e.wsEndpoint||`wss://${e.spaceId}.api-hz.cloudbasefunction.cn`}),this._websocket=new en(this.config)}callFunction(e){return function(e,t){const{name:n,data:s,async:o=!1,timeout:r}=e,a="POST",i={"x-to-function-name":n};o&&(i["x-function-invoke-type"]="async");const{url:c,headers:l}=Gt("/functions/invokeFunction",{functionName:n,data:s,method:a,headers:i,signHeaderKeys:["x-to-function-name"],config:t});return Qt({url:c,data:s,method:a,headers:l,timeout:r}).then((e=>{let t=0;if(o){const n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new fe({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((e=>{throw new fe({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:n,formData:s,onUploadProgress:o}){return new Promise(((r,a)=>{const i=me.uploadFile({url:e,filePath:t,fileType:n,formData:s,name:"file",success(e){e&&e.statusCode<400?r(e):a(new fe({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){a(new fe({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&i&&"function"==typeof i.onProgressUpdate&&i.onProgressUpdate((e=>{o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:e,cloudPath:t="",fileType:n="image",onUploadProgress:s}){if("string"!==O(t))throw new fe({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new fe({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new fe({code:"INVALID_PARAM",message:"cloudPath不合法"});const o=await Yt({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:r,upload_url:a,form_data:i}=o,c=i&&i.reduce(((e,t)=>(e[t.key]=t.value,e)),{});return this.uploadFileToOSS({url:a,filePath:e,fileType:n,formData:c,onUploadProgress:s}).then((()=>({fileID:r})))}async getTempFileURL({fileList:e}){return new Promise(((t,n)=>{(!e||e.length<0)&&t({code:"INVALID_PARAM",message:"fileList不能为空数组"}),e.length>50&&t({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});const s=[];for(const r of e){let e;"string"!==O(r)&&t({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{e=Xt.call(this,r)}catch(o){console.warn(o.errCode,o.errMsg),e=r}s.push({file_id:e,expire:600})}Yt({path:"/?download_url",data:{file_list:s},method:"POST"},this.config).then((e=>{const{file_list:n=[]}=e;t({fileList:n.map((e=>({fileID:Zt.call(this,e.file_id),tempFileURL:e.download_url})))})})).catch((e=>n(e)))}))}async connectWebSocket(e){const{name:t,query:n}=e;return me.connectSocket({url:this._websocket.signedURL(t,n),complete:()=>{}})}}(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function nn({data:e}){let t;t=Ie();const n=JSON.parse(JSON.stringify(e||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){const{token:e}=_e();e&&(n.uniIdToken=e)}return n}const sn=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var on=/[\\^$.*+?()[\]{}|]/g,rn=RegExp(on.source);function an(e,t,n){return e.replace(new RegExp((s=t)&&rn.test(s)?s.replace(on,"\\$&"):s,"g"),n);var s}const cn="request",ln="response",un="both",dn="_globalUniCloudStatus",hn={code:2e4,message:"System error"},pn={code:20101,message:"Invalid client"};function gn(e){const{errSubject:t,subject:n,errCode:s,errMsg:o,code:r,message:a,cause:i}=e||{};return new fe({subject:t||n||"uni-secure-network",code:s||r||hn.code,message:o||a,cause:i})}let fn;function mn({secretType:e}={}){return e===cn||e===ln||e===un}function yn({name:e,data:t={}}={}){return"DCloud-clientDB"===e&&"encryption"===t.redirectTo&&"getAppClientKey"===t.action}function _n({functionName:e,result:t,logPvd:n}){}function vn(e){const t=e.callFunction,n=function(n){const s=n.name;n.data=nn.call(e,{data:n.data});const o={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],r=mn(n),a=yn(n),i=r||a;return t.call(this,n).then((e=>(e.errCode=0,!i&&_n.call(this,{functionName:s,result:e,logPvd:o}),Promise.resolve(e))),(e=>(!i&&_n.call(this,{functionName:s,result:e,logPvd:o}),e&&e.message&&(e.message=function({message:e="",extraInfo:t={},formatter:n=[]}={}){for(let s=0;s<n.length;s++){const{rule:o,content:r,mode:a}=n[s],i=e.match(o);if(!i)continue;let c=r;for(let e=1;e<i.length;e++)c=an(c,`{$${e}}`,i[e]);for(const e in t)c=an(c,`{${e}}`,t[e]);return"replace"===a?c:e+c}return e}({message:`[${n.name}]: ${e.message}`,formatter:sn,extraInfo:{functionName:s}})),Promise.reject(e))))};e.callFunction=function(t){const{provider:s,spaceId:o}=e.config,r=t.name;let a,i;return t.data=t.data||{},a=n,a=a.bind(e),i=yn(t)?n.call(e,t):mn(t)?new fn({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function({provider:e,spaceId:t,functionName:n}={}){const{appId:s,uniPlatform:o,osName:r}=Se();let a=o;"app"===o&&(a=r);const i=function({provider:e,spaceId:t}={}){const n=V;if(!n)return{};e=function(e){return"tencent"===e?"tcb":e}(e);const s=n.find((n=>n.provider===e&&n.spaceId===t));return s&&s.config}({provider:e,spaceId:t});if(!i||!i.accessControl||!i.accessControl.enable)return!1;const c=i.accessControl.function||{},l=Object.keys(c);if(0===l.length)return!0;const u=function(e,t){let n,s,o;for(let r=0;r<e.length;r++){const a=e[r];a!==t?"*"!==a?a.split(",").map((e=>e.trim())).indexOf(t)>-1&&(s=a):o=a:n=a}return n||s||o}(l,n);if(!u)return!1;if((c[u]||[]).find(((e={})=>e.appId===s&&(e.platform||"").toLowerCase()===a.toLowerCase())))return!0;throw console.error(`此应用[appId: ${s}, platform: ${a}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),gn(pn)}({provider:s,spaceId:o,functionName:r})?new fn({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):a(t),Object.defineProperty(i,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),i.then((e=>("undefined"!=typeof UTSJSONObject&&(e.result=new UTSJSONObject(e.result)),e)))}}fn=class{constructor(){throw gn({message:"Platform app is not enabled, please check whether secure network module is enabled in your manifest.json"})}};const wn=Symbol("CLIENT_DB_INTERNAL");function kn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=wn,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(e,n,s){if("_uniClient"===n)return null;if("symbol"==typeof n)return e[n];if(n in e||"string"!=typeof n){const t=e[n];return"function"==typeof t?t.bind(e):t}return t.get(e,n,s)}})}function Sn(e){return{on:(t,n)=>{e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:(t,n)=>{e[t]=e[t]||[];const s=e[t].indexOf(n);-1!==s&&e[t].splice(s,1)}}}const bn=["db.Geo","db.command","command.aggregate"];function Tn(e,t){return bn.indexOf(`${e}.${t}`)>-1}function In(e){switch(O(e=ye(e))){case"array":return e.map((e=>In(e)));case"object":return e._internalType===wn||Object.keys(e).forEach((t=>{e[t]=In(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function An(e){return e&&e.content&&e.content.$method}class Pn{constructor(e,t,n){this.content=e,this.prevStage=t||null,this.udb=null,this._database=n}toJSON(){let e=this;const t=[e.content];for(;e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((e=>({$method:e.$method,$param:In(e.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const e=this.toJSON().$db.find((e=>"action"===e.$method));return e&&e.$param&&e.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((e=>"action"!==e.$method))}}get isAggregate(){let e=this;for(;e;){const t=An(e),n=An(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}get isCommand(){let e=this;for(;e;){if("command"===An(e))return!0;e=e.prevStage}return!1}get isAggregateCommand(){let e=this;for(;e;){const t=An(e),n=An(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}getNextStageFn(e){const t=this;return function(){return En({$method:e,$param:In(Array.from(arguments))},t,t._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(e,t){const n=this.getAction(),s=this.getCommand();return s.$db.push({$method:e,$param:In(t)}),this._database._callCloudFunction({action:n,command:s})}}function En(e,t,n){return kn(new Pn(e,t,n),{get(e,t){let s="db";return e&&e.content&&(s=e.content.$method),Tn(s,t)?En({$method:t},e,n):function(){return En({$method:t,$param:In(Array.from(arguments))},e,n)}}})}function xn({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map((e=>({$method:e}))),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function Cn(e,t={}){return kn(new e(t),{get:(e,t)=>Tn("db",t)?En({$method:t},null,e):function(){return En({$method:t,$param:In(Array.from(arguments))},null,e)}})}class Nn extends class{constructor({uniClient:e={},isJQL:t=!1}={}){this._uniClient=e,this._authCallBacks={},this._dbCallBacks={},e._isDefault&&(this._dbCallBacks=H("_globalUniCloudDatabaseCallback")),t||(this.auth=Sn(this._authCallBacks)),this._isJQL=t,Object.assign(this,Sn(this._dbCallBacks)),this.env=kn({},{get:(e,t)=>({$env:t})}),this.Geo=kn({},{get:(e,t)=>xn({path:["Geo"],method:t})}),this.serverDate=xn({path:[],method:"serverDate"}),this.RegExp=xn({path:[],method:"RegExp"})}getCloudEnv(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}_callback(e,t){const n=this._dbCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}_callbackAuth(e,t){const n=this._authCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}multiSend(){const e=Array.from(arguments),t=e.map((e=>{const t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}{_parseResult(e){return this._isJQL?e.result:e}_callCloudFunction({action:e,command:t,multiCommand:n,queryList:s}){function o(e,t){if(n&&s)for(let n=0;n<s.length;n++){const o=s[n];o.udb&&"function"==typeof o.udb.setResult&&(t?o.udb.setResult(t):o.udb.setResult(e.result.dataList[n]))}}const r=this,a=this._isJQL?"databaseForJQL":"database";function i(e){return r._callback("error",[e]),Y(X(a,"fail"),e).then((()=>Y(X(a,"complete"),e))).then((()=>(o(null,e),ue(te,{type:oe,content:e}),Promise.reject(e))))}const c=Y(X(a,"invoke")),l=this._uniClient;return c.then((()=>l.callFunction({name:"DCloud-clientDB",type:C,data:{action:e,command:t,multiCommand:n}}))).then((e=>{const{code:t,message:n,token:s,tokenExpired:c,systemInfo:l=[]}=e.result;if(l)for(let o=0;o<l.length;o++){const{level:e,message:t,detail:n}=l[o];let s="[System Info]"+t;n&&(s=`${s}\n详细信息：${n}`),(console["warn"===e?"error":e]||console.log)(s)}if(t)return i(new fe({code:t,message:n,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,s&&c&&(ve({token:s,tokenExpired:c}),this._callbackAuth("refreshToken",[{token:s,tokenExpired:c}]),this._callback("refreshToken",[{token:s,tokenExpired:c}]),ue(se,{token:s,tokenExpired:c}));const u=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let o=0;o<u.length;o++){const{prop:t,tips:n}=u[o];if(t in e.result){const s=e.result[t];Object.defineProperty(e.result,t,{get:()=>(console.warn(n),s)})}}return d=e,Y(X(a,"success"),d).then((()=>Y(X(a,"complete"),d))).then((()=>{o(d,null);const e=r._parseResult(d);return ue(te,{type:oe,content:e}),Promise.resolve(e)}));var d}),(e=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),i(new fe({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId})))))}}const Ln="token无效，跳转登录页面",On="token过期，跳转登录页面",Un={TOKEN_INVALID_TOKEN_EXPIRED:On,TOKEN_INVALID_INVALID_CLIENTID:Ln,TOKEN_INVALID:Ln,TOKEN_INVALID_WRONG_TOKEN:Ln,TOKEN_INVALID_ANONYMOUS_USER:Ln},Dn={"uni-id-token-expired":On,"uni-id-check-token-failed":Ln,"uni-id-token-not-exist":Ln,"uni-id-check-device-feature-failed":Ln};function Rn(e,t){let n="";return n=e?`${e}/${t}`:t,n.replace(/^\//,"")}function Mn(e=[],t=""){const n=[],s=[];return e.forEach((e=>{!0===e.needLogin?n.push(Rn(t,e.path)):!1===e.needLogin&&s.push(Rn(t,e.path))})),{needLoginPage:n,notNeedLoginPage:s}}function qn(e){return e.split("?")[0].replace(/^\//,"")}function Fn(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){const e=getCurrentPages();return e[e.length-1]}())}function jn(){return qn(Fn())}function Vn(e="",t={}){if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;const n=t.list,s=qn(e);return n.some((e=>e.pagePath===s))}const Bn=!!v.uniIdRouter,{loginPage:$n,routerNeedLogin:Kn,resToLogin:zn,needLoginPage:Hn,notNeedLoginPage:Jn,loginPageInTabBar:Wn}=function({pages:e=[],subPackages:t=[],uniIdRouter:n={},tabBar:s={}}=v){const{loginPage:o,needLogin:r=[],resToLogin:a=!0}=n,{needLoginPage:i,notNeedLoginPage:c}=Mn(e),{needLoginPage:l,notNeedLoginPage:u}=function(e=[]){const t=[],n=[];return e.forEach((e=>{const{root:s,pages:o=[]}=e,{needLoginPage:r,notNeedLoginPage:a}=Mn(o,s);t.push(...r),n.push(...a)})),{needLoginPage:t,notNeedLoginPage:n}}(t);return{loginPage:o,routerNeedLogin:r,resToLogin:a,needLoginPage:[...i,...l],notNeedLoginPage:[...c,...u],loginPageInTabBar:Vn(o,s)}}();if(Hn.indexOf($n)>-1)throw new Error(`Login page [${$n}] should not be "needLogin", please check your pages.json`);function Gn(e){const t=jn();if("/"===e.charAt(0))return e;const[n,s]=e.split("?"),o=n.replace(/^\//,"").split("/"),r=t.split("/");r.pop();for(let a=0;a<o.length;a++){const e=o[a];".."===e?r.pop():"."!==e&&r.push(e)}return""===r[0]&&r.shift(),"/"+r.join("/")+(s?"?"+s:"")}function Qn({redirect:e}){const t=qn(e),n=qn($n);return jn()!==n&&t!==n}function Yn({api:e,redirect:t}={}){if(!t||!Qn({redirect:t}))return;const n=(o=t,"/"!==(s=$n).charAt(0)&&(s="/"+s),o?s.indexOf("?")>-1?s+`&uniIdRedirectUrl=${encodeURIComponent(o)}`:s+`?uniIdRedirectUrl=${encodeURIComponent(o)}`:s);var s,o;Wn?"navigateTo"!==e&&"redirectTo"!==e||(e="switchTab"):"switchTab"===e&&(e="navigateTo");const r={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((()=>{r[e]({url:n})}),0)}function Xn({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){const{token:e,tokenExpired:t}=_e();let n;if(e){if(t<Date.now()){const e="uni-id-token-expired";n={errCode:e,errMsg:Dn[e]}}}else{const e="uni-id-check-token-failed";n={errCode:e,errMsg:Dn[e]}}return n}();if(function(e){const t=qn(Gn(e));return!(Jn.indexOf(t)>-1)&&(Hn.indexOf(t)>-1||Kn.some((t=>{return n=e,new RegExp(t).test(n);var n})))}(e)&&n){if(n.uniIdRedirectUrl=e,ie(ne).length>0)return setTimeout((()=>{ue(ne,n)}),0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function Zn(){!function(){const e=Fn(),{abortLoginPageJump:t,autoToLoginPage:n}=Xn({url:e});t||n&&Yn({api:"redirectTo",redirect:e})}();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const n=e[t];uni.addInterceptor(n,{invoke(e){const{abortLoginPageJump:t,autoToLoginPage:s}=Xn({url:e.url});return t?e:s?(Yn({api:n,redirect:Gn(e.url)}),!1):e}})}}function es(){this.onResponse((e=>{const{type:t,content:n}=e;let s=!1;switch(t){case"cloudobject":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in Dn}(n);break;case"clientdb":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in Un}(n)}s&&function(e={}){const t=ie(ne);pe().then((()=>{const n=Fn();if(n&&Qn({redirect:n}))return t.length>0?ue(ne,Object.assign({uniIdRedirectUrl:n},e)):void($n&&Yn({api:"navigateTo",redirect:n}))}))}(n)}))}function ts(e){var t;(t=e).onResponse=function(e){ce(te,e)},t.offResponse=function(e){le(te,e)},function(e){e.onNeedLogin=function(e){ce(ne,e)},e.offNeedLogin=function(e){le(ne,e)},Bn&&(H(dn).needLoginInit||(H(dn).needLoginInit=!0,pe().then((()=>{Zn.call(e)})),zn&&es.call(e)))}(e),function(e){e.onRefreshToken=function(e){ce(se,e)},e.offRefreshToken=function(e){le(se,e)}}(e)}let ns;const ss="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",os=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function rs(){const e=_e().token||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((s=t[1],decodeURIComponent(ns(s).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}var s;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}ns="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!os.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,s,o="",r=0;r<e.length;)t=ss.indexOf(e.charAt(r++))<<18|ss.indexOf(e.charAt(r++))<<12|(n=ss.indexOf(e.charAt(r++)))<<6|(s=ss.indexOf(e.charAt(r++))),o+=64===n?String.fromCharCode(t>>16&255):64===s?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}:atob;var as=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(w((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const n="chooseAndUploadFile:ok",s="chooseAndUploadFile:fail";function o(e,t){return e.tempFiles.forEach(((e,n)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}function r(e,t,{onChooseFile:s,onUploadProgress:o}){return t.then((e=>{if(s){const t=s(e);if(void 0!==t)return Promise.resolve(t).then((t=>void 0===t?e:t))}return e})).then((t=>!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t,s=5,o){(t=Object.assign({},t)).errMsg=n;const r=t.tempFiles,a=r.length;let i=0;return new Promise((n=>{for(;i<s;)c();function c(){const s=i++;if(s>=a)return void(!r.find((e=>!e.url&&!e.errMsg))&&n(t));const l=r[s];e.uploadFile({provider:l.provider,filePath:l.path,cloudPath:l.cloudPath,fileType:l.fileType,cloudPathAsRealPath:l.cloudPathAsRealPath,onUploadProgress(e){e.index=s,e.tempFile=l,e.tempFilePath=l.path,o&&o(e)}}).then((e=>{l.url=e.fileID,s<a&&c()})).catch((e=>{l.errMsg=e.errMsg||e.message,s<a&&c()}))}}))}(e,t,5,o)))}t.initChooseAndUploadFile=function(e){return function(t={type:"all"}){return"image"===t.type?r(e,function(e){const{count:t,sizeType:n,sourceType:r=["album","camera"],extension:a}=e;return new Promise(((e,i)=>{uni.chooseImage({count:t,sizeType:n,sourceType:r,extension:a,success(t){e(o(t,"image"))},fail(e){i({errMsg:e.errMsg.replace("chooseImage:fail",s)})}})}))}(t),t):"video"===t.type?r(e,function(e){const{camera:t,compressed:n,maxDuration:r,sourceType:a=["album","camera"],extension:i}=e;return new Promise(((e,c)=>{uni.chooseVideo({camera:t,compressed:n,maxDuration:r,sourceType:a,extension:i,success(t){const{tempFilePath:n,duration:s,size:r,height:a,width:i}=t;e(o({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:r,type:t.tempFile&&t.tempFile.type||"",width:i,height:a,duration:s,fileType:"video",cloudPath:""}]},"video"))},fail(e){c({errMsg:e.errMsg.replace("chooseVideo:fail",s)})}})}))}(t),t):r(e,function(e){const{count:t,extension:n}=e;return new Promise(((e,r)=>{let a=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(a=wx.chooseMessageFile),"function"!=typeof a)return r({errMsg:s+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});a({type:"all",count:t,extension:n,success(t){e(o(t))},fail(e){r({errMsg:e.errMsg.replace("chooseFile:fail",s)})}})}))}(t),t)}}})));const is="manual";function cs(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var e=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((t=>{e.push(this[t])})),e}),((e,t)=>{if(this.loadtime===is)return;let n=!1;const s=[];for(let o=2;o<e.length;o++)e[o]!==t[o]&&(s.push(e[o]),n=!0);e[0]!==t[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(n,s)}))},methods:{onMixinDatacomPropsChange(e,t){},mixinDatacomEasyGet({getone:e=!1,success:t,fail:n}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((n=>{this.mixinDatacomLoading=!1;const{data:s,count:o}=n.result;this.getcount&&(this.mixinDatacomPage.count=o),this.mixinDatacomHasMore=s.length<this.pageSize;const r=e?s.length?s[0]:void 0:s;this.mixinDatacomResData=r,t&&t(r)})).catch((e=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e,this.mixinDatacomError=e,n&&n(e)})))},mixinDatacomGet(t={}){let n;t=t||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);const s=t.action||this.action;s&&(n=n.action(s));const o=t.collection||this.collection;n=Array.isArray(o)?n.collection(...o):n.collection(o);const r=t.where||this.where;r&&Object.keys(r).length&&(n=n.where(r));const a=t.field||this.field;a&&(n=n.field(a));const i=t.foreignKey||this.foreignKey;i&&(n=n.foreignKey(i));const c=t.groupby||this.groupby;c&&(n=n.groupBy(c));const l=t.groupField||this.groupField;l&&(n=n.groupField(l)),!0===(void 0!==t.distinct?t.distinct:this.distinct)&&(n=n.distinct());const u=t.orderby||this.orderby;u&&(n=n.orderBy(u));const d=void 0!==t.pageCurrent?t.pageCurrent:this.mixinDatacomPage.current,h=void 0!==t.pageSize?t.pageSize:this.mixinDatacomPage.size,p=void 0!==t.getcount?t.getcount:this.getcount,g=void 0!==t.gettree?t.gettree:this.gettree,f=void 0!==t.gettreepath?t.gettreepath:this.gettreepath,m={getCount:p},y={limitLevel:void 0!==t.limitlevel?t.limitlevel:this.limitlevel,startWith:void 0!==t.startwith?t.startwith:this.startwith};return g&&(m.getTree=y),f&&(m.getTreePath=y),n=n.skip(h*(d-1)).limit(h).get(m),n}}}}function ls(e){return H("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function us({openid:e,callLoginByWeixin:t=!1}={}){throw ls(this),new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `app`")}async function ds(e){const t=ls(this);return t.initPromise||(t.initPromise=us.call(this,e).then((e=>e)).catch((e=>{throw delete t.initPromise,e}))),t.initPromise}function hs(e){Te=e}function ps(e){const t={getSystemInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(n){return new Promise(((s,o)=>{t[e]({...n,success(e){s(e)},fail(e){o(e)}})}))}}class gs extends class{constructor(){this._callback={}}addListener(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}on(e,t){return this.addListener(e,t)}removeListener(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');const n=this._callback[e];if(!n)return;const s=function(e,t){for(let n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(s,1)}off(e,t){return this.removeListener(e,t)}removeAllListener(e){delete this._callback[e]}emit(e,...t){const n=this._callback[e];if(n)for(let s=0;s<n.length;s++)n[s](...t)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([ps("getSystemInfo")(),ps("getPushClientId")()]).then((([{appId:e}={},{cid:t}={}]=[])=>{if(!e)throw new Error("Invalid appId, please check the manifest.json file");if(!t)throw new Error("Invalid push client id");this._appId=e,this._pushClientId=t,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(e=>{throw this.emit("error",e),this.close(),e}))}async open(){return this.init()}_isUniCloudSSE(e){if("receive"!==e.type)return!1;const t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}_receivePushMessage(e){if(!this._isUniCloudSSE(e))return;const t=e&&e.data&&e.data.payload,{action:n,messageId:s,message:o}=t;this._payloadQueue.push({action:n,messageId:s,message:o}),this._consumMessage()}_consumMessage(){for(;;){const e=this._payloadQueue.find((e=>e.messageId===this._currentMessageId+1));if(!e)break;this._currentMessageId++,this._parseMessagePayload(e)}}_parseMessagePayload(e){const{action:t,messageId:n,message:s}=e;"end"===t?this._end({messageId:n,message:s}):"message"===t&&this._appendMessage({messageId:n,message:s})}_appendMessage({messageId:e,message:t}={}){this.emit("message",t)}_end({messageId:e,message:t}={}){this.emit("end",t),this.close()}_initMessageListener(){uni.onPushMessage(this._uniPushMessageCallback)}_destroy(){uni.offPushMessage(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}const fs={tcb:$t,tencent:$t,aliyun:xe,private:Ht,dcloud:Ht,alipay:tn};let ms=new class{init(e){let t={};const n=fs[e.provider];if(!n)throw new Error("未提供正确的provider参数");var s;return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new F({createPromise:function(){let t=Promise.resolve();t=new Promise((e=>{setTimeout((()=>{e()}),1)}));const n=e.auth();return t.then((()=>n.getLoginState())).then((e=>e?Promise.resolve():n.signInAnonymously()))}}))}(t),vn(t),function(e){const t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),(s=t).database=function(e){if(e&&Object.keys(e).length>0)return s.init(e).database();if(this._database)return this._database;const t=Cn(Nn,{uniClient:s});return this._database=t,t},s.databaseForJQL=function(e){if(e&&Object.keys(e).length>0)return s.init(e).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const t=Cn(Nn,{uniClient:s,isJQL:!0});return this._databaseForJQL=t,t},function(e){e.getCurrentUserInfo=rs,e.chooseAndUploadFile=as.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return cs(e)}}),e.SSEChannel=gs,e.initSecureNetworkByWeixin=function(e){return function({openid:t,callLoginByWeixin:n=!1}={}){return ds.call(e,{openid:t,callLoginByWeixin:n})}}(e),e.setCustomClientInfo=hs,e.importObject=function(t){return function(n,s={}){s=function(e,t={}){return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==typeof t.secretMethods&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},s);const{customUI:o,loadingOptions:r,errorOptions:a,parseSystemError:i}=s,c=!o;return new Proxy({},{get(o,l){switch(l){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:e,interceptorName:t,getCallbackArgs:n}={}){return async function(...s){const o=n?n({params:s}):{};let r,a;try{return await Y(X(t,"invoke"),{...o}),r=await e(...s),await Y(X(t,"success"),{...o,result:r}),r}catch(i){throw a=i,await Y(X(t,"fail"),{...o,error:a}),a}finally{await Y(X(t,"complete"),a?{...o,error:a}:{...o,result:r})}}}({fn:async function o(...u){let d;c&&uni.showLoading({title:r.title,mask:r.mask});const h={name:n,type:x,data:{method:l,params:u}};"object"==typeof s.secretMethods&&function(e,t){const n=t.data.method,s=e.secretMethods||{},o=s[n]||s["*"];o&&(t.secretType=o)}(s,h);let p=!1;try{d=await t.callFunction(h)}catch(e){p=!0,d={result:new fe(e)}}const{errSubject:g,errCode:f,errMsg:m,newToken:y}=d.result||{};if(c&&uni.hideLoading(),y&&y.token&&y.tokenExpired&&(ve(y),ue(se,{...y})),f){let e=m;if(p&&i&&(e=(await i({objectName:n,methodName:l,params:u,errSubject:g,errCode:f,errMsg:m})).errMsg||m),c)if("toast"===a.type)uni.showToast({title:e,icon:"none"});else{if("modal"!==a.type)throw new Error(`Invalid errorOptions.type: ${a.type}`);{const{confirm:t}=await async function({title:e,content:t,showCancel:n,cancelText:s,confirmText:o}={}){return new Promise(((r,a)=>{uni.showModal({title:e,content:t,showCancel:n,cancelText:s,confirmText:o,success(e){r(e)},fail(){r({confirm:!1,cancel:!0})}})}))}({title:"提示",content:e,showCancel:a.retry,cancelText:"取消",confirmText:a.retry?"重试":"确定"});if(a.retry&&t)return o(...u)}}const t=new fe({subject:g,code:f,message:m,requestId:d.requestId});throw t.detail=d.result,ue(te,{type:ae,content:t}),t}return ue(te,{type:ae,content:d.result}),d.result},interceptorName:"callObject",getCallbackArgs:function({params:e}={}){return{objectName:n,methodName:l,params:e}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((e=>{if(!t[e])return;const n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){let s=!1;if("callFunction"===t){const e=n&&n.type||E;s=e!==E}const o="callFunction"===t&&!s,r=this._initPromiseHub.exec();n=n||{};const{success:a,fail:i,complete:c}=ge(n),l=r.then((()=>s?Promise.resolve():Y(X(t,"invoke"),n))).then((()=>e.call(this,n))).then((e=>s?Promise.resolve(e):Y(X(t,"success"),e).then((()=>Y(X(t,"complete"),e))).then((()=>(o&&ue(te,{type:re,content:e}),Promise.resolve(e))))),(e=>s?Promise.reject(e):Y(X(t,"fail"),e).then((()=>Y(X(t,"complete"),e))).then((()=>(ue(te,{type:re,content:e}),Promise.reject(e))))));if(!(a||i||c))return l;l.then((e=>{a&&a(e),c&&c(e),o&&ue(te,{type:re,content:e})}),(e=>{i&&i(e),c&&c(e),o&&ue(te,{type:re,content:e})}))}}(t[e],e).bind(t)})),t.init=this.init,t}};(()=>{const e=B;let t={};if(e&&1===e.length)t=e[0],ms=ms.init(t),ms._isDefault=!0;else{const t=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let n;n=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",t.forEach((e=>{ms[e]=function(){return console.error(n),Promise.reject(new fe({code:"SYS_ERR",message:n}))}}))}Object.assign(ms,{get mixinDatacom(){return cs(ms)}}),ts(ms),ms.addInterceptor=G,ms.removeInterceptor=Q,ms.interceptObject=Z,uni.__uniCloud=ms;{const e=K||(K=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),K);e.uniCloud=ms,e.UniCloudError=fe}})();const{app:ys,Vuex:_s,Pinia:vs}=function(){const t=e.createVueApp(g);return t.config.globalProperties.$request=_.request,t.config.globalProperties.$http=_.http,t.config.globalProperties.baseURL=BASE_URL,{app:t}}();uni.Vuex=_s,uni.Pinia=vs,ys.provide("__globalStyles",__uniConfig.styles),ys._component.mpType="app",ys._component.render=()=>{},ys.mount("#app")}(Vue);
