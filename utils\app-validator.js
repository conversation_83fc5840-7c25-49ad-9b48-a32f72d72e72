/**
 * 应用配置验证工具
 * 用于检查应用配置是否正确，确保打包后正常运行
 */

import { BASE_URL, API_TIMEOUT, ENV } from '@/config/index.js';

/**
 * 验证配置是否正确
 */
export const validateConfig = () => {
  const issues = [];
  
  // 检查BASE_URL
  if (!BASE_URL) {
    issues.push('BASE_URL 未配置');
  } else if (!BASE_URL.startsWith('http')) {
    issues.push('BASE_URL 格式不正确，应以 http:// 或 https:// 开头');
  } else if (!BASE_URL.endsWith('/')) {
    issues.push('BASE_URL 应以 / 结尾');
  }
  
  // 检查API_TIMEOUT
  if (!API_TIMEOUT || typeof API_TIMEOUT !== 'number') {
    issues.push('API_TIMEOUT 未配置或格式不正确');
  }
  
  // 检查ENV
  if (!ENV) {
    issues.push('ENV 环境标识未配置');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    config: {
      BASE_URL,
      API_TIMEOUT,
      ENV
    }
  };
};

/**
 * 测试网络连接
 */
export const testNetworkConnection = async () => {
  try {
    const res = await uni.request({
      url: BASE_URL + 'health',
      method: 'GET',
      timeout: 5000
    });
    
    return {
      success: true,
      message: '网络连接正常',
      response: res
    };
  } catch (error) {
    return {
      success: false,
      message: '网络连接失败',
      error: error
    };
  }
};

/**
 * 验证token有效性
 */
export const validateToken = async () => {
  const authorization = uni.getStorageSync('authorization');
  
  if (!authorization) {
    return {
      valid: false,
      message: '未找到授权token'
    };
  }
  
  try {
    const res = await uni.request({
      url: BASE_URL + 'user',
      method: 'GET',
      header: {
        'Authorization': authorization,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    if (res.data && res.data.code === 0) {
      return {
        valid: true,
        message: 'Token有效',
        userInfo: res.data.data
      };
    } else {
      return {
        valid: false,
        message: 'Token无效',
        response: res.data
      };
    }
  } catch (error) {
    return {
      valid: false,
      message: 'Token验证失败',
      error: error
    };
  }
};

/**
 * 完整的应用健康检查
 */
export const healthCheck = async () => {
  console.log('=== 应用健康检查开始 ===');
  
  // 1. 配置验证
  const configResult = validateConfig();
  console.log('配置验证:', configResult);
  
  // 2. 网络连接测试
  const networkResult = await testNetworkConnection();
  console.log('网络连接测试:', networkResult);
  
  // 3. Token验证
  const tokenResult = await validateToken();
  console.log('Token验证:', tokenResult);
  
  const overallHealth = {
    config: configResult.isValid,
    network: networkResult.success,
    token: tokenResult.valid,
    timestamp: new Date().toISOString()
  };
  
  console.log('=== 应用健康检查完成 ===');
  console.log('整体状态:', overallHealth);
  
  return {
    overall: overallHealth,
    details: {
      config: configResult,
      network: networkResult,
      token: tokenResult
    }
  };
};

export default {
  validateConfig,
  testNetworkConnection,
  validateToken,
  healthCheck
};
