if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...n){uni.__log__?uni.__log__(e,t,...n):console[e].apply(console,[...n,t])}const n=(t=>(n,s=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,n,s)})("onLoad"),s="/static/pet/rabbit.png",o={__name:"login",setup(n){const o=e.ref(""),r=e.ref(""),i=e.ref(!1),a=e.ref(60);e.onMounted((()=>{(()=>{const e=uni.getStorageSync("authorization");e&&uni.request({url:"http://124.223.80.197:8080/user",method:"GET",header:{Authorization:e},success:e=>{e.data&&0===e.data.code?uni.reLaunch({url:"/pages/tabbar/index/index"}):uni.removeStorageSync("authorization")},fail:()=>{}})})()}));const c=()=>{o.value=o.value.replace(/\D/g,""),o.value.length>11&&(o.value=o.value.slice(0,11))},l=()=>{o.value?11===o.value.length?i.value||((()=>{i.value=!0,a.value=60;const e=setInterval((()=>{a.value--,a.value<=0&&(clearInterval(e),i.value=!1)}),1e3)})(),uni.request({url:"http://124.223.80.197:8080/user",method:"POST",data:{phone:o.value},header:{"Content-Type":"application/json"}}).then((e=>{t("log","at pages/login/login.vue:132",e),uni.showToast({title:"验证码已发送",icon:"success"})})).catch((e=>{t("log","at pages/login/login.vue:138",e),uni.showToast({title:"发送失败，请重试",icon:"none"}),i.value=!1}))):uni.showToast({title:"请输入11位手机号",icon:"none"}):uni.showToast({title:"请输入手机号",icon:"none"})},u=()=>{o.value&&r.value?11===o.value.length?6===r.value.length?uni.request({url:"http://124.223.80.197:8080/user",method:"POST",data:{phone:o.value,code:r.value},header:{"Content-Type":"application/json"}}).then((e=>{t("log","at pages/login/login.vue:185",e),uni.setStorageSync("authorization",e.data.data.authorization),t("log","at pages/login/login.vue:189","保存的authorization:",uni.getStorageSync("authorization")),uni.reLaunch({url:"/pages/tabbar/index/index",success:function(){setTimeout((()=>{uni.showToast({title:"登录成功",icon:"success"})}),200)},fail:function(e){t("error","at pages/login/login.vue:204","跳转到首页失败:",e)}})})).catch((e=>{t("log","at pages/login/login.vue:208",e),uni.showToast({title:"登录失败，请重试",icon:"none"})})):uni.showToast({title:"验证码格式错误",icon:"none"}):uni.showToast({title:"请输入11位手机号",icon:"none"}):uni.showToast({title:"请输入手机号和验证码",icon:"none"})};return(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"login-page"},[e.createElementVNode("image",{class:"bg-image",src:s,mode:"aspectFill"}),e.createElementVNode("view",{class:"login-container"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("image",{class:"logo",src:s}),e.createElementVNode("text",{class:"app-title"},"GuiYuan")]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-item"},[e.withDirectives(e.createElementVNode("input",{type:"number",maxlength:"11","onUpdate:modelValue":n[0]||(n[0]=e=>o.value=e),placeholder:"请输入手机号","placeholder-class":"placeholder",onInput:c},null,544),[[e.vModelText,o.value]])]),e.createElementVNode("view",{class:"code-container"},[e.createElementVNode("view",{class:"input-item code-input"},[e.withDirectives(e.createElementVNode("input",{type:"number",maxlength:"6","onUpdate:modelValue":n[1]||(n[1]=e=>r.value=e),placeholder:"请输入验证码","placeholder-class":"placeholder"},null,512),[[e.vModelText,r.value]])]),e.createElementVNode("button",{class:"send-code-btn",onClick:l,disabled:i.value},e.toDisplayString(i.value?`${a.value}s后重试`:"发送验证码"),9,["disabled"])]),e.createElementVNode("button",{class:"login-btn",onClick:u},"登录")])])]))}},r="/static/user/avatar.jpg",i={__name:"index",setup(o){const i=e.ref([]),a=e.ref(1),c=e.ref(10),l=e.ref(0),u=e.ref(0),d=e.ref(!1),h=e.ref(!1),p=e=>{var t,n;const s=320+.8*(((null==(t=e.title)?void 0:t.length)||0)+((null==(n=e.content)?void 0:n.length)||0)),o=100*Math.random()-50;return Math.max(240,Math.min(500,s+o))+"rpx"},g=e.computed((()=>i.value.filter(((e,t)=>t%2==0)))),f=e.computed((()=>i.value.filter(((e,t)=>t%2==1)))),m=async(e=1,n=!1)=>{if(!d.value){d.value=!0;try{const s=uni.getStorageSync("authorization"),o=await uni.request({url:"http://124.223.80.197:8080/recommend",method:"GET",header:{Cookie:`Authorization=${s}`},data:{pageNum:e,pageSize:c.value},withCredentials:!0});if(t("log","at pages/tabbar/index/index.vue:161","推荐列表响应:",o),o.data&&o.data.data){const e=o.data.data;l.value=e.total,u.value=e.pages;const t=(e.records||[]).map((e=>({...e,content:e.content||"这是一段随机生成的内容，用来展示瀑布流布局效果。内容长度不同，会影响卡片高度。"})));i.value=n?t:[...i.value,...t],h.value=a.value>=u.value}}catch(s){t("error","at pages/tabbar/index/index.vue:190","获取推荐列表失败:",s)}finally{d.value=!1}}},y=()=>{h.value||d.value||(a.value++,m(a.value))},_=e=>{uni.navigateTo({url:`/pages/recommend/detail?id=${e.id}`})};return e.onMounted((async()=>{await m(1,!0)})),n((()=>{const e=getApp();t("log","at pages/tabbar/index/index.vue:223","当前页面authorization值:",uni.getStorageSync("authorization")),e.checkLogin()&&(t("log","at pages/tabbar/index/index.vue:228","登录成功"),m(1,!0))})),(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("image",{class:"logo",src:s}),e.createElementVNode("text",{class:"title"},"推荐")]),e.createElementVNode("view",{class:"search-box"},[e.createElementVNode("text",{class:"search-placeholder"},"搜索")])]),e.createElementVNode("scroll-view",{class:"waterfall","scroll-y":"",onScrolltolower:y},[e.createElementVNode("view",{class:"waterfall-wrapper"},[e.createElementVNode("view",{class:"waterfall-column left-column"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(g.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"waterfall-item",key:n,onClick:e=>_(t),style:e.normalizeStyle({marginBottom:10+Math.floor(15*Math.random())+"rpx"})},[e.createElementVNode("view",{class:"media-wrapper",style:e.normalizeStyle({height:p(t)})},[e.createElementVNode("image",{class:"item-image",src:t.img,mode:"aspectFill"},null,8,["src"])],4),e.createElementVNode("view",{class:"item-content"},[e.createElementVNode("text",{class:"item-title",style:e.normalizeStyle({"-webkit-line-clamp":n%2==0?2:1})},e.toDisplayString(t.title),5),e.createElementVNode("text",{class:"item-desc",style:e.normalizeStyle({"-webkit-line-clamp":n%5==0?4:n%3==0?3:2})},e.toDisplayString(t.content),5),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("image",{class:"user-avatar",src:r}),e.createElementVNode("text",{class:"user-name"},"用户"+e.toDisplayString(t.userid),1)]),e.createElementVNode("view",{class:"like-info"},[e.createElementVNode("image",{class:"icon-rabbit",src:s})])])])],12,["onClick"])))),128))]),e.createElementVNode("view",{class:"waterfall-column right-column"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(f.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"waterfall-item",key:n,onClick:e=>_(t),style:e.normalizeStyle({marginBottom:10+Math.floor(15*Math.random())+"rpx"})},[e.createElementVNode("view",{class:"media-wrapper",style:e.normalizeStyle({height:p(t)})},[e.createElementVNode("image",{class:"item-image",src:t.img,mode:"aspectFill"},null,8,["src"]),n%7==0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"floating-title"},[e.createElementVNode("text",null,e.toDisplayString(t.title),1)])):e.createCommentVNode("",!0)],4),e.createElementVNode("view",{class:"item-content"},[e.createElementVNode("text",{class:"item-title",style:e.normalizeStyle({"-webkit-line-clamp":n%2==0?1:2})},e.toDisplayString(t.title),5),e.createElementVNode("text",{class:"item-desc",style:e.normalizeStyle({"-webkit-line-clamp":n%4==0?3:2})},e.toDisplayString(t.content),5),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("image",{class:"user-avatar",src:r}),e.createElementVNode("text",{class:"user-name"},"用户"+e.toDisplayString(t.userid),1)]),e.createElementVNode("view",{class:"like-info"},[e.createElementVNode("image",{class:"icon-rabbit",src:s})])])])],12,["onClick"])))),128))])]),d.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading"},[e.createElementVNode("text",{class:"loading-text"},"加载中...")])):e.createCommentVNode("",!0),h.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-more"},[e.createElementVNode("text",{class:"no-more-text"},"没有更多了~")])):e.createCommentVNode("",!0)],32)]))}},a={__name:"home",setup(n){const s=e.ref({}),o=e.ref({nickname:"",gender:"",birthday:""}),r=["男","女","其他"],i=async()=>{try{const e=await uni.request({url:"http://124.223.80.197:8080/user",method:"GET",header:{Authorization:uni.getStorageSync("authorization")},withCredentials:!0});e.data&&e.data.data&&(s.value=e.data.data,o.value={id:s.value.id,nickname:s.value.nickname,gender:s.value.gender,birthday:s.value.birthday})}catch(e){t("error","at pages/tabbar/home/<USER>","获取用户信息失败:",e)}},a=async()=>{var e;try{const n=await uni.request({url:"http://124.223.80.197:8080/user",method:"PUT",header:{Authorization:uni.getStorageSync("authorization"),"Content-Type":"application/json"},data:JSON.stringify(o.value),withCredentials:!0});if(n.data&&0===n.data.code)uni.showToast({title:"保存成功",icon:"success"}),i();else{const s=(null==(e=n.data)?void 0:e.message)||"保存失败";t("error","at pages/tabbar/home/<USER>","保存设置失败:",s),uni.showToast({title:s,icon:"none"})}}catch(n){t("error","at pages/tabbar/home/<USER>","保存设置失败:",n),uni.showToast({title:n.message||"保存失败",icon:"none",duration:2e3})}},c=e=>new Promise(((n,s)=>{uni.getFileSystemManager().readFile({filePath:e,encoding:"base64",success:e=>{const o={avatar:`data:image/jpeg;base64,${e.data}`,type:"avatar"};uni.request({url:"http://124.223.80.197:8080/user",method:"PUT",header:{Authorization:uni.getStorageSync("authorization"),"Content-Type":"application/json"},data:o,success:e=>{try{const t=e.data;if(t&&0===t.code)uni.showToast({title:"头像更新成功",icon:"success"}),i(),n(t);else{const e=(null==t?void 0:t.message)||"头像更新失败";uni.showToast({title:e,icon:"none",duration:2e3}),s(new Error(e))}}catch(o){t("error","at pages/tabbar/home/<USER>","处理响应数据失败:",o),uni.showToast({title:"处理响应数据失败",icon:"none",duration:2e3}),s(o)}},fail:e=>{t("error","at pages/tabbar/home/<USER>","上传头像失败:",e),uni.showToast({title:"上传头像失败",icon:"none",duration:2e3}),s(e)}})},fail:e=>{t("error","at pages/tabbar/home/<USER>","读取图片失败:",e),uni.showToast({title:"读取图片失败",icon:"none",duration:2e3}),s(e)}})})),l=async()=>{try{const e=await uni.request({url:"http://124.223.80.197:8080/user",method:"DELETE",header:{Authorization:uni.getStorageSync("authorization")},withCredentials:!0});e.data&&0===e.data.code?(uni.removeStorageSync("authorization"),uni.reLaunch({url:"/pages/login/login"})):uni.showToast({title:e.data.message||"退出失败",icon:"none"})}catch(e){t("error","at pages/tabbar/home/<USER>","退出登录失败:",e),uni.showToast({title:"退出失败",icon:"none"})}},u=()=>{uni.showActionSheet({itemList:["从相册选择","拍照"],success:e=>{0===e.tapIndex?d():1===e.tapIndex&&h()}})},d=async()=>{try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album"]});e.tempFilePaths.length>0&&await c(e.tempFilePaths[0])}catch(e){t("error","at pages/tabbar/home/<USER>","选择头像失败:",e)}},h=async()=>{try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["camera"]});e.tempFilePaths.length>0&&await c(e.tempFilePaths[0])}catch(e){t("error","at pages/tabbar/home/<USER>","拍照失败:",e)}},p=e=>{o.value.gender=r[e.detail.value]},g=e=>{o.value.birthday=e.detail.value};return e.onMounted((()=>{i()})),(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"user-card"},[e.createElementVNode("view",{class:"avatar-container"},[e.createElementVNode("image",{class:"avatar",src:s.value.avatar||"/static/user/avatar.jpg"},null,8,["src"]),e.createElementVNode("button",{class:"edit-avatar-btn",onClick:u},"更换头像")]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"nickname"},e.toDisplayString(s.value.nickname||"未设置昵称"),1),e.createElementVNode("text",{class:"username"},"ID: "+e.toDisplayString(s.value.id),1)])]),e.createElementVNode("view",{class:"settings-form"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"昵称"),e.withDirectives(e.createElementVNode("input",{class:"input","onUpdate:modelValue":n[0]||(n[0]=e=>o.value.nickname=e),placeholder:"请输入昵称"},null,512),[[e.vModelText,o.value.nickname]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"性别"),e.createElementVNode("picker",{class:"picker",mode:"selector",range:r,onChange:p},[e.createElementVNode("text",null,e.toDisplayString(o.value.gender||"请选择性别"),1)],32)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"生日"),e.createElementVNode("picker",{class:"picker",mode:"date",onChange:g},[e.createElementVNode("text",null,e.toDisplayString(o.value.birthday||"请选择生日"),1)],32)]),e.createElementVNode("button",{class:"save-btn",onClick:a},"保存设置"),e.createElementVNode("button",{class:"logout-btn",onClick:l},"退出登录")])]))}};const c=((e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n})({data:()=>({}),methods:{}},[["render",function(t,n,s,o,r,i){return e.openBlock(),e.createElementBlock("view")}]]),l={__name:"daily",setup(s){const o=getApp(),r=uni.getStorageSync("authorization"),i=e.ref([]),a=e.ref(!1),c=e.reactive({id:null,userId:null,title:"",content:"",image:""}),l=async()=>{try{const e=await uni.request({url:"http://124.223.80.197:8080/daily",method:"GET",header:{Authorization:r},data:{userId:o.globalData.userId}});i.value=e.data.data}catch(e){t("error","at pages/tabbar/daily/daily.vue:58","获取日记列表失败:",e)}},u=()=>{c.id=null,c.title="",c.content="",c.image=""};return n((()=>{o.checkLogin()&&l()})),(n,s)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[i.value&&i.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"daily-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.value,((n,s)=>(e.openBlock(),e.createElementBlock("view",{class:"daily-item",key:s},[e.createElementVNode("view",{class:"daily-header"},[e.createElementVNode("text",{class:"daily-title"},e.toDisplayString(n.title),1),e.createElementVNode("view",{class:"daily-actions"},[e.createElementVNode("button",{onClick:e=>{return t=n,Object.assign(c,t),void(a.value=!0);var t}},"编辑",8,["onClick"]),e.createElementVNode("button",{onClick:e=>(async e=>{try{await uni.request({url:"http://124.223.80.197:8080/daily",method:"DELETE",header:{Authorization:r},data:{id:e,userId:o.globalData.userId}}),l()}catch(n){t("error","at pages/tabbar/daily/daily.vue:104","删除日记失败:",n)}})(n.id)},"删除",8,["onClick"])])]),e.createElementVNode("view",{class:"daily-content"},e.toDisplayString(n.content),1),n.image?(e.openBlock(),e.createElementBlock("image",{key:0,src:n.image,class:"daily-image"},null,8,["src"])):e.createCommentVNode("",!0)])))),128))])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"form-container"},[e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":s[0]||(s[0]=e=>c.title=e),placeholder:"标题"},null,512),[[e.vModelText,c.title]]),e.withDirectives(e.createElementVNode("textarea",{"onUpdate:modelValue":s[1]||(s[1]=e=>c.content=e),placeholder:"内容"},null,512),[[e.vModelText,c.content]]),e.createElementVNode("button",{onClick:s[2]||(s[2]=e=>a.value?(async()=>{try{await uni.request({url:"http://124.223.80.197:8080/daily",method:"PUT",header:{Authorization:r},data:c}),l(),u(),a.value=!1}catch(e){t("error","at pages/tabbar/daily/daily.vue:90","更新日记失败:",e)}})():(async()=>{try{c.userId=o.globalData.userId,await uni.request({url:"http://124.223.80.197:8080/daily",method:"POST",header:{Authorization:r},data:c}),l(),u()}catch(e){t("error","at pages/tabbar/daily/daily.vue:74","添加日记失败:",e)}})())},e.toDisplayString(a.value?"更新":"添加"),1)])]))}};__definePage("pages/login/login",o),__definePage("pages/tabbar/index/index",i),__definePage("pages/tabbar/home/<USER>",a),__definePage("pages/tabbar/message/message",c),__definePage("pages/tabbar/daily/daily",l);const u={globalData:{isLogin:!1},onLaunch:function(){t("log","at App.vue:7","App Launch");if(uni.getStorageSync("authorization")){t("log","at App.vue:12","用户已登录"),this.globalData.isLogin=!0;const e=getCurrentPages(),n=e[e.length-1];n&&n.route&&n.route.includes("login")&&uni.switchTab({url:"/pages/tabbar/index/index"})}else t("log","at App.vue:24","用户未登录"),this.globalData.isLogin=!1},onShow:function(){t("log","at App.vue:29","App Show")},onHide:function(){t("log","at App.vue:32","App Hide")},methods:{checkLogin:()=>!!uni.getStorageSync("authorization")||(uni.showToast({title:"请先登录",icon:"none"}),uni.navigateTo({url:"/pages/login/login"}),!1),logout(){uni.removeStorageSync("authorization"),this.globalData.isLogin=!1,uni.reLaunch({url:"/pages/login/login"})}}},d="http://124.223.80.197:8080/",h={baseURL:d,timeout:1e4,header:{"Content-Type":"application/json"}},p=(e={})=>(e=(e=>{e.url=(e.baseURL||h.baseURL)+(e.url||""),e.header={...h.header,...e.header||{}},e.timeout=e.timeout||h.timeout;const n=uni.getStorageSync("authorization");return n&&(e.header.Authorization=n),t("log","at api/request.js:32","请求拦截器 ==>",{url:e.url,method:e.method,data:e.data,header:e.header}),e})(e),new Promise(((n,s)=>{uni.request({...e,success:o=>{((e,n)=>{var s;return t("log","at api/request.js:50","响应拦截器 ==>",{url:n.url,data:e,statusCode:e.statusCode}),e.statusCode>=200&&e.statusCode<300?Promise.resolve(e.data):401===e.statusCode?(uni.removeStorageSync("authorization"),uni.showToast({title:"登录已过期，请重新登录",icon:"none"}),uni.reLaunch({url:"/pages/login/login"}),Promise.reject(e)):(uni.showToast({title:(null==(s=e.data)?void 0:s.message)||`请求失败(${e.statusCode})`,icon:"none"}),Promise.reject(e))})(o,e).then(n).catch(s)},fail:n=>{((e,n)=>(t("error","at api/request.js:88","请求错误 ==>",{url:n.url,error:e}),uni.showToast({title:"网络异常，请稍后重试",icon:"none"}),Promise.reject(e)))(n,e).catch(s)}})}))),g={get:(e,t={},n={})=>p({url:e,data:t,method:"GET",...n}),post:(e,t={},n={})=>p({url:e,data:t,method:"POST",...n}),put:(e,t={},n={})=>p({url:e,data:t,method:"PUT",...n}),delete:(e,t={},n={})=>p({url:e,data:t,method:"DELETE",...n}),upload:(e,t,n={},s={})=>{const o={url:(s.baseURL||h.baseURL)+e,filePath:t,name:s.name||"file",formData:n,header:s.header||{}},r=uni.getStorageSync("authorization");return r&&(o.header.Authorization=r),new Promise(((e,t)=>{uni.uploadFile({...o,success:n=>{if(n.statusCode>=200&&n.statusCode<300)try{const t=JSON.parse(n.data);e(t)}catch(Ee){e(n.data)}else t(n)},fail:t})}))}},f={BASE_URL:d,config:h,request:p,http:g},m={pages:[{path:"pages/login/login",style:{navigationBarTitleText:"账号登陆"}},{path:"pages/tabbar/index/index",style:{navigationBarTitleText:"桂圆可爱"}},{path:"pages/tabbar/home/<USER>",style:{navigationBarTitleText:"个人主页"}},{path:"pages/tabbar/message/message",style:{navigationBarTitleText:"我的消息"}},{path:"pages/tabbar/daily/daily",style:{navigationBarTitleText:"发布日常"}}],globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},tabBar:{color:"#F0AD4E",selectedColor:"#3cc51f",borderStyle:"black",backgroundColor:"#F8F8F8",list:[{pagePath:"pages/tabbar/index/index",iconPath:"/static/index/index.png",selectedIconPath:"/static/index/indexselect.png",text:"首页"},{pagePath:"pages/tabbar/daily/daily",iconPath:"/static/daily/daily.png",selectedIconPath:"/static/daily/dailyselect.png",text:"日常"},{pagePath:"pages/tabbar/message/message",iconPath:"/static/message/message.png",selectedIconPath:"/static/message/messageselect.png",text:"消息"},{pagePath:"pages/tabbar/home/<USER>",iconPath:"/static/home/<USER>",selectedIconPath:"/static/home/<USER>",text:"主页"}]}};function y(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var _=y((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),s={},o=s.lib={},r=o.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},i=o.WordArray=r.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,n=e.words,s=this.sigBytes,o=e.sigBytes;if(this.clamp(),s%4)for(var r=0;r<o;r++){var i=n[r>>>2]>>>24-r%4*8&255;t[s+r>>>2]|=i<<24-(s+r)%4*8}else for(r=0;r<o;r+=4)t[s+r>>>2]=n[r>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=r.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,s=[],o=function(t){var n=987654321,s=4294967295;return function(){var o=((n=36969*(65535&n)+(n>>16)&s)<<16)+(t=18e3*(65535&t)+(t>>16)&s)&s;return o/=4294967296,(o+=.5)*(e.random()>.5?1:-1)}},r=0;r<t;r+=4){var a=o(4294967296*(n||e.random()));n=987654071*a(),s.push(4294967296*a()|0)}return new i.init(s,t)}}),a=s.enc={},c=a.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],o=0;o<n;o++){var r=t[o>>>2]>>>24-o%4*8&255;s.push((r>>>4).toString(16)),s.push((15&r).toString(16))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s+=2)n[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new i.init(n,t/2)}},l=a.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],o=0;o<n;o++){var r=t[o>>>2]>>>24-o%4*8&255;s.push(String.fromCharCode(r))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s++)n[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new i.init(n,t)}},u=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(l.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return l.parse(unescape(encodeURIComponent(e)))}},d=o.BufferedBlockAlgorithm=r.extend({reset:function(){this._data=new i.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=u.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,s=n.words,o=n.sigBytes,r=this.blockSize,a=o/(4*r),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*r,l=e.min(4*c,o);if(c){for(var u=0;u<c;u+=r)this._doProcessBlock(s,u);var d=s.splice(0,c);n.sigBytes-=l}return new i.init(d,l)},clone:function(){var e=r.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=d.extend({cfg:r.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}});var h=s.algo={};return s}(Math),n)})),v=_,w=(y((function(e,t){var n;e.exports=(n=v,function(e){var t=n,s=t.lib,o=s.WordArray,r=s.Hasher,i=t.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=i.MD5=r.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var s=t+n,o=e[s];e[s]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var r=this._hash.words,i=e[t+0],c=e[t+1],p=e[t+2],g=e[t+3],f=e[t+4],m=e[t+5],y=e[t+6],_=e[t+7],v=e[t+8],w=e[t+9],S=e[t+10],k=e[t+11],b=e[t+12],T=e[t+13],I=e[t+14],A=e[t+15],P=r[0],x=r[1],E=r[2],C=r[3];P=l(P,x,E,C,i,7,a[0]),C=l(C,P,x,E,c,12,a[1]),E=l(E,C,P,x,p,17,a[2]),x=l(x,E,C,P,g,22,a[3]),P=l(P,x,E,C,f,7,a[4]),C=l(C,P,x,E,m,12,a[5]),E=l(E,C,P,x,y,17,a[6]),x=l(x,E,C,P,_,22,a[7]),P=l(P,x,E,C,v,7,a[8]),C=l(C,P,x,E,w,12,a[9]),E=l(E,C,P,x,S,17,a[10]),x=l(x,E,C,P,k,22,a[11]),P=l(P,x,E,C,b,7,a[12]),C=l(C,P,x,E,T,12,a[13]),E=l(E,C,P,x,I,17,a[14]),P=u(P,x=l(x,E,C,P,A,22,a[15]),E,C,c,5,a[16]),C=u(C,P,x,E,y,9,a[17]),E=u(E,C,P,x,k,14,a[18]),x=u(x,E,C,P,i,20,a[19]),P=u(P,x,E,C,m,5,a[20]),C=u(C,P,x,E,S,9,a[21]),E=u(E,C,P,x,A,14,a[22]),x=u(x,E,C,P,f,20,a[23]),P=u(P,x,E,C,w,5,a[24]),C=u(C,P,x,E,I,9,a[25]),E=u(E,C,P,x,g,14,a[26]),x=u(x,E,C,P,v,20,a[27]),P=u(P,x,E,C,T,5,a[28]),C=u(C,P,x,E,p,9,a[29]),E=u(E,C,P,x,_,14,a[30]),P=d(P,x=u(x,E,C,P,b,20,a[31]),E,C,m,4,a[32]),C=d(C,P,x,E,v,11,a[33]),E=d(E,C,P,x,k,16,a[34]),x=d(x,E,C,P,I,23,a[35]),P=d(P,x,E,C,c,4,a[36]),C=d(C,P,x,E,f,11,a[37]),E=d(E,C,P,x,_,16,a[38]),x=d(x,E,C,P,S,23,a[39]),P=d(P,x,E,C,T,4,a[40]),C=d(C,P,x,E,i,11,a[41]),E=d(E,C,P,x,g,16,a[42]),x=d(x,E,C,P,y,23,a[43]),P=d(P,x,E,C,w,4,a[44]),C=d(C,P,x,E,b,11,a[45]),E=d(E,C,P,x,A,16,a[46]),P=h(P,x=d(x,E,C,P,p,23,a[47]),E,C,i,6,a[48]),C=h(C,P,x,E,_,10,a[49]),E=h(E,C,P,x,I,15,a[50]),x=h(x,E,C,P,m,21,a[51]),P=h(P,x,E,C,b,6,a[52]),C=h(C,P,x,E,g,10,a[53]),E=h(E,C,P,x,S,15,a[54]),x=h(x,E,C,P,c,21,a[55]),P=h(P,x,E,C,v,6,a[56]),C=h(C,P,x,E,A,10,a[57]),E=h(E,C,P,x,y,15,a[58]),x=h(x,E,C,P,T,21,a[59]),P=h(P,x,E,C,f,6,a[60]),C=h(C,P,x,E,k,10,a[61]),E=h(E,C,P,x,p,15,a[62]),x=h(x,E,C,P,w,21,a[63]),r[0]=r[0]+P|0,r[1]=r[1]+x|0,r[2]=r[2]+E|0,r[3]=r[3]+C|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,o=8*t.sigBytes;n[o>>>5]|=128<<24-o%32;var r=e.floor(s/4294967296),i=s;n[15+(o+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),n[14+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),t.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,c=a.words,l=0;l<4;l++){var u=c[l];c[l]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return a},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,n,s,o,r,i){var a=e+(t&n|~t&s)+o+i;return(a<<r|a>>>32-r)+t}function u(e,t,n,s,o,r,i){var a=e+(t&s|n&~s)+o+i;return(a<<r|a>>>32-r)+t}function d(e,t,n,s,o,r,i){var a=e+(t^n^s)+o+i;return(a<<r|a>>>32-r)+t}function h(e,t,n,s,o,r,i){var a=e+(n^(t|~s))+o+i;return(a<<r|a>>>32-r)+t}t.MD5=r._createHelper(c),t.HmacMD5=r._createHmacHelper(c)}(Math),n.MD5)})),y((function(e,t){var n,s,o;e.exports=(s=(n=v).lib.Base,o=n.enc.Utf8,void(n.algo.HMAC=s.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=o.parse(t));var n=e.blockSize,s=4*n;t.sigBytes>s&&(t=e.finalize(t)),t.clamp();for(var r=this._oKey=t.clone(),i=this._iKey=t.clone(),a=r.words,c=i.words,l=0;l<n;l++)a[l]^=1549556828,c[l]^=909522486;r.sigBytes=i.sigBytes=s,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})))})),y((function(e,t){e.exports=v.HmacMD5}))),S=y((function(e,t){e.exports=v.enc.Utf8})),k=y((function(e,t){var n,s,o;e.exports=(o=(s=n=v).lib.WordArray,s.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,s=this._map;e.clamp();for(var o=[],r=0;r<n;r+=3)for(var i=(t[r>>>2]>>>24-r%4*8&255)<<16|(t[r+1>>>2]>>>24-(r+1)%4*8&255)<<8|t[r+2>>>2]>>>24-(r+2)%4*8&255,a=0;a<4&&r+.75*a<n;a++)o.push(s.charAt(i>>>6*(3-a)&63));var c=s.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,n=this._map,s=this._reverseMap;if(!s){s=this._reverseMap=[];for(var r=0;r<n.length;r++)s[n.charCodeAt(r)]=r}var i=n.charAt(64);if(i){var a=e.indexOf(i);-1!==a&&(t=a)}return function(e,t,n){for(var s=[],r=0,i=0;i<t;i++)if(i%4){var a=n[e.charCodeAt(i-1)]<<i%4*2,c=n[e.charCodeAt(i)]>>>6-i%4*2;s[r>>>2]|=(a|c)<<24-r%4*8,r++}return o.create(s,r)}(e,t,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},n.enc.Base64)}));const b="uni_id_token",T="uni_id_token_expired",I="FUNCTION",A="OBJECT",P="CLIENT_DB",x="pending",E="rejected";function C(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function O(e){return"object"===C(e)}function N(e){return"function"==typeof e}function L(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}const U="REJECTED",D="NOT_PENDING";class R{constructor({createPromise:e,retryRule:t=U}={}){this.createPromise=e,this.status=null,this.promise=null,this.retryRule=t}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case U:return this.status===E;case D:return this.status!==x}}exec(){return this.needRetry?(this.status=x,this.promise=this.createPromise().then((e=>(this.status="fulfilled",Promise.resolve(e))),(e=>(this.status=E,Promise.reject(e)))),this.promise):this.promise}}function M(e){return e&&"string"==typeof e?JSON.parse(e):e}const q=M([]);M("");const F=M('[{"provider":"aliyun","spaceName":"suakitsu","spaceId":"mp-2e6e8eae-0872-49fd-b869-9ea34baed9f1","clientSecret":"nJUVHv9y7feXJzGSaPlaVg==","endpoint":"https://api.next.bspapp.com"}]')||[];let j="";try{j="__UNI__1F0BDA5"}catch(Ee){}let V,B={};function $(e,t={}){var n,s;return n=B,s=e,Object.prototype.hasOwnProperty.call(n,s)||(B[e]=t),B[e]}B=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={};const K=["invoke","success","fail","complete"],z=$("_globalUniCloudInterceptor");function H(e,t){z[e]||(z[e]={}),O(t)&&Object.keys(t).forEach((n=>{K.indexOf(n)>-1&&function(e,t,n){let s=z[e][t];s||(s=z[e][t]=[]),-1===s.indexOf(n)&&N(n)&&s.push(n)}(e,n,t[n])}))}function J(e,t){z[e]||(z[e]={}),O(t)?Object.keys(t).forEach((n=>{K.indexOf(n)>-1&&function(e,t,n){const s=z[e][t];if(!s)return;const o=s.indexOf(n);o>-1&&s.splice(o,1)}(e,n,t[n])})):delete z[e]}function W(e,t){return e&&0!==e.length?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function G(e,t){return z[e]&&z[e][t]||[]}function Q(e){H("callObject",e)}const Y=$("_globalUniCloudListener"),X="response",Z="needLogin",ee="refreshToken",te="clientdb",ne="cloudfunction",se="cloudobject";function oe(e){return Y[e]||(Y[e]=[]),Y[e]}function re(e,t){const n=oe(e);n.includes(t)||n.push(t)}function ie(e,t){const n=oe(e),s=n.indexOf(t);-1!==s&&n.splice(s,1)}function ae(e,t){const n=oe(e);for(let s=0;s<n.length;s++)(0,n[s])(t)}let ce,le=!1;function ue(){return ce||(ce=new Promise((e=>{le&&e(),function t(){if("function"==typeof getCurrentPages){const t=getCurrentPages();t&&t[0]&&(le=!0,e())}le||setTimeout((()=>{t()}),30)}()})),ce)}function de(e){const t={};for(const n in e){const s=e[n];N(s)&&(t[n]=L(s))}return t}class he extends Error{constructor(e){super(e.message),this.errMsg=e.message||e.errMsg||"unknown system error",this.code=this.errCode=e.code||e.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=e.subject||e.errSubject,this.cause=e.cause,this.requestId=e.requestId}toJson(e=0){if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}var pe={request:e=>uni.request(e),uploadFile:e=>uni.uploadFile(e),setStorageSync:(e,t)=>uni.setStorageSync(e,t),getStorageSync:e=>uni.getStorageSync(e),removeStorageSync:e=>uni.removeStorageSync(e),clearStorageSync:()=>uni.clearStorageSync(),connectSocket:e=>uni.connectSocket(e)};function ge(e){return e&&ge(e.__v_raw)||e}function fe(){return{token:pe.getStorageSync(b)||pe.getStorageSync("uniIdToken"),tokenExpired:pe.getStorageSync(T)}}function me({token:e,tokenExpired:t}={}){e&&pe.setStorageSync(b,e),t&&pe.setStorageSync(T,t)}let ye,_e;function ve(){return ye||(ye=uni.getSystemInfoSync()),ye}function we(){let e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;const{scene:n,channel:s}=uni.getLaunchOptionsSync();e=s,t=n}}catch(n){}return{channel:e,scene:t}}let Se={};function ke(){const e=uni.getLocale&&uni.getLocale()||"en";if(_e)return{...Se,..._e,locale:e,LOCALE:e};const t=ve(),{deviceId:n,osName:s,uniPlatform:o,appId:r}=t,i=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(const a in t)Object.hasOwnProperty.call(t,a)&&-1===i.indexOf(a)&&delete t[a];return _e={PLATFORM:o,OS:s,APPID:r,DEVICEID:n,...we(),...t},{...Se,..._e,locale:e,LOCALE:e}}var be=function(e,t){let n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),w(n,t).toString()},Te=function(e,t){return new Promise(((n,s)=>{t(Object.assign(e,{complete(e){e||(e={});const t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){const n=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",o=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return s(new he({code:n,message:o,requestId:t}))}const o=e.data;if(o.error)return s(new he({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,n(o)}}))}))},Ie=function(e){return k.stringify(S.parse(e))},Ae={init(e){const t=new class{constructor(e){["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),this.config=Object.assign({},{endpoint:0===e.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=pe,this._getAccessTokenPromiseHub=new R({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((e=>{if(!e.result||!e.result.accessToken)throw new he({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(e.result.accessToken)})),retryRule:D})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return Te(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then((()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch((t=>new Promise(((e,n)=>{!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((()=>this.getAccessToken())).then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)})))):this.getAccessToken().then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)}))))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=be(t.data,this.config.clientSecret),t}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),s={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,s["x-basement-token"]=this.accessToken),s["x-serverless-sign"]=be(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:s}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request({...this.setupRequest(t),timeout:e.timeout})}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:n,filePath:s,fileType:o,onUploadProgress:r}){return new Promise(((i,a)=>{const c=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:s,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success(e){e&&e.statusCode<400?i(e):a(new he({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){a(new he({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof r&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{r({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:n="image",cloudPathAsRealPath:s=!1,onUploadProgress:o,config:r}){if("string"!==C(t))throw new he({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new he({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new he({code:"INVALID_PARAM",message:"cloudPath不合法"});const i=r&&r.envType||this.config.envType;if(s&&("/"!==t[0]&&(t="/"+t),t.indexOf("\\")>-1))throw new he({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const a=(await this.getOSSUploadOptionsFromPath({env:i,filename:s?t.split("/").pop():t,fileId:s?t:void 0})).result,c="https://"+a.cdnDomain+"/"+a.ossPath,{securityToken:l,accessKeyId:u,signature:d,host:h,ossPath:p,id:g,policy:f,ossCallbackUrl:m}=a,y={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:u,Signature:d,host:h,id:g,key:p,policy:f,success_action_status:200};if(l&&(y["x-oss-security-token"]=l),m){const e=JSON.stringify({callbackUrl:m,callbackBody:JSON.stringify({fileId:g,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});y.callback=Ie(e)}const _={url:"https://"+a.host,formData:y,fileName:"file",name:"file",filePath:e,fileType:n};if(await this.uploadFileToOSS(Object.assign({},_,{onUploadProgress:o})),m)return{success:!0,filePath:e,fileID:c};if((await this.reportOSSUpload({id:g})).success)return{success:!0,filePath:e,fileID:c};throw new he({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise(((t,n)=>{Array.isArray(e)&&0!==e.length||n(new he({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map((e=>({fileID:e,tempFileURL:e})))})}))}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||0===e.length)throw new he({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map((e=>e.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}}(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}};const Pe="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var xe,Ee;(Ee=xe||(xe={})).local="local",Ee.none="none",Ee.session="session";var Ce=function(){},Oe=y((function(e,t){var n;e.exports=(n=v,function(e){var t=n,s=t.lib,o=s.WordArray,r=s.Hasher,i=t.algo,a=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),s=2;s<=n;s++)if(!(t%s))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var s=2,o=0;o<64;)t(s)&&(o<8&&(a[o]=n(e.pow(s,.5))),c[o]=n(e.pow(s,1/3)),o++),s++}();var l=[],u=i.SHA256=r.extend({_doReset:function(){this._hash=new o.init(a.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,s=n[0],o=n[1],r=n[2],i=n[3],a=n[4],u=n[5],d=n[6],h=n[7],p=0;p<64;p++){if(p<16)l[p]=0|e[t+p];else{var g=l[p-15],f=(g<<25|g>>>7)^(g<<14|g>>>18)^g>>>3,m=l[p-2],y=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[p]=f+l[p-7]+y+l[p-16]}var _=s&o^s&r^o&r,v=(s<<30|s>>>2)^(s<<19|s>>>13)^(s<<10|s>>>22),w=h+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&u^~a&d)+c[p]+l[p];h=d,d=u,u=a,a=i+w|0,i=r,r=o,o=s,s=w+(v+_)|0}n[0]=n[0]+s|0,n[1]=n[1]+o|0,n[2]=n[2]+r|0,n[3]=n[3]+i|0,n[4]=n[4]+a|0,n[5]=n[5]+u|0,n[6]=n[6]+d|0,n[7]=n[7]+h|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,o=8*t.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(o+64>>>9<<4)]=e.floor(s/4294967296),n[15+(o+64>>>9<<4)]=s,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=r._createHelper(u),t.HmacSHA256=r._createHmacHelper(u)}(Math),n.SHA256)})),Ne=Oe,Le=y((function(e,t){e.exports=v.HmacSHA256}));const Ue=()=>{let e;if(!Promise){e=()=>{},e.promise={};const t=()=>{throw new he({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}const t=new Promise(((t,n)=>{e=(e,s)=>e?n(e):t(s)}));return e.promise=t,e};function De(e){return void 0===e}function Re(e){return"[object Null]"===Object.prototype.toString.call(e)}function Me(e=""){return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function qe(e=32){let t="";for(let n=0;n<e;n++)t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(Math.floor(62*Math.random()));return t}var Fe;!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Fe||(Fe={}));const je={adapter:null,runtime:void 0},Ve=["anonymousUuidKey"];class Be extends Ce{constructor(){super(),je.adapter.root.tcbObject||(je.adapter.root.tcbObject={})}setItem(e,t){je.adapter.root.tcbObject[e]=t}getItem(e){return je.adapter.root.tcbObject[e]}removeItem(e){delete je.adapter.root.tcbObject[e]}clear(){delete je.adapter.root.tcbObject}}function $e(e,t){switch(e){case"local":return t.localStorage||new Be;case"none":return new Be;default:return t.sessionStorage||new Be}}class Ke{constructor(e){if(!this._storage){this._persistence=je.adapter.primaryStorage||e.persistence,this._storage=$e(this._persistence,je.adapter);const t=`access_token_${e.env}`,n=`access_token_expire_${e.env}`,s=`refresh_token_${e.env}`,o=`anonymous_uuid_${e.env}`,r=`login_type_${e.env}`,i="device_id",a=`token_type_${e.env}`,c=`user_info_${e.env}`;this.keys={accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s,anonymousUuidKey:o,loginTypeKey:r,userInfoKey:c,deviceIdKey:i,tokenTypeKey:a}}}updatePersistence(e){if(e===this._persistence)return;const t="local"===this._persistence;this._persistence=e;const n=$e(e,je.adapter);for(const s in this.keys){const e=this.keys[s];if(t&&Ve.includes(s))continue;const o=this._storage.getItem(e);De(o)||Re(o)||(n.setItem(e,o),this._storage.removeItem(e))}this._storage=n}setStore(e,t,n){if(!this._storage)return;const s={version:n||"localCachev1",content:t},o=JSON.stringify(s);try{this._storage.setItem(e,o)}catch(r){throw r}}getStore(e,t){try{if(!this._storage)return}catch(s){return""}t=t||"localCachev1";const n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}removeStore(e){this._storage.removeItem(e)}}const ze={},He={};function Je(e){return ze[e]}class We{constructor(e,t){this.data=t||null,this.name=e}}class Ge extends We{constructor(e,t){super("error",{error:e,data:t}),this.error=e}}const Qe=new class{constructor(){this._listeners={}}on(e,t){return n=e,s=t,(o=this._listeners)[n]=o[n]||[],o[n].push(s),this;var n,s,o}off(e,t){return function(e,t,n){if(n&&n[e]){const s=n[e].indexOf(t);-1!==s&&n[e].splice(s,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof Ge)return console.error(e.error),this;const n="string"==typeof e?new We(e,t||{}):e,s=n.name;if(this._listens(s)){n.target=this;const e=this._listeners[s]?[...this._listeners[s]]:[];for(const t of e)t.call(this,n)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function Ye(e,t){Qe.on(e,t)}function Xe(e,t={}){Qe.fire(e,t)}function Ze(e,t){Qe.off(e,t)}const et="loginStateChanged",tt="loginStateExpire",nt="loginTypeChanged",st="anonymousConverted",ot="refreshAccessToken";var rt;!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(rt||(rt={}));class it{constructor(){this._fnPromiseMap=new Map}async run(e,t){let n=this._fnPromiseMap.get(e);return n||(n=new Promise((async(n,s)=>{try{await this._runIdlePromise();const s=t();n(await s)}catch(o){s(o)}finally{this._fnPromiseMap.delete(e)}})),this._fnPromiseMap.set(e,n)),n}_runIdlePromise(){return Promise.resolve()}}class at{constructor(e){this._singlePromise=new it,this._cache=Je(e.env),this._baseURL=`https://${e.env}.ap-shanghai.tcb-api.tencentcloudapi.com`,this._reqClass=new je.adapter.reqClass({timeout:e.timeout,timeoutMsg:`请求在${e.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]})}_getDeviceId(){if(this._deviceID)return this._deviceID;const{deviceIdKey:e}=this._cache.keys;let t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=qe(),this._cache.setStore(e,t)),this._deviceID=t,t}async _request(e,t,n={}){const s={"x-request-id":qe(),"x-device-id":this._getDeviceId()};if(n.withAccessToken){const{tokenTypeKey:e}=this._cache.keys,t=await this.getAccessToken(),n=this._cache.getStore(e);s.authorization=`${n} ${t}`}return this._reqClass["get"===n.method?"get":"post"]({url:`${this._baseURL}${e}`,data:t,headers:s})}async _fetchAccessToken(){const{loginTypeKey:e,accessTokenKey:t,accessTokenExpireKey:n,tokenTypeKey:s}=this._cache.keys,o=this._cache.getStore(e);if(o&&o!==rt.ANONYMOUS)throw new he({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});const r=await this._singlePromise.run("fetchAccessToken",(async()=>(await this._request("/auth/v1/signin/anonymously",{},{method:"post"})).data)),{access_token:i,expires_in:a,token_type:c}=r;return this._cache.setStore(s,c),this._cache.setStore(t,i),this._cache.setStore(n,Date.now()+1e3*a),i}isAccessTokenExpired(e,t){let n=!0;return e&&t&&(n=t<Date.now()),n}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),s=this._cache.getStore(t);return this.isAccessTokenExpired(n,s)?this._fetchAccessToken():n}async refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,loginTypeKey:n}=this._cache.keys;return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.setStore(n,rt.ANONYMOUS),this.getAccessToken()}async getUserInfo(){return this._singlePromise.run("getUserInfo",(async()=>(await this._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"})).data))}}const ct=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],lt={"X-SDK-Version":"1.3.5"};function ut(e,t,n){const s=e[t];e[t]=function(t){const o={},r={};n.forEach((n=>{const{data:s,headers:i}=n.call(e,t);Object.assign(o,s),Object.assign(r,i)}));const i=t.data;return i&&(()=>{var e;if(e=i,"[object FormData]"!==Object.prototype.toString.call(e))t.data={...i,...o};else for(const t in o)i.append(t,o[t])})(),t.headers={...t.headers||{},...r},s.call(e,t)}}function dt(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...lt,"x-seqid":e}}}class ht{constructor(e={}){var t;this.config=e,this._reqClass=new je.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=Je(this.config.env),this._localCache=(t=this.config.env,He[t]),this.oauth=new at(this.config),ut(this._reqClass,"post",[dt]),ut(this._reqClass,"upload",[dt]),ut(this._reqClass,"download",[dt])}async post(e){return await this._reqClass.post(e)}async upload(e){return await this._reqClass.upload(e)}async download(e){return await this._reqClass.download(e)}async refreshAccessToken(){let e,t;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{e=await this._refreshAccessTokenPromise}catch(n){t=n}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,t)throw t;return e}async _refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n,loginTypeKey:s,anonymousUuidKey:o}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(t);let r=this._cache.getStore(n);if(!r)throw new he({message:"未登录CloudBase"});const i={refresh_token:r},a=await this.request("auth.fetchAccessTokenWithRefreshToken",i);if(a.data.code){const{code:e}=a.data;if("SIGN_PARAM_INVALID"===e||"REFRESH_TOKEN_EXPIRED"===e||"INVALID_REFRESH_TOKEN"===e){if(this._cache.getStore(s)===rt.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===e){const e=this._cache.getStore(o),t=this._cache.getStore(n),s=await this.send("auth.signInAnonymously",{anonymous_uuid:e,refresh_token:t});return this.setRefreshToken(s.refresh_token),this._refreshAccessToken()}Xe(tt),this._cache.removeStore(n)}throw new he({code:a.data.code,message:`刷新access token失败：${a.data.code}`})}if(a.data.access_token)return Xe(ot),this._cache.setStore(e,a.data.access_token),this._cache.setStore(t,a.data.access_token_expire+Date.now()),{accessToken:a.data.access_token,accessTokenExpire:a.data.access_token_expire};a.data.refresh_token&&(this._cache.removeStore(n),this._cache.setStore(n,a.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n}=this._cache.keys;if(!this._cache.getStore(n))throw new he({message:"refresh token不存在，登录状态异常"});let s=this._cache.getStore(e),o=this._cache.getStore(t),r=!0;return this._shouldRefreshAccessTokenHook&&!(await this._shouldRefreshAccessTokenHook(s,o))&&(r=!1),(!s||!o||o<Date.now())&&r?this.refreshAccessToken():{accessToken:s,accessTokenExpire:o}}async request(e,t,n){const s=`x-tcb-trace_${this.config.env}`;let o="application/x-www-form-urlencoded";const r={action:e,env:this.config.env,dataVersion:"2019-08-16",...t};let i;if(-1===ct.indexOf(e)&&(this._cache.keys,r.access_token=await this.oauth.getAccessToken()),"storage.uploadFile"===e){i=new FormData;for(let e in i)i.hasOwnProperty(e)&&void 0!==i[e]&&i.append(e,r[e]);o="multipart/form-data"}else{o="application/json",i={};for(let e in r)void 0!==r[e]&&(i[e]=r[e])}let a={headers:{"content-type":o}};n&&n.timeout&&(a.timeout=n.timeout),n&&n.onUploadProgress&&(a.onUploadProgress=n.onUploadProgress);const c=this._localCache.getStore(s);c&&(a.headers["X-TCB-Trace"]=c);const{parse:l,inQuery:u,search:d}=t;let h={env:this.config.env};l&&(h.parse=!0),u&&(h={...u,...h});let p=function(e,t,n={}){const s=/\?/.test(t);let o="";for(let r in n)""===o?!s&&(t+="?"):o+="&",o+=`${r}=${encodeURIComponent(n[r])}`;return/^http(s)?\:\/\//.test(t+=o)?t:`${e}${t}`}(Pe,"//tcb-api.tencentcloudapi.com/web",h);d&&(p+=d);const g=await this.post({url:p,data:i,...a}),f=g.header&&g.header["x-tcb-trace"];if(f&&this._localCache.setStore(s,f),200!==Number(g.status)&&200!==Number(g.statusCode)||!g.data)throw new he({code:"NETWORK_ERROR",message:"network request error"});return g}async send(e,t={},n={}){const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(("ACCESS_TOKEN_DISABLED"===s.data.code||"ACCESS_TOKEN_EXPIRED"===s.data.code)&&-1===ct.indexOf(e)){await this.oauth.refreshAccessToken();const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(s.data.code)throw new he({code:s.data.code,message:Me(s.data.message)});return s.data}if(s.data.code)throw new he({code:s.data.code,message:Me(s.data.message)});return s.data}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}}const pt={};function gt(e){return pt[e]}class ft{constructor(e){this.config=e,this._cache=Je(e.env),this._request=gt(e.env)}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}setAccessToken(e,t){const{accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys;this._cache.setStore(n,e),this._cache.setStore(s,t)}async refreshUserInfo(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e)}}class mt{constructor(e){if(!e)throw new he({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=e,this._cache=Je(this._envId),this._request=gt(this._envId),this.setUserInfo()}linkWithTicket(e){if("string"!=typeof e)throw new he({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}linkWithRedirect(e){e.signInWithRedirect()}updatePassword(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}updateEmail(e){return this._request.send("auth.updateEmail",{newEmail:e})}updateUsername(e){if("string"!=typeof e)throw new he({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}async getLinkedUidList(){const{data:e}=await this._request.send("auth.getLinkedUidList",{});let t=!1;const{users:n}=e;return n.forEach((e=>{e.wxOpenId&&e.wxPublicId&&(t=!0)})),{users:n,hasPrimaryUid:t}}setPrimaryUid(e){return this._request.send("auth.setPrimaryUid",{uid:e})}unlink(e){return this._request.send("auth.unlink",{platform:e})}async update(e){const{nickName:t,gender:n,avatarUrl:s,province:o,country:r,city:i}=e,{data:a}=await this._request.send("auth.updateUserInfo",{nickName:t,gender:n,avatarUrl:s,province:o,country:r,city:i});this.setLocalUserInfo(a)}async refresh(){const e=await this._request.oauth.getUserInfo();return this.setLocalUserInfo(e),e}setUserInfo(){const{userInfoKey:e}=this._cache.keys,t=this._cache.getStore(e);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((e=>{this[e]=t[e]})),this.location={country:t.country,province:t.province,city:t.city}}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e),this.setUserInfo()}}class yt{constructor(e){if(!e)throw new he({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Je(e);const{refreshTokenKey:t,accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys,o=this._cache.getStore(t),r=this._cache.getStore(n),i=this._cache.getStore(s);this.credential={refreshToken:o,accessToken:r,accessTokenExpire:i},this.user=new mt(e)}get isAnonymousAuth(){return this.loginType===rt.ANONYMOUS}get isCustomAuth(){return this.loginType===rt.CUSTOM}get isWeixinAuth(){return this.loginType===rt.WECHAT||this.loginType===rt.WECHAT_OPEN||this.loginType===rt.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class _t extends ft{async signIn(){this._cache.updatePersistence("local"),await this._request.oauth.getAccessToken(),Xe(et),Xe(nt,{env:this.config.env,loginType:rt.ANONYMOUS,persistence:"local"});const e=new yt(this.config.env);return await e.user.refresh(),e}async linkAndRetrieveDataWithTicket(e){const{anonymousUuidKey:t,refreshTokenKey:n}=this._cache.keys,s=this._cache.getStore(t),o=this._cache.getStore(n),r=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:s,refresh_token:o,ticket:e});if(r.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(r.refresh_token),await this._request.refreshAccessToken(),Xe(st,{env:this.config.env}),Xe(nt,{loginType:rt.CUSTOM,persistence:"local"}),{credential:{refreshToken:r.refresh_token}};throw new he({message:"匿名转化失败"})}_setAnonymousUUID(e){const{anonymousUuidKey:t,loginTypeKey:n}=this._cache.keys;this._cache.removeStore(t),this._cache.setStore(t,e),this._cache.setStore(n,rt.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class vt extends ft{async signIn(e){if("string"!=typeof e)throw new he({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:t}=this._cache.keys,n=await this._request.send("auth.signInWithTicket",{ticket:e,refresh_token:this._cache.getStore(t)||""});if(n.refresh_token)return this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),Xe(et),Xe(nt,{env:this.config.env,loginType:rt.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new yt(this.config.env);throw new he({message:"自定义登录失败"})}}class wt extends ft{async signIn(e,t){if("string"!=typeof e)throw new he({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:"EMAIL",email:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token:r,access_token_expire:i}=s;if(o)return this.setRefreshToken(o),r&&i?this.setAccessToken(r,i):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Xe(et),Xe(nt,{env:this.config.env,loginType:rt.EMAIL,persistence:this.config.persistence}),new yt(this.config.env);throw s.code?new he({code:s.code,message:`邮箱登录失败: ${s.message}`}):new he({message:"邮箱登录失败"})}async activate(e){return this._request.send("auth.activateEndUserMail",{token:e})}async resetPasswordWithToken(e,t){return this._request.send("auth.resetPasswordWithToken",{token:e,newPassword:t})}}class St extends ft{async signIn(e,t){if("string"!=typeof e)throw new he({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof t&&(t="",console.warn("password is empty"));const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:rt.USERNAME,username:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token_expire:r,access_token:i}=s;if(o)return this.setRefreshToken(o),i&&r?this.setAccessToken(i,r):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Xe(et),Xe(nt,{env:this.config.env,loginType:rt.USERNAME,persistence:this.config.persistence}),new yt(this.config.env);throw s.code?new he({code:s.code,message:`用户名密码登录失败: ${s.message}`}):new he({message:"用户名密码登录失败"})}}class kt{constructor(e){this.config=e,this._cache=Je(e.env),this._request=gt(e.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Ye(nt,this._onLoginTypeChanged)}get currentUser(){const e=this.hasLoginState();return e&&e.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new _t(this.config)}customAuthProvider(){return new vt(this.config)}emailAuthProvider(){return new wt(this.config)}usernameAuthProvider(){return new St(this.config)}async signInAnonymously(){return new _t(this.config).signIn()}async signInWithEmailAndPassword(e,t){return new wt(this.config).signIn(e,t)}signInWithUsernameAndPassword(e,t){return new St(this.config).signIn(e,t)}async linkAndRetrieveDataWithTicket(e){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new _t(this.config)),Ye(st,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(e)}async signOut(){if(this.loginType===rt.ANONYMOUS)throw new he({message:"匿名用户不支持登出操作"});const{refreshTokenKey:e,accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,s=this._cache.getStore(e);if(!s)return;const o=await this._request.send("auth.logout",{refresh_token:s});return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.removeStore(n),Xe(et),Xe(nt,{env:this.config.env,loginType:rt.NULL,persistence:this.config.persistence}),o}async signUpWithEmailAndPassword(e,t){return this._request.send("auth.signUpWithEmailAndPassword",{email:e,password:t})}async sendPasswordResetEmail(e){return this._request.send("auth.sendPasswordResetEmail",{email:e})}onLoginStateChanged(e){Ye(et,(()=>{const t=this.hasLoginState();e.call(this,t)}));const t=this.hasLoginState();e.call(this,t)}onLoginStateExpired(e){Ye(tt,e.bind(this))}onAccessTokenRefreshed(e){Ye(ot,e.bind(this))}onAnonymousConverted(e){Ye(st,e.bind(this))}onLoginTypeChanged(e){Ye(nt,(()=>{const t=this.hasLoginState();e.call(this,t)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),s=this._cache.getStore(t);return this._request.oauth.isAccessTokenExpired(n,s)?null:new yt(this.config.env)}async isUsernameRegistered(e){if("string"!=typeof e)throw new he({code:"PARAM_ERROR",message:"username must be a string"});const{data:t}=await this._request.send("auth.isUsernameRegistered",{username:e});return t&&t.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(e){return new vt(this.config).signIn(e)}shouldRefreshAccessToken(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((e=>e.code?e:{...e.data,requestId:e.seqId}))}getAuthHeader(){const{refreshTokenKey:e,accessTokenKey:t}=this._cache.keys,n=this._cache.getStore(e);return{"x-cloudbase-credentials":this._cache.getStore(t)+"/@@/"+n}}_onAnonymousConverted(e){const{env:t}=e.data;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(e){const{loginType:t,persistence:n,env:s}=e.data;s===this.config.env&&(this._cache.updatePersistence(n),this._cache.setStore(this._cache.keys.loginTypeKey,t))}}const bt=function(e,t){t=t||Ue();const n=gt(this.config.env),{cloudPath:s,filePath:o,onUploadProgress:r,fileType:i="image"}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{const{data:{url:a,authorization:c,token:l,fileId:u,cosFileId:d},requestId:h}=e,p={key:s,signature:c,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":l};n.upload({url:a,data:p,file:o,name:s,fileType:i,onUploadProgress:r}).then((e=>{201===e.statusCode?t(null,{fileID:u,requestId:h}):t(new he({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${e.data}`}))})).catch((e=>{t(e)}))})).catch((e=>{t(e)})),t.promise},Tt=function(e,t){t=t||Ue();const n=gt(this.config.env),{cloudPath:s}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{t(null,e)})).catch((e=>{t(e)})),t.promise},It=function({fileList:e},t){if(t=t||Ue(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let s of e)if(!s||"string"!=typeof s)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const n={fileid_list:e};return gt(this.config.env).send("storage.batchDeleteFile",n).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},At=function({fileList:e},t){t=t||Ue(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let n=[];for(let o of e)"object"==typeof o?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),n.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?n.push({fileid:o}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const s={file_list:n};return gt(this.config.env).send("storage.batchGetDownloadUrl",s).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},Pt=async function({fileID:e},t){const n=(await At.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if("SUCCESS"!==n.code)return t?t(n):new Promise((e=>{e(n)}));const s=gt(this.config.env);let o=n.download_url;if(o=encodeURI(o),!t)return s.download({url:o});t(await s.download({url:o}))},xt=function({name:e,data:t,query:n,parse:s,search:o,timeout:r},i){const a=i||Ue();let c;try{c=t?JSON.stringify(t):""}catch(u){return Promise.reject(u)}if(!e)return Promise.reject(new he({code:"PARAM_ERROR",message:"函数名不能为空"}));const l={inQuery:n,parse:s,search:o,function_name:e,request_data:c};return gt(this.config.env).send("functions.invokeFunction",l,{timeout:r}).then((e=>{if(e.code)a(null,e);else{let n=e.data.response_data;if(s)a(null,{result:n,requestId:e.requestId});else try{n=JSON.parse(e.data.response_data),a(null,{result:n,requestId:e.requestId})}catch(t){a(new he({message:"response data must be json"}))}}return a.promise})).catch((e=>{a(e)})),a.promise},Et={timeout:15e3,persistence:"session"},Ct=6e5,Ot={};class Nt{constructor(e){this.config=e||this.config,this.authObj=void 0}init(e){switch(je.adapter||(this.requestClient=new je.adapter.reqClass({timeout:e.timeout||5e3,timeoutMsg:`请求在${(e.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...Et,...e},!0){case this.config.timeout>Ct:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=Ct;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new Nt(this.config)}auth({persistence:e}={}){if(this.authObj)return this.authObj;const t=e||je.adapter.primaryStorage||Et.persistence;var n;return t!==this.config.persistence&&(this.config.persistence=t),function(e){const{env:t}=e;ze[t]=new Ke(e),He[t]=new Ke({...e,persistence:"local"})}(this.config),n=this.config,pt[n.env]=new ht(n),this.authObj=new kt(this.config),this.authObj}on(e,t){return Ye.apply(this,[e,t])}off(e,t){return Ze.apply(this,[e,t])}callFunction(e,t){return xt.apply(this,[e,t])}deleteFile(e,t){return It.apply(this,[e,t])}getTempFileURL(e,t){return At.apply(this,[e,t])}downloadFile(e,t){return Pt.apply(this,[e,t])}uploadFile(e,t){return bt.apply(this,[e,t])}getUploadMetadata(e,t){return Tt.apply(this,[e,t])}registerExtension(e){Ot[e.name]=e}async invokeExtension(e,t){const n=Ot[e];if(!n)throw new he({message:`扩展${e} 必须先注册`});return await n.invoke(t,this)}useAdapters(e){const{adapter:t,runtime:n}=function(e){const t=(n=e,"[object Array]"===Object.prototype.toString.call(n)?e:[e]);var n;for(const s of t){const{isMatch:e,genAdapter:t,runtime:n}=s;if(e())return{adapter:t(),runtime:n}}}(e)||{};t&&(je.adapter=t),n&&(je.runtime=n)}}var Lt=new Nt;function Ut(e,t,n){void 0===n&&(n={});var s=/\?/.test(t),o="";for(var r in n)""===o?!s&&(t+="?"):o+="&",o+=r+"="+encodeURIComponent(n[r]);return/^http(s)?:\/\//.test(t+=o)?t:""+e+t}class Dt{get(e){const{url:t,data:n,headers:s,timeout:o}=e;return new Promise(((e,r)=>{pe.request({url:Ut("https:",t),data:n,method:"GET",header:s,timeout:o,success(t){e(t)},fail(e){r(e)}})}))}post(e){const{url:t,data:n,headers:s,timeout:o}=e;return new Promise(((e,r)=>{pe.request({url:Ut("https:",t),data:n,method:"POST",header:s,timeout:o,success(t){e(t)},fail(e){r(e)}})}))}upload(e){return new Promise(((t,n)=>{const{url:s,file:o,data:r,headers:i,fileType:a}=e,c=pe.uploadFile({url:Ut("https:",s),name:"file",formData:Object.assign({},r),filePath:o,fileType:a,header:i,success(e){const n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&r.success_action_status&&(n.statusCode=parseInt(r.success_action_status,10)),t(n)},fail(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((t=>{e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}const Rt={setItem(e,t){pe.setStorageSync(e,t)},getItem:e=>pe.getStorageSync(e),removeItem(e){pe.removeStorageSync(e)},clear(){pe.clearStorageSync()}};var Mt={genAdapter:function(){return{root:{},reqClass:Dt,localStorage:Rt,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Lt.useAdapters(Mt);const qt=Lt,Ft=qt.init;qt.init=function(e){e.env=e.spaceId;const t=Ft.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const n=t.auth;return t.auth=function(e){const t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((e=>{var n;t[e]=(n=t[e],function(e){e=e||{};const{success:t,fail:s,complete:o}=de(e);if(!(t||s||o))return n.call(this,e);n.call(this,e).then((e=>{t&&t(e),o&&o(e)}),(e=>{s&&s(e),o&&o(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var jt=qt;async function Vt(e,t){const n=`http://${e}:${t}/system/ping`;try{const e=await(s={url:n,timeout:500},new Promise(((e,t)=>{pe.request({...s,success(t){e(t)},fail(e){t(e)}})})));return!(!e.data||0!==e.data.code)}catch(o){return!1}var s}const Bt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"};var $t={init(e){const t=new class{constructor(e){if(["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),!e.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},e),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=pe}async request(e,t=!0){return e=this.setupRequest(e),Promise.resolve().then((()=>Te(e,this.adapter.request)))}requestLocal(e){return new Promise(((t,n)=>{this.adapter.request(Object.assign(e,{complete(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){const t=e.data&&e.data.code||"SYS_ERR",s=e.data&&e.data.message||"request:fail";return n(new he({code:t,message:s}))}t({success:!0,result:e.data})}}))}))}setupRequest(e){const t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=be(t,this.config.clientSecret);const s=ke();n["x-client-info"]=encodeURIComponent(JSON.stringify(s));const{token:o}=fe();return n["x-client-token"]=o,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}async setupLocalRequest(e){const t=ke(),{token:n}=fe(),s=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:t,token:n}),{address:o,servePort:r}=this.__dev__&&this.__dev__.debugInfo||{},{address:i}=await async function(e,t){let n;for(let s=0;s<e.length;s++){const o=e[s];if(await Vt(o,t)){n=o;break}}return{address:n,port:t}}(o,r);return{url:`http://${i}:${r}/${Bt[e.method]}`,method:"POST",data:s,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))}}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}getUploadFileOptions(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}reportUploadFile(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}uploadFile({filePath:e,cloudPath:t,fileType:n="image",onUploadProgress:s}){if(!t)throw new he({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let o;return this.getUploadFileOptions({cloudPath:t}).then((t=>{const{url:r,formData:i,name:a}=t.result;return o=t.result.fileUrl,new Promise(((t,o)=>{const c=this.adapter.uploadFile({url:r,formData:i,name:a,filePath:e,fileType:n,success(e){e&&e.statusCode<400?t(e):o(new he({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){o(new he({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((()=>this.reportUploadFile({cloudPath:t}))).then((t=>new Promise(((n,s)=>{t.success?n({success:!0,filePath:e,fileID:o}):s(new he({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(t).then((e=>{if(e.success)return e.result;throw new he({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||0===e.length)throw new he({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(n).then((e=>{if(e.success)return{fileList:e.result.fileList.map((e=>({fileID:e.fileID,tempFileURL:e.tempFileURL})))};throw new he({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Kt=y((function(e,t){e.exports=v.enc.Hex}));function zt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Ht(e="",t={}){const{data:n,functionName:s,method:o,headers:r,signHeaderKeys:i=[],config:a}=t,c=String(Date.now()),l=zt(),u=Object.assign({},r,{"x-from-app-id":a.spaceAppId,"x-from-env-id":a.spaceId,"x-to-env-id":a.spaceId,"x-from-instance-id":c,"x-from-function-name":s,"x-client-timestamp":c,"x-alipay-source":"client","x-request-id":l,"x-alipay-callid":l,"x-trace-id":l}),d=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(i),[h="",p=""]=e.split("?")||[],g=function(e){const t="HMAC-SHA256",n=e.signedHeaders.join(";"),s=e.signedHeaders.map((t=>`${t.toLowerCase()}:${e.headers[t]}\n`)).join(""),o=Ne(e.body).toString(Kt),r=`${e.method.toUpperCase()}\n${e.path}\n${e.query}\n${s}\n${n}\n${o}\n`,i=Ne(r).toString(Kt),a=`${t}\n${e.timestamp}\n${i}\n`,c=Le(a,e.secretKey).toString(Kt);return`${t} Credential=${e.secretId}, SignedHeaders=${n}, Signature=${c}`}({path:h,query:p,method:o,headers:u,timestamp:c,body:JSON.stringify(n),secretId:a.accessKey,secretKey:a.secretKey,signedHeaders:d.sort()});return{url:`${a.endpoint}${e}`,headers:Object.assign({},u,{Authorization:g})}}function Jt({url:e,data:t,method:n="POST",headers:s={},timeout:o}){return new Promise(((r,i)=>{pe.request({url:e,method:n,data:"object"==typeof t?JSON.stringify(t):t,header:s,dataType:"json",timeout:o,complete:(e={})=>{const t=s["x-trace-id"]||"";if(!e.statusCode||e.statusCode>=400){const{message:n,errMsg:s,trace_id:o}=e.data||{};return i(new he({code:"SYS_ERR",message:n||s||"request:fail",requestId:o||t}))}r({status:e.statusCode,data:e.data,headers:e.header,requestId:t})}})}))}function Wt(e,t){const{path:n,data:s,method:o="GET"}=e,{url:r,headers:i}=Ht(n,{functionName:"",data:s,method:o,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return Jt({url:r,data:s,method:o,headers:i}).then((e=>{const t=e.data||{};if(!t.success)throw new he({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((e=>{throw new he({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Gt(e=""){const t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new he({code:"INVALID_PARAM",message:"fileID不合法"});const s=t.substring(0,n),o=t.substring(n+1);return s!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),o}function Qt(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}class Yt{constructor(e){this.config=e}signedURL(e,t={}){const n=`/ws/function/${e}`,s=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),o=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:zt(),timestamp:""+Date.now()}),r=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return o[e]?"".concat(e,"=").concat(o[e]):null})).filter(Boolean).join("&"),`host:${s}`].join("\n"),i=["HMAC-SHA256",Ne(r).toString(Kt)].join("\n"),a=Le(i,this.config.secretKey).toString(Kt),c=Object.keys(o).map((e=>`${e}=${encodeURIComponent(o[e])}`)).join("&");return`${this.config.wsEndpoint}${n}?${c}&signature=${a}`}}var Xt={init:e=>{e.provider="alipay";const t=new class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),e.endpoint){if("string"!=typeof e.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`,wsEndpoint:e.wsEndpoint||`wss://${e.spaceId}.api-hz.cloudbasefunction.cn`}),this._websocket=new Yt(this.config)}callFunction(e){return function(e,t){const{name:n,data:s,async:o=!1,timeout:r}=e,i="POST",a={"x-to-function-name":n};o&&(a["x-function-invoke-type"]="async");const{url:c,headers:l}=Ht("/functions/invokeFunction",{functionName:n,data:s,method:i,headers:a,signHeaderKeys:["x-to-function-name"],config:t});return Jt({url:c,data:s,method:i,headers:l,timeout:r}).then((e=>{let t=0;if(o){const n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new he({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((e=>{throw new he({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:n,formData:s,onUploadProgress:o}){return new Promise(((r,i)=>{const a=pe.uploadFile({url:e,filePath:t,fileType:n,formData:s,name:"file",success(e){e&&e.statusCode<400?r(e):i(new he({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){i(new he({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&a&&"function"==typeof a.onProgressUpdate&&a.onProgressUpdate((e=>{o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:e,cloudPath:t="",fileType:n="image",onUploadProgress:s}){if("string"!==C(t))throw new he({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new he({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new he({code:"INVALID_PARAM",message:"cloudPath不合法"});const o=await Wt({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:r,upload_url:i,form_data:a}=o,c=a&&a.reduce(((e,t)=>(e[t.key]=t.value,e)),{});return this.uploadFileToOSS({url:i,filePath:e,fileType:n,formData:c,onUploadProgress:s}).then((()=>({fileID:r})))}async getTempFileURL({fileList:e}){return new Promise(((t,n)=>{(!e||e.length<0)&&t({code:"INVALID_PARAM",message:"fileList不能为空数组"}),e.length>50&&t({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});const s=[];for(const r of e){let e;"string"!==C(r)&&t({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{e=Gt.call(this,r)}catch(o){console.warn(o.errCode,o.errMsg),e=r}s.push({file_id:e,expire:600})}Wt({path:"/?download_url",data:{file_list:s},method:"POST"},this.config).then((e=>{const{file_list:n=[]}=e;t({fileList:n.map((e=>({fileID:Qt.call(this,e.file_id),tempFileURL:e.download_url})))})})).catch((e=>n(e)))}))}async connectWebSocket(e){const{name:t,query:n}=e;return pe.connectSocket({url:this._websocket.signedURL(t,n),complete:()=>{}})}}(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function Zt({data:e}){let t;t=ke();const n=JSON.parse(JSON.stringify(e||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){const{token:e}=fe();e&&(n.uniIdToken=e)}return n}const en=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var tn=/[\\^$.*+?()[\]{}|]/g,nn=RegExp(tn.source);function sn(e,t,n){return e.replace(new RegExp((s=t)&&nn.test(s)?s.replace(tn,"\\$&"):s,"g"),n);var s}const on="request",rn="response",an="both",cn="_globalUniCloudStatus",ln={code:2e4,message:"System error"},un={code:20101,message:"Invalid client"};function dn(e){const{errSubject:t,subject:n,errCode:s,errMsg:o,code:r,message:i,cause:a}=e||{};return new he({subject:t||n||"uni-secure-network",code:s||r||ln.code,message:o||i,cause:a})}let hn;function pn({secretType:e}={}){return e===on||e===rn||e===an}function gn({name:e,data:t={}}={}){return"DCloud-clientDB"===e&&"encryption"===t.redirectTo&&"getAppClientKey"===t.action}function fn({functionName:e,result:t,logPvd:n}){}function mn(e){const t=e.callFunction,n=function(n){const s=n.name;n.data=Zt.call(e,{data:n.data});const o={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],r=pn(n),i=gn(n),a=r||i;return t.call(this,n).then((e=>(e.errCode=0,!a&&fn.call(this,{functionName:s,result:e,logPvd:o}),Promise.resolve(e))),(e=>(!a&&fn.call(this,{functionName:s,result:e,logPvd:o}),e&&e.message&&(e.message=function({message:e="",extraInfo:t={},formatter:n=[]}={}){for(let s=0;s<n.length;s++){const{rule:o,content:r,mode:i}=n[s],a=e.match(o);if(!a)continue;let c=r;for(let e=1;e<a.length;e++)c=sn(c,`{$${e}}`,a[e]);for(const e in t)c=sn(c,`{${e}}`,t[e]);return"replace"===i?c:e+c}return e}({message:`[${n.name}]: ${e.message}`,formatter:en,extraInfo:{functionName:s}})),Promise.reject(e))))};e.callFunction=function(t){const{provider:s,spaceId:o}=e.config,r=t.name;let i,a;return t.data=t.data||{},i=n,i=i.bind(e),a=gn(t)?n.call(e,t):pn(t)?new hn({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function({provider:e,spaceId:t,functionName:n}={}){const{appId:s,uniPlatform:o,osName:r}=ve();let i=o;"app"===o&&(i=r);const a=function({provider:e,spaceId:t}={}){const n=q;if(!n)return{};e=function(e){return"tencent"===e?"tcb":e}(e);const s=n.find((n=>n.provider===e&&n.spaceId===t));return s&&s.config}({provider:e,spaceId:t});if(!a||!a.accessControl||!a.accessControl.enable)return!1;const c=a.accessControl.function||{},l=Object.keys(c);if(0===l.length)return!0;const u=function(e,t){let n,s,o;for(let r=0;r<e.length;r++){const i=e[r];i!==t?"*"!==i?i.split(",").map((e=>e.trim())).indexOf(t)>-1&&(s=i):o=i:n=i}return n||s||o}(l,n);if(!u)return!1;if((c[u]||[]).find(((e={})=>e.appId===s&&(e.platform||"").toLowerCase()===i.toLowerCase())))return!0;throw console.error(`此应用[appId: ${s}, platform: ${i}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),dn(un)}({provider:s,spaceId:o,functionName:r})?new hn({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):i(t),Object.defineProperty(a,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),a.then((e=>("undefined"!=typeof UTSJSONObject&&(e.result=new UTSJSONObject(e.result)),e)))}}hn=class{constructor(){throw dn({message:"Platform app is not enabled, please check whether secure network module is enabled in your manifest.json"})}};const yn=Symbol("CLIENT_DB_INTERNAL");function _n(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=yn,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(e,n,s){if("_uniClient"===n)return null;if("symbol"==typeof n)return e[n];if(n in e||"string"!=typeof n){const t=e[n];return"function"==typeof t?t.bind(e):t}return t.get(e,n,s)}})}function vn(e){return{on:(t,n)=>{e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:(t,n)=>{e[t]=e[t]||[];const s=e[t].indexOf(n);-1!==s&&e[t].splice(s,1)}}}const wn=["db.Geo","db.command","command.aggregate"];function Sn(e,t){return wn.indexOf(`${e}.${t}`)>-1}function kn(e){switch(C(e=ge(e))){case"array":return e.map((e=>kn(e)));case"object":return e._internalType===yn||Object.keys(e).forEach((t=>{e[t]=kn(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function bn(e){return e&&e.content&&e.content.$method}class Tn{constructor(e,t,n){this.content=e,this.prevStage=t||null,this.udb=null,this._database=n}toJSON(){let e=this;const t=[e.content];for(;e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((e=>({$method:e.$method,$param:kn(e.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const e=this.toJSON().$db.find((e=>"action"===e.$method));return e&&e.$param&&e.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((e=>"action"!==e.$method))}}get isAggregate(){let e=this;for(;e;){const t=bn(e),n=bn(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}get isCommand(){let e=this;for(;e;){if("command"===bn(e))return!0;e=e.prevStage}return!1}get isAggregateCommand(){let e=this;for(;e;){const t=bn(e),n=bn(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}getNextStageFn(e){const t=this;return function(){return In({$method:e,$param:kn(Array.from(arguments))},t,t._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(e,t){const n=this.getAction(),s=this.getCommand();return s.$db.push({$method:e,$param:kn(t)}),this._database._callCloudFunction({action:n,command:s})}}function In(e,t,n){return _n(new Tn(e,t,n),{get(e,t){let s="db";return e&&e.content&&(s=e.content.$method),Sn(s,t)?In({$method:t},e,n):function(){return In({$method:t,$param:kn(Array.from(arguments))},e,n)}}})}function An({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map((e=>({$method:e}))),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function Pn(e,t={}){return _n(new e(t),{get:(e,t)=>Sn("db",t)?In({$method:t},null,e):function(){return In({$method:t,$param:kn(Array.from(arguments))},null,e)}})}class xn extends class{constructor({uniClient:e={},isJQL:t=!1}={}){this._uniClient=e,this._authCallBacks={},this._dbCallBacks={},e._isDefault&&(this._dbCallBacks=$("_globalUniCloudDatabaseCallback")),t||(this.auth=vn(this._authCallBacks)),this._isJQL=t,Object.assign(this,vn(this._dbCallBacks)),this.env=_n({},{get:(e,t)=>({$env:t})}),this.Geo=_n({},{get:(e,t)=>An({path:["Geo"],method:t})}),this.serverDate=An({path:[],method:"serverDate"}),this.RegExp=An({path:[],method:"RegExp"})}getCloudEnv(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}_callback(e,t){const n=this._dbCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}_callbackAuth(e,t){const n=this._authCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}multiSend(){const e=Array.from(arguments),t=e.map((e=>{const t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}{_parseResult(e){return this._isJQL?e.result:e}_callCloudFunction({action:e,command:t,multiCommand:n,queryList:s}){function o(e,t){if(n&&s)for(let n=0;n<s.length;n++){const o=s[n];o.udb&&"function"==typeof o.udb.setResult&&(t?o.udb.setResult(t):o.udb.setResult(e.result.dataList[n]))}}const r=this,i=this._isJQL?"databaseForJQL":"database";function a(e){return r._callback("error",[e]),W(G(i,"fail"),e).then((()=>W(G(i,"complete"),e))).then((()=>(o(null,e),ae(X,{type:te,content:e}),Promise.reject(e))))}const c=W(G(i,"invoke")),l=this._uniClient;return c.then((()=>l.callFunction({name:"DCloud-clientDB",type:P,data:{action:e,command:t,multiCommand:n}}))).then((e=>{const{code:t,message:n,token:s,tokenExpired:c,systemInfo:l=[]}=e.result;if(l)for(let o=0;o<l.length;o++){const{level:e,message:t,detail:n}=l[o];let s="[System Info]"+t;n&&(s=`${s}\n详细信息：${n}`),(console["warn"===e?"error":e]||console.log)(s)}if(t)return a(new he({code:t,message:n,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,s&&c&&(me({token:s,tokenExpired:c}),this._callbackAuth("refreshToken",[{token:s,tokenExpired:c}]),this._callback("refreshToken",[{token:s,tokenExpired:c}]),ae(ee,{token:s,tokenExpired:c}));const u=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let o=0;o<u.length;o++){const{prop:t,tips:n}=u[o];if(t in e.result){const s=e.result[t];Object.defineProperty(e.result,t,{get:()=>(console.warn(n),s)})}}return d=e,W(G(i,"success"),d).then((()=>W(G(i,"complete"),d))).then((()=>{o(d,null);const e=r._parseResult(d);return ae(X,{type:te,content:e}),Promise.resolve(e)}));var d}),(e=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),a(new he({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId})))))}}const En="token无效，跳转登录页面",Cn="token过期，跳转登录页面",On={TOKEN_INVALID_TOKEN_EXPIRED:Cn,TOKEN_INVALID_INVALID_CLIENTID:En,TOKEN_INVALID:En,TOKEN_INVALID_WRONG_TOKEN:En,TOKEN_INVALID_ANONYMOUS_USER:En},Nn={"uni-id-token-expired":Cn,"uni-id-check-token-failed":En,"uni-id-token-not-exist":En,"uni-id-check-device-feature-failed":En};function Ln(e,t){let n="";return n=e?`${e}/${t}`:t,n.replace(/^\//,"")}function Un(e=[],t=""){const n=[],s=[];return e.forEach((e=>{!0===e.needLogin?n.push(Ln(t,e.path)):!1===e.needLogin&&s.push(Ln(t,e.path))})),{needLoginPage:n,notNeedLoginPage:s}}function Dn(e){return e.split("?")[0].replace(/^\//,"")}function Rn(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){const e=getCurrentPages();return e[e.length-1]}())}function Mn(){return Dn(Rn())}function qn(e="",t={}){if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;const n=t.list,s=Dn(e);return n.some((e=>e.pagePath===s))}const Fn=!!m.uniIdRouter,{loginPage:jn,routerNeedLogin:Vn,resToLogin:Bn,needLoginPage:$n,notNeedLoginPage:Kn,loginPageInTabBar:zn}=function({pages:e=[],subPackages:t=[],uniIdRouter:n={},tabBar:s={}}=m){const{loginPage:o,needLogin:r=[],resToLogin:i=!0}=n,{needLoginPage:a,notNeedLoginPage:c}=Un(e),{needLoginPage:l,notNeedLoginPage:u}=function(e=[]){const t=[],n=[];return e.forEach((e=>{const{root:s,pages:o=[]}=e,{needLoginPage:r,notNeedLoginPage:i}=Un(o,s);t.push(...r),n.push(...i)})),{needLoginPage:t,notNeedLoginPage:n}}(t);return{loginPage:o,routerNeedLogin:r,resToLogin:i,needLoginPage:[...a,...l],notNeedLoginPage:[...c,...u],loginPageInTabBar:qn(o,s)}}();if($n.indexOf(jn)>-1)throw new Error(`Login page [${jn}] should not be "needLogin", please check your pages.json`);function Hn(e){const t=Mn();if("/"===e.charAt(0))return e;const[n,s]=e.split("?"),o=n.replace(/^\//,"").split("/"),r=t.split("/");r.pop();for(let i=0;i<o.length;i++){const e=o[i];".."===e?r.pop():"."!==e&&r.push(e)}return""===r[0]&&r.shift(),"/"+r.join("/")+(s?"?"+s:"")}function Jn({redirect:e}){const t=Dn(e),n=Dn(jn);return Mn()!==n&&t!==n}function Wn({api:e,redirect:t}={}){if(!t||!Jn({redirect:t}))return;const n=(o=t,"/"!==(s=jn).charAt(0)&&(s="/"+s),o?s.indexOf("?")>-1?s+`&uniIdRedirectUrl=${encodeURIComponent(o)}`:s+`?uniIdRedirectUrl=${encodeURIComponent(o)}`:s);var s,o;zn?"navigateTo"!==e&&"redirectTo"!==e||(e="switchTab"):"switchTab"===e&&(e="navigateTo");const r={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((()=>{r[e]({url:n})}),0)}function Gn({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){const{token:e,tokenExpired:t}=fe();let n;if(e){if(t<Date.now()){const e="uni-id-token-expired";n={errCode:e,errMsg:Nn[e]}}}else{const e="uni-id-check-token-failed";n={errCode:e,errMsg:Nn[e]}}return n}();if(function(e){const t=Dn(Hn(e));return!(Kn.indexOf(t)>-1)&&($n.indexOf(t)>-1||Vn.some((t=>{return n=e,new RegExp(t).test(n);var n})))}(e)&&n){if(n.uniIdRedirectUrl=e,oe(Z).length>0)return setTimeout((()=>{ae(Z,n)}),0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function Qn(){!function(){const e=Rn(),{abortLoginPageJump:t,autoToLoginPage:n}=Gn({url:e});t||n&&Wn({api:"redirectTo",redirect:e})}();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const n=e[t];uni.addInterceptor(n,{invoke(e){const{abortLoginPageJump:t,autoToLoginPage:s}=Gn({url:e.url});return t?e:s?(Wn({api:n,redirect:Hn(e.url)}),!1):e}})}}function Yn(){this.onResponse((e=>{const{type:t,content:n}=e;let s=!1;switch(t){case"cloudobject":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in Nn}(n);break;case"clientdb":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in On}(n)}s&&function(e={}){const t=oe(Z);ue().then((()=>{const n=Rn();if(n&&Jn({redirect:n}))return t.length>0?ae(Z,Object.assign({uniIdRedirectUrl:n},e)):void(jn&&Wn({api:"navigateTo",redirect:n}))}))}(n)}))}function Xn(e){var t;(t=e).onResponse=function(e){re(X,e)},t.offResponse=function(e){ie(X,e)},function(e){e.onNeedLogin=function(e){re(Z,e)},e.offNeedLogin=function(e){ie(Z,e)},Fn&&($(cn).needLoginInit||($(cn).needLoginInit=!0,ue().then((()=>{Qn.call(e)})),Bn&&Yn.call(e)))}(e),function(e){e.onRefreshToken=function(e){re(ee,e)},e.offRefreshToken=function(e){ie(ee,e)}}(e)}let Zn;const es="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",ts=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function ns(){const e=fe().token||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((s=t[1],decodeURIComponent(Zn(s).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}var s;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}Zn="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!ts.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,s,o="",r=0;r<e.length;)t=es.indexOf(e.charAt(r++))<<18|es.indexOf(e.charAt(r++))<<12|(n=es.indexOf(e.charAt(r++)))<<6|(s=es.indexOf(e.charAt(r++))),o+=64===n?String.fromCharCode(t>>16&255):64===s?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}:atob;var ss=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(y((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const n="chooseAndUploadFile:ok",s="chooseAndUploadFile:fail";function o(e,t){return e.tempFiles.forEach(((e,n)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}function r(e,t,{onChooseFile:s,onUploadProgress:o}){return t.then((e=>{if(s){const t=s(e);if(void 0!==t)return Promise.resolve(t).then((t=>void 0===t?e:t))}return e})).then((t=>!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t,s=5,o){(t=Object.assign({},t)).errMsg=n;const r=t.tempFiles,i=r.length;let a=0;return new Promise((n=>{for(;a<s;)c();function c(){const s=a++;if(s>=i)return void(!r.find((e=>!e.url&&!e.errMsg))&&n(t));const l=r[s];e.uploadFile({provider:l.provider,filePath:l.path,cloudPath:l.cloudPath,fileType:l.fileType,cloudPathAsRealPath:l.cloudPathAsRealPath,onUploadProgress(e){e.index=s,e.tempFile=l,e.tempFilePath=l.path,o&&o(e)}}).then((e=>{l.url=e.fileID,s<i&&c()})).catch((e=>{l.errMsg=e.errMsg||e.message,s<i&&c()}))}}))}(e,t,5,o)))}t.initChooseAndUploadFile=function(e){return function(t={type:"all"}){return"image"===t.type?r(e,function(e){const{count:t,sizeType:n,sourceType:r=["album","camera"],extension:i}=e;return new Promise(((e,a)=>{uni.chooseImage({count:t,sizeType:n,sourceType:r,extension:i,success(t){e(o(t,"image"))},fail(e){a({errMsg:e.errMsg.replace("chooseImage:fail",s)})}})}))}(t),t):"video"===t.type?r(e,function(e){const{camera:t,compressed:n,maxDuration:r,sourceType:i=["album","camera"],extension:a}=e;return new Promise(((e,c)=>{uni.chooseVideo({camera:t,compressed:n,maxDuration:r,sourceType:i,extension:a,success(t){const{tempFilePath:n,duration:s,size:r,height:i,width:a}=t;e(o({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:r,type:t.tempFile&&t.tempFile.type||"",width:a,height:i,duration:s,fileType:"video",cloudPath:""}]},"video"))},fail(e){c({errMsg:e.errMsg.replace("chooseVideo:fail",s)})}})}))}(t),t):r(e,function(e){const{count:t,extension:n}=e;return new Promise(((e,r)=>{let i=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(i=wx.chooseMessageFile),"function"!=typeof i)return r({errMsg:s+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});i({type:"all",count:t,extension:n,success(t){e(o(t))},fail(e){r({errMsg:e.errMsg.replace("chooseFile:fail",s)})}})}))}(t),t)}}})));const os="manual";function rs(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var e=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((t=>{e.push(this[t])})),e}),((e,t)=>{if(this.loadtime===os)return;let n=!1;const s=[];for(let o=2;o<e.length;o++)e[o]!==t[o]&&(s.push(e[o]),n=!0);e[0]!==t[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(n,s)}))},methods:{onMixinDatacomPropsChange(e,t){},mixinDatacomEasyGet({getone:e=!1,success:t,fail:n}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((n=>{this.mixinDatacomLoading=!1;const{data:s,count:o}=n.result;this.getcount&&(this.mixinDatacomPage.count=o),this.mixinDatacomHasMore=s.length<this.pageSize;const r=e?s.length?s[0]:void 0:s;this.mixinDatacomResData=r,t&&t(r)})).catch((e=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e,this.mixinDatacomError=e,n&&n(e)})))},mixinDatacomGet(t={}){let n;t=t||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);const s=t.action||this.action;s&&(n=n.action(s));const o=t.collection||this.collection;n=Array.isArray(o)?n.collection(...o):n.collection(o);const r=t.where||this.where;r&&Object.keys(r).length&&(n=n.where(r));const i=t.field||this.field;i&&(n=n.field(i));const a=t.foreignKey||this.foreignKey;a&&(n=n.foreignKey(a));const c=t.groupby||this.groupby;c&&(n=n.groupBy(c));const l=t.groupField||this.groupField;l&&(n=n.groupField(l)),!0===(void 0!==t.distinct?t.distinct:this.distinct)&&(n=n.distinct());const u=t.orderby||this.orderby;u&&(n=n.orderBy(u));const d=void 0!==t.pageCurrent?t.pageCurrent:this.mixinDatacomPage.current,h=void 0!==t.pageSize?t.pageSize:this.mixinDatacomPage.size,p=void 0!==t.getcount?t.getcount:this.getcount,g=void 0!==t.gettree?t.gettree:this.gettree,f=void 0!==t.gettreepath?t.gettreepath:this.gettreepath,m={getCount:p},y={limitLevel:void 0!==t.limitlevel?t.limitlevel:this.limitlevel,startWith:void 0!==t.startwith?t.startwith:this.startwith};return g&&(m.getTree=y),f&&(m.getTreePath=y),n=n.skip(h*(d-1)).limit(h).get(m),n}}}}function is(e){return $("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function as({openid:e,callLoginByWeixin:t=!1}={}){throw is(this),new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `app`")}async function cs(e){const t=is(this);return t.initPromise||(t.initPromise=as.call(this,e).then((e=>e)).catch((e=>{throw delete t.initPromise,e}))),t.initPromise}function ls(e){Se=e}function us(e){const t={getSystemInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(n){return new Promise(((s,o)=>{t[e]({...n,success(e){s(e)},fail(e){o(e)}})}))}}class ds extends class{constructor(){this._callback={}}addListener(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}on(e,t){return this.addListener(e,t)}removeListener(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');const n=this._callback[e];if(!n)return;const s=function(e,t){for(let n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(s,1)}off(e,t){return this.removeListener(e,t)}removeAllListener(e){delete this._callback[e]}emit(e,...t){const n=this._callback[e];if(n)for(let s=0;s<n.length;s++)n[s](...t)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([us("getSystemInfo")(),us("getPushClientId")()]).then((([{appId:e}={},{cid:t}={}]=[])=>{if(!e)throw new Error("Invalid appId, please check the manifest.json file");if(!t)throw new Error("Invalid push client id");this._appId=e,this._pushClientId=t,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(e=>{throw this.emit("error",e),this.close(),e}))}async open(){return this.init()}_isUniCloudSSE(e){if("receive"!==e.type)return!1;const t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}_receivePushMessage(e){if(!this._isUniCloudSSE(e))return;const t=e&&e.data&&e.data.payload,{action:n,messageId:s,message:o}=t;this._payloadQueue.push({action:n,messageId:s,message:o}),this._consumMessage()}_consumMessage(){for(;;){const e=this._payloadQueue.find((e=>e.messageId===this._currentMessageId+1));if(!e)break;this._currentMessageId++,this._parseMessagePayload(e)}}_parseMessagePayload(e){const{action:t,messageId:n,message:s}=e;"end"===t?this._end({messageId:n,message:s}):"message"===t&&this._appendMessage({messageId:n,message:s})}_appendMessage({messageId:e,message:t}={}){this.emit("message",t)}_end({messageId:e,message:t}={}){this.emit("end",t),this.close()}_initMessageListener(){uni.onPushMessage(this._uniPushMessageCallback)}_destroy(){uni.offPushMessage(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}const hs={tcb:jt,tencent:jt,aliyun:Ae,private:$t,dcloud:$t,alipay:Xt};let ps=new class{init(e){let t={};const n=hs[e.provider];if(!n)throw new Error("未提供正确的provider参数");var s;return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new R({createPromise:function(){let t=Promise.resolve();t=new Promise((e=>{setTimeout((()=>{e()}),1)}));const n=e.auth();return t.then((()=>n.getLoginState())).then((e=>e?Promise.resolve():n.signInAnonymously()))}}))}(t),mn(t),function(e){const t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),(s=t).database=function(e){if(e&&Object.keys(e).length>0)return s.init(e).database();if(this._database)return this._database;const t=Pn(xn,{uniClient:s});return this._database=t,t},s.databaseForJQL=function(e){if(e&&Object.keys(e).length>0)return s.init(e).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const t=Pn(xn,{uniClient:s,isJQL:!0});return this._databaseForJQL=t,t},function(e){e.getCurrentUserInfo=ns,e.chooseAndUploadFile=ss.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return rs(e)}}),e.SSEChannel=ds,e.initSecureNetworkByWeixin=function(e){return function({openid:t,callLoginByWeixin:n=!1}={}){return cs.call(e,{openid:t,callLoginByWeixin:n})}}(e),e.setCustomClientInfo=ls,e.importObject=function(t){return function(n,s={}){s=function(e,t={}){return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==typeof t.secretMethods&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},s);const{customUI:o,loadingOptions:r,errorOptions:i,parseSystemError:a}=s,c=!o;return new Proxy({},{get(o,l){switch(l){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:e,interceptorName:t,getCallbackArgs:n}={}){return async function(...s){const o=n?n({params:s}):{};let r,i;try{return await W(G(t,"invoke"),{...o}),r=await e(...s),await W(G(t,"success"),{...o,result:r}),r}catch(a){throw i=a,await W(G(t,"fail"),{...o,error:i}),i}finally{await W(G(t,"complete"),i?{...o,error:i}:{...o,result:r})}}}({fn:async function o(...u){let d;c&&uni.showLoading({title:r.title,mask:r.mask});const h={name:n,type:A,data:{method:l,params:u}};"object"==typeof s.secretMethods&&function(e,t){const n=t.data.method,s=e.secretMethods||{},o=s[n]||s["*"];o&&(t.secretType=o)}(s,h);let p=!1;try{d=await t.callFunction(h)}catch(e){p=!0,d={result:new he(e)}}const{errSubject:g,errCode:f,errMsg:m,newToken:y}=d.result||{};if(c&&uni.hideLoading(),y&&y.token&&y.tokenExpired&&(me(y),ae(ee,{...y})),f){let e=m;if(p&&a&&(e=(await a({objectName:n,methodName:l,params:u,errSubject:g,errCode:f,errMsg:m})).errMsg||m),c)if("toast"===i.type)uni.showToast({title:e,icon:"none"});else{if("modal"!==i.type)throw new Error(`Invalid errorOptions.type: ${i.type}`);{const{confirm:t}=await async function({title:e,content:t,showCancel:n,cancelText:s,confirmText:o}={}){return new Promise(((r,i)=>{uni.showModal({title:e,content:t,showCancel:n,cancelText:s,confirmText:o,success(e){r(e)},fail(){r({confirm:!1,cancel:!0})}})}))}({title:"提示",content:e,showCancel:i.retry,cancelText:"取消",confirmText:i.retry?"重试":"确定"});if(i.retry&&t)return o(...u)}}const t=new he({subject:g,code:f,message:m,requestId:d.requestId});throw t.detail=d.result,ae(X,{type:se,content:t}),t}return ae(X,{type:se,content:d.result}),d.result},interceptorName:"callObject",getCallbackArgs:function({params:e}={}){return{objectName:n,methodName:l,params:e}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((e=>{if(!t[e])return;const n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){let s=!1;if("callFunction"===t){const e=n&&n.type||I;s=e!==I}const o="callFunction"===t&&!s,r=this._initPromiseHub.exec();n=n||{};const{success:i,fail:a,complete:c}=de(n),l=r.then((()=>s?Promise.resolve():W(G(t,"invoke"),n))).then((()=>e.call(this,n))).then((e=>s?Promise.resolve(e):W(G(t,"success"),e).then((()=>W(G(t,"complete"),e))).then((()=>(o&&ae(X,{type:ne,content:e}),Promise.resolve(e))))),(e=>s?Promise.reject(e):W(G(t,"fail"),e).then((()=>W(G(t,"complete"),e))).then((()=>(ae(X,{type:ne,content:e}),Promise.reject(e))))));if(!(i||a||c))return l;l.then((e=>{i&&i(e),c&&c(e),o&&ae(X,{type:ne,content:e})}),(e=>{a&&a(e),c&&c(e),o&&ae(X,{type:ne,content:e})}))}}(t[e],e).bind(t)})),t.init=this.init,t}};(()=>{const e=F;let t={};if(e&&1===e.length)t=e[0],ps=ps.init(t),ps._isDefault=!0;else{const t=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let n;n=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",t.forEach((e=>{ps[e]=function(){return console.error(n),Promise.reject(new he({code:"SYS_ERR",message:n}))}}))}Object.assign(ps,{get mixinDatacom(){return rs(ps)}}),Xn(ps),ps.addInterceptor=H,ps.removeInterceptor=J,ps.interceptObject=Q,uni.__uniCloud=ps;{const e=V||(V=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),V);e.uniCloud=ps,e.UniCloudError=he}})();const{app:gs,Vuex:fs,Pinia:ms}=function(){const t=e.createVueApp(u);return t.config.globalProperties.$request=f.request,t.config.globalProperties.$http=f.http,t.config.globalProperties.baseURL=f.BASE_URL,{app:t}}();uni.Vuex=fs,uni.Pinia=ms,gs.provide("__globalStyles",__uniConfig.styles),gs._component.mpType="app",gs._component.render=()=>{},gs.mount("#app")}(Vue);
