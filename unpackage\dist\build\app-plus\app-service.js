if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...n){uni.__log__?uni.__log__(e,t,...n):console[e].apply(console,[...n,t])}const n=(t=>(n,s=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,n,s)})("onLoad"),s="/static/pet/rabbit.png",o={BASE_URL:"http://124.223.80.197:8080/",API_TIMEOUT:15e3,ENV:"production"},r=o.BASE_URL,i=o.API_TIMEOUT,a={__name:"login",setup(n){const o=e.ref(""),i=e.ref(""),a=e.ref(!1),c=e.ref(60),l=()=>{o.value=o.value.replace(/\D/g,""),o.value.length>11&&(o.value=o.value.slice(0,11))};e.onMounted((async()=>{t("log","at pages/login/login.vue:55","登录页面加载完成"),await u()}));const u=async()=>{const e=uni.getStorageSync("authorization");if(!e)return void t("log","at pages/login/login.vue:65","未找到Cookie，停留在登录页");t("log","at pages/login/login.vue:69","检测到Cookie，验证中...");const n=new Promise(((e,t)=>{setTimeout((()=>t(new Error("请求超时"))),5e3)})),s=uni.request({url:r+"user",method:"GET",header:{Cookie:`Authorization=${e}`,"Content-Type":"application/json"},withCredentials:!0});try{const e=await Promise.race([s,n]);t("log","at pages/login/login.vue:88","Cookie验证结果:",e),e.data&&0===e.data.code?(t("log","at pages/login/login.vue:92","Cookie有效，跳转到首页"),setTimeout((()=>{uni.switchTab({url:"/pages/tabbar/index/index"})}),100)):(t("log","at pages/login/login.vue:100","Cookie无效，清除本地数据"),uni.removeStorageSync("authorization"))}catch(o){t("log","at pages/login/login.vue:104","Cookie验证失败:",o.message||o),uni.removeStorageSync("authorization"),"请求超时"===o.message&&uni.showToast({title:"网络连接超时",icon:"none",duration:2e3})}},d=async()=>{t("log","at pages/login/login.vue:121","测试网络连接..."),uni.showLoading({title:"测试中..."});try{const e=await uni.request({url:r+"user",method:"GET",header:{"Content-Type":"application/json"},timeout:5e3});uni.hideLoading(),t("log","at pages/login/login.vue:137","网络测试结果:",e),uni.showModal({title:"网络测试结果",content:`状态码: ${e.statusCode}\n响应: ${JSON.stringify(e.data)}`,showCancel:!1})}catch(e){uni.hideLoading(),t("log","at pages/login/login.vue:146","网络测试失败:",e),uni.showModal({title:"网络测试失败",content:`错误: ${e.message||e}`,showCancel:!1})}},h=()=>{o.value?11===o.value.length?a.value||((()=>{a.value=!0,c.value=60;const e=setInterval((()=>{c.value--,c.value<=0&&(clearInterval(e),a.value=!1)}),1e3)})(),uni.request({url:r+"user",method:"POST",data:{phone:o.value},header:{"Content-Type":"application/json"}}).then((e=>{t("log","at pages/login/login.vue:205",e),uni.showToast({title:"验证码已发送",icon:"success"})})).catch((e=>{t("log","at pages/login/login.vue:211",e),uni.showToast({title:"发送失败，请重试",icon:"none"}),a.value=!1}))):uni.showToast({title:"请输入11位手机号",icon:"none"}):uni.showToast({title:"请输入手机号",icon:"none"})},g=()=>{o.value&&i.value?11===o.value.length?6===i.value.length?uni.request({url:r+"user",method:"POST",data:{phone:o.value,code:i.value},header:{"Content-Type":"application/json"}}).then((e=>{t("log","at pages/login/login.vue:258",e),uni.setStorageSync("authorization",e.data.data.authorization),t("log","at pages/login/login.vue:262","保存的authorization:",uni.getStorageSync("authorization")),uni.reLaunch({url:"/pages/tabbar/index/index",success:function(){setTimeout((()=>{uni.showToast({title:"登录成功",icon:"success"})}),200)},fail:function(e){t("error","at pages/login/login.vue:277","跳转到首页失败:",e)}})})).catch((e=>{t("log","at pages/login/login.vue:281",e),uni.showToast({title:"登录失败，请重试",icon:"none"})})):uni.showToast({title:"验证码格式错误",icon:"none"}):uni.showToast({title:"请输入11位手机号",icon:"none"}):uni.showToast({title:"请输入手机号和验证码",icon:"none"})};return(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"login-page"},[e.createElementVNode("image",{class:"bg-image",src:s,mode:"aspectFill"}),e.createElementVNode("view",{class:"login-container"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("image",{class:"logo",src:s}),e.createElementVNode("text",{class:"app-title"},"GuiYuan")]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-item"},[e.withDirectives(e.createElementVNode("input",{type:"number",maxlength:"11","onUpdate:modelValue":n[0]||(n[0]=e=>o.value=e),placeholder:"请输入手机号","placeholder-class":"placeholder",onInput:l},null,544),[[e.vModelText,o.value]])]),e.createElementVNode("view",{class:"code-container"},[e.createElementVNode("view",{class:"input-item code-input"},[e.withDirectives(e.createElementVNode("input",{type:"number",maxlength:"6","onUpdate:modelValue":n[1]||(n[1]=e=>i.value=e),placeholder:"请输入验证码","placeholder-class":"placeholder"},null,512),[[e.vModelText,i.value]])]),e.createElementVNode("button",{class:"send-code-btn",onClick:h,disabled:a.value},e.toDisplayString(a.value?`${c.value}s后重试`:"发送验证码"),9,["disabled"])]),e.createElementVNode("button",{class:"login-btn",onClick:g},"登录"),e.createElementVNode("button",{class:"test-btn",onClick:d},"测试网络连接")])])]))}},c="/static/user/avatar.jpg",l={__name:"index",setup(o){const i=e.ref([]),a=e.ref(1),l=e.ref(10),u=e.ref(0),d=e.ref(0),h=e.ref(!1),g=e.ref(!1),p=e=>{var t,n;const s=320+.8*(((null==(t=e.title)?void 0:t.length)||0)+((null==(n=e.content)?void 0:n.length)||0)),o=100*Math.random()-50;return Math.max(240,Math.min(500,s+o))+"rpx"},f=e.computed((()=>i.value.filter(((e,t)=>t%2==0)))),m=e.computed((()=>i.value.filter(((e,t)=>t%2==1)))),y=async(e=1,n=!1)=>{if(!h.value){h.value=!0;try{const s=uni.getStorageSync("authorization"),o=await uni.request({url:r+"recommend",method:"GET",header:{Cookie:`Authorization=${s}`},data:{pageNum:e,pageSize:l.value},withCredentials:!0});if(t("log","at pages/tabbar/index/index.vue:162","推荐列表响应:",o),o.data&&o.data.data){const e=o.data.data;u.value=e.total,d.value=e.pages;const t=(e.records||[]).map((e=>({...e,content:e.content||"这是一段随机生成的内容，用来展示瀑布流布局效果。内容长度不同，会影响卡片高度。"})));i.value=n?t:[...i.value,...t],g.value=a.value>=d.value}}catch(s){t("error","at pages/tabbar/index/index.vue:191","获取推荐列表失败:",s)}finally{h.value=!1}}},_=()=>{g.value||h.value||(a.value++,y(a.value))},w=e=>{uni.navigateTo({url:`/pages/recommend/detail?id=${e.id}`})};return e.onMounted((async()=>{await y(1,!0)})),n((()=>{const e=getApp();t("log","at pages/tabbar/index/index.vue:224","当前页面authorization值:",uni.getStorageSync("authorization")),e.checkLogin()&&(t("log","at pages/tabbar/index/index.vue:229","登录成功"),y(1,!0))})),(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("image",{class:"logo",src:s}),e.createElementVNode("text",{class:"title"},"推荐")]),e.createElementVNode("view",{class:"search-box"},[e.createElementVNode("text",{class:"search-placeholder"},"搜索")])]),e.createElementVNode("scroll-view",{class:"waterfall","scroll-y":"",onScrolltolower:_},[e.createElementVNode("view",{class:"waterfall-wrapper"},[e.createElementVNode("view",{class:"waterfall-column left-column"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(f.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"waterfall-item",key:n,onClick:e=>w(t),style:e.normalizeStyle({marginBottom:10+Math.floor(15*Math.random())+"rpx"})},[e.createElementVNode("view",{class:"media-wrapper",style:e.normalizeStyle({height:p(t)})},[e.createElementVNode("image",{class:"item-image",src:t.img,mode:"aspectFill"},null,8,["src"])],4),e.createElementVNode("view",{class:"item-content"},[e.createElementVNode("text",{class:"item-title",style:e.normalizeStyle({"-webkit-line-clamp":n%2==0?2:1})},e.toDisplayString(t.title),5),e.createElementVNode("text",{class:"item-desc",style:e.normalizeStyle({"-webkit-line-clamp":n%5==0?4:n%3==0?3:2})},e.toDisplayString(t.content),5),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("image",{class:"user-avatar",src:c}),e.createElementVNode("text",{class:"user-name"},"用户"+e.toDisplayString(t.userid),1)]),e.createElementVNode("view",{class:"like-info"},[e.createElementVNode("image",{class:"icon-rabbit",src:s})])])])],12,["onClick"])))),128))]),e.createElementVNode("view",{class:"waterfall-column right-column"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(m.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"waterfall-item",key:n,onClick:e=>w(t),style:e.normalizeStyle({marginBottom:10+Math.floor(15*Math.random())+"rpx"})},[e.createElementVNode("view",{class:"media-wrapper",style:e.normalizeStyle({height:p(t)})},[e.createElementVNode("image",{class:"item-image",src:t.img,mode:"aspectFill"},null,8,["src"]),n%7==0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"floating-title"},[e.createElementVNode("text",null,e.toDisplayString(t.title),1)])):e.createCommentVNode("",!0)],4),e.createElementVNode("view",{class:"item-content"},[e.createElementVNode("text",{class:"item-title",style:e.normalizeStyle({"-webkit-line-clamp":n%2==0?1:2})},e.toDisplayString(t.title),5),e.createElementVNode("text",{class:"item-desc",style:e.normalizeStyle({"-webkit-line-clamp":n%4==0?3:2})},e.toDisplayString(t.content),5),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("image",{class:"user-avatar",src:c}),e.createElementVNode("text",{class:"user-name"},"用户"+e.toDisplayString(t.userid),1)]),e.createElementVNode("view",{class:"like-info"},[e.createElementVNode("image",{class:"icon-rabbit",src:s})])])])],12,["onClick"])))),128))])]),h.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading"},[e.createElementVNode("text",{class:"loading-text"},"加载中...")])):e.createCommentVNode("",!0),g.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-more"},[e.createElementVNode("text",{class:"no-more-text"},"没有更多了~")])):e.createCommentVNode("",!0)],32)]))}},u={__name:"home",setup(n){const s=e.ref({}),o=e.ref({nickname:"",gender:"",birthday:""}),i=["男","女","其他"],a=async()=>{try{const e=await uni.request({url:r+"user",method:"GET",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`},withCredentials:!0});e.data&&e.data.data&&(s.value=e.data.data,o.value={id:s.value.id,nickname:s.value.nickname,gender:s.value.gender,birthday:s.value.birthday})}catch(e){t("error","at pages/tabbar/home/<USER>","获取用户信息失败:",e)}},c=async()=>{var e;try{const n=await uni.request({url:r+"user",method:"PUT",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`,"Content-Type":"application/json"},data:JSON.stringify(o.value),withCredentials:!0});if(n.data&&0===n.data.code)uni.showToast({title:"保存成功",icon:"success"}),a();else{const s=(null==(e=n.data)?void 0:e.message)||"保存失败";t("error","at pages/tabbar/home/<USER>","保存设置失败:",s),uni.showToast({title:s,icon:"none"})}}catch(n){t("error","at pages/tabbar/home/<USER>","保存设置失败:",n),uni.showToast({title:n.message||"保存失败",icon:"none",duration:2e3})}},l=e=>new Promise(((n,s)=>{uni.getFileSystemManager().readFile({filePath:e,encoding:"base64",success:e=>{const o={avatar:`data:image/jpeg;base64,${e.data}`,type:"avatar"};uni.request({url:r+"user",method:"PUT",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`,"Content-Type":"application/json"},data:o,success:e=>{try{const t=e.data;if(t&&0===t.code)uni.showToast({title:"头像更新成功",icon:"success"}),a(),n(t);else{const e=(null==t?void 0:t.message)||"头像更新失败";uni.showToast({title:e,icon:"none",duration:2e3}),s(new Error(e))}}catch(o){t("error","at pages/tabbar/home/<USER>","处理响应数据失败:",o),uni.showToast({title:"处理响应数据失败",icon:"none",duration:2e3}),s(o)}},fail:e=>{t("error","at pages/tabbar/home/<USER>","上传头像失败:",e),uni.showToast({title:"上传头像失败",icon:"none",duration:2e3}),s(e)}})},fail:e=>{t("error","at pages/tabbar/home/<USER>","读取图片失败:",e),uni.showToast({title:"读取图片失败",icon:"none",duration:2e3}),s(e)}})})),u=async()=>{try{const e=await uni.request({url:r+"user",method:"DELETE",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`},withCredentials:!0});e.data&&0===e.data.code?(uni.removeStorageSync("authorization"),uni.reLaunch({url:"/pages/login/login"})):uni.showToast({title:e.data.message||"退出失败",icon:"none"})}catch(e){t("error","at pages/tabbar/home/<USER>","退出登录失败:",e),uni.showToast({title:"退出失败",icon:"none"})}},d=()=>{uni.showActionSheet({itemList:["从相册选择","拍照"],success:e=>{0===e.tapIndex?h():1===e.tapIndex&&g()}})},h=async()=>{try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album"]});e.tempFilePaths.length>0&&await l(e.tempFilePaths[0])}catch(e){t("error","at pages/tabbar/home/<USER>","选择头像失败:",e)}},g=async()=>{try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["camera"]});e.tempFilePaths.length>0&&await l(e.tempFilePaths[0])}catch(e){t("error","at pages/tabbar/home/<USER>","拍照失败:",e)}},p=e=>{o.value.gender=i[e.detail.value]},f=e=>{o.value.birthday=e.detail.value};return e.onMounted((()=>{a()})),(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"user-card"},[e.createElementVNode("view",{class:"avatar-container"},[e.createElementVNode("image",{class:"avatar",src:s.value.avatar||"/static/user/avatar.jpg"},null,8,["src"]),e.createElementVNode("button",{class:"edit-avatar-btn",onClick:d},"更换头像")]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"nickname"},e.toDisplayString(s.value.nickname||"未设置昵称"),1),e.createElementVNode("text",{class:"username"},"ID: "+e.toDisplayString(s.value.id),1)])]),e.createElementVNode("view",{class:"settings-form"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"昵称"),e.withDirectives(e.createElementVNode("input",{class:"input","onUpdate:modelValue":n[0]||(n[0]=e=>o.value.nickname=e),placeholder:"请输入昵称"},null,512),[[e.vModelText,o.value.nickname]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"性别"),e.createElementVNode("picker",{class:"picker",mode:"selector",range:i,onChange:p},[e.createElementVNode("text",null,e.toDisplayString(o.value.gender||"请选择性别"),1)],32)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"生日"),e.createElementVNode("picker",{class:"picker",mode:"date",onChange:f},[e.createElementVNode("text",null,e.toDisplayString(o.value.birthday||"请选择生日"),1)],32)]),e.createElementVNode("button",{class:"save-btn",onClick:c},"保存设置"),e.createElementVNode("button",{class:"logout-btn",onClick:u},"退出登录")])]))}};const d=((e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n})({data:()=>({}),methods:{}},[["render",function(t,n,s,o,r,i){return e.openBlock(),e.createElementBlock("view")}]]),h={__name:"daily",setup(s){const o=getApp(),i=uni.getStorageSync("authorization"),a=e.ref([]),c=e.ref(!1),l=e.reactive({id:null,userId:null,title:"",content:"",image:""}),u=async()=>{try{const n=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],maxSize:5242880});if(!n||!n.tempFilePaths||!n.tempFilePaths.length)throw new Error("选择图片失败");const s=n.tempFilePaths[0],o=await uni.uploadFile({url:r+"daily/upload",filePath:s,name:"file",header:{Cookie:`Authorization=${i}`,"Content-Type":"multipart/form-data"},timeout:6e4,formData:{type:"image"}});if(!o||!o.statusCode||200!==o.statusCode)throw new Error(`图片上传失败: ${(null==o?void 0:o.errMsg)||"未知错误"}`);try{const e=JSON.parse(o.data);if(0!==e.code||!e.data)throw new Error(e.message||"图片上传失败");l.image=e.data}catch(e){throw t("error","at pages/tabbar/daily/daily.vue:105","解析上传响应失败:",e),new Error("服务器响应格式错误")}}catch(n){t("error","at pages/tabbar/daily/daily.vue:109","图片处理失败:",n),uni.showToast({title:n.message||"图片处理失败",icon:"none",duration:2e3})}},d=e=>{uni.previewImage({urls:[e]})},h=async()=>{try{const e=await uni.request({url:r+"daily",method:"GET",header:{Cookie:`Authorization=${i}`},data:{userId:o.globalData.userId}});0===e.data.code?a.value=e.data.data:uni.showToast({title:e.data.message||"获取日记失败",icon:"none"})}catch(e){t("error","at pages/tabbar/daily/daily.vue:142","获取日记列表失败:",e),uni.showToast({title:"获取日记列表失败",icon:"none"})}},g=()=>{p(),c.value=!1},p=()=>{l.id=null,l.title="",l.content="",l.image=""};return n((()=>{o.checkLogin()&&h()})),(n,s)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[a.value&&a.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"daily-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.value,((n,s)=>(e.openBlock(),e.createElementBlock("view",{class:"daily-item",key:s},[e.createElementVNode("view",{class:"daily-header"},[e.createElementVNode("text",{class:"daily-title"},e.toDisplayString(n.title),1),e.createElementVNode("view",{class:"daily-actions"},[e.createElementVNode("button",{class:"action-btn edit",onClick:e=>{return t=n,Object.assign(l,t),void(c.value=!0);var t}},"编辑",8,["onClick"]),e.createElementVNode("button",{class:"action-btn delete",onClick:e=>(async e=>{try{const[t,n]=await uni.showModal({title:"确认删除",content:"确定要删除这条日记吗？"});if(n.confirm){const t=await uni.request({url:r+"daily",method:"DELETE",header:{Cookie:`Authorization=${i}`},data:{id:e,userId:o.globalData.userId}});0===t.data.code?(uni.showToast({title:"删除成功",icon:"success"}),h()):uni.showToast({title:t.data.message||"删除失败",icon:"none"})}}catch(n){t("error","at pages/tabbar/daily/daily.vue:259","删除日记失败:",n),uni.showToast({title:"删除失败",icon:"none"})}})(n.id)},"删除",8,["onClick"])])]),e.createElementVNode("view",{class:"daily-content"},e.toDisplayString(n.content),1),n.image?(e.openBlock(),e.createElementBlock("image",{key:0,src:n.image,class:"daily-image",mode:"aspectFill",onClick:e=>d(n.image)},null,8,["src","onClick"])):e.createCommentVNode("",!0)])))),128))])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"form-header"},[e.createElementVNode("text",{class:"form-title"},e.toDisplayString(c.value?"编辑日记":"新建日记"),1)]),e.withDirectives(e.createElementVNode("input",{class:"input-field","onUpdate:modelValue":s[0]||(s[0]=e=>l.title=e),placeholder:"请输入标题"},null,512),[[e.vModelText,l.title]]),e.withDirectives(e.createElementVNode("textarea",{class:"textarea-field","onUpdate:modelValue":s[1]||(s[1]=e=>l.content=e),placeholder:"写下你的想法..."},null,512),[[e.vModelText,l.content]]),e.createElementVNode("view",{class:"image-upload"},[l.image?(e.openBlock(),e.createElementBlock("image",{key:0,src:l.image,class:"preview-image",mode:"aspectFill",onClick:s[2]||(s[2]=e=>d(l.image))},null,8,["src"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"upload-placeholder",onClick:u},[e.createElementVNode("text",{class:"iconfont icon-camera"}),e.createElementVNode("text",null,"点击添加图片")]))]),e.createElementVNode("view",{class:"form-actions"},[e.createElementVNode("button",{class:"submit-btn",onClick:s[3]||(s[3]=e=>c.value?(async()=>{if(l.title&&l.content)try{const e=await uni.request({url:r+"daily",method:"PUT",header:{Cookie:`Authorization=${i}`},data:l});0===e.data.code?(uni.showToast({title:"更新成功",icon:"success"}),h(),p(),c.value=!1):uni.showToast({title:e.data.message||"更新失败",icon:"none"})}catch(e){t("error","at pages/tabbar/daily/daily.vue:222","更新日记失败:",e),uni.showToast({title:"更新失败",icon:"none"})}else uni.showToast({title:"请填写标题和内容",icon:"none"})})():(async()=>{if(l.title&&l.content)try{l.userId=o.globalData.userId;const e=await uni.request({url:r+"daily",method:"POST",header:{Cookie:`Authorization=${i}`},data:l});0===e.data.code?(uni.showToast({title:"发布成功",icon:"success"}),h(),p()):uni.showToast({title:e.data.message||"发布失败",icon:"none"})}catch(e){t("error","at pages/tabbar/daily/daily.vue:182","添加日记失败:",e),uni.showToast({title:"发布失败",icon:"none"})}else uni.showToast({title:"请填写标题和内容",icon:"none"})})())},e.toDisplayString(c.value?"更新":"发布"),1),c.value?(e.openBlock(),e.createElementBlock("button",{key:0,class:"cancel-btn",onClick:g},"取消")):e.createCommentVNode("",!0)])])]))}};__definePage("pages/login/login",a),__definePage("pages/tabbar/index/index",l),__definePage("pages/tabbar/home/<USER>",u),__definePage("pages/tabbar/message/message",d),__definePage("pages/tabbar/daily/daily",h);const g={globalData:{isLogin:!1,userInfo:null},onLaunch:function(){t("log","at App.vue:10","App Launch")},onShow:function(){t("log","at App.vue:14","App Show")},onHide:function(){t("log","at App.vue:17","App Hide")},methods:{clearLoginData(){uni.removeStorageSync("authorization"),this.globalData.isLogin=!1},checkLogin:()=>!!uni.getStorageSync("authorization")||(uni.showToast({title:"请先登录",icon:"none"}),uni.navigateTo({url:"/pages/login/login"}),!1),logout(){this.clearLoginData(),uni.reLaunch({url:"/pages/login/login"})}}},p={baseURL:r,timeout:i,header:{"Content-Type":"application/json"}},f=(e={})=>(e=(e=>{e.url=(e.baseURL||p.baseURL)+(e.url||""),e.header={...p.header,...e.header||{}},e.timeout=e.timeout||p.timeout;const n=uni.getStorageSync("authorization");return n&&(e.header.Authorization=n),t("log","at api/request.js:32","请求拦截器 ==>",{url:e.url,method:e.method,data:e.data,header:e.header}),e})(e),new Promise(((n,s)=>{uni.request({...e,success:o=>{((e,n)=>{var s;return t("log","at api/request.js:50","响应拦截器 ==>",{url:n.url,data:e,statusCode:e.statusCode}),e.statusCode>=200&&e.statusCode<300?Promise.resolve(e.data):401===e.statusCode?(uni.removeStorageSync("authorization"),uni.showToast({title:"登录已过期，请重新登录",icon:"none"}),uni.reLaunch({url:"/pages/login/login"}),Promise.reject(e)):(uni.showToast({title:(null==(s=e.data)?void 0:s.message)||`请求失败(${e.statusCode})`,icon:"none"}),Promise.reject(e))})(o,e).then(n).catch(s)},fail:n=>{((e,n)=>(t("error","at api/request.js:88","请求错误 ==>",{url:n.url,error:e}),uni.showToast({title:"网络异常，请稍后重试",icon:"none"}),Promise.reject(e)))(n,e).catch(s)}})}))),m={get:(e,t={},n={})=>f({url:e,data:t,method:"GET",...n}),post:(e,t={},n={})=>f({url:e,data:t,method:"POST",...n}),put:(e,t={},n={})=>f({url:e,data:t,method:"PUT",...n}),delete:(e,t={},n={})=>f({url:e,data:t,method:"DELETE",...n}),upload:(e,t,n={},s={})=>{const o={url:(s.baseURL||p.baseURL)+e,filePath:t,name:s.name||"file",formData:n,header:s.header||{}},r=uni.getStorageSync("authorization");return r&&(o.header.Authorization=r),new Promise(((e,t)=>{uni.uploadFile({...o,success:n=>{if(n.statusCode>=200&&n.statusCode<300)try{const t=JSON.parse(n.data);e(t)}catch(Ne){e(n.data)}else t(n)},fail:t})}))}},y={config:p,request:f,http:m},_={pages:[{path:"pages/login/login",style:{navigationBarTitleText:"账号登陆"}},{path:"pages/tabbar/index/index",style:{navigationBarTitleText:"桂圆可爱"}},{path:"pages/tabbar/home/<USER>",style:{navigationBarTitleText:"个人主页"}},{path:"pages/tabbar/message/message",style:{navigationBarTitleText:"我的消息"}},{path:"pages/tabbar/daily/daily",style:{navigationBarTitleText:"发布日常"}}],globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},tabBar:{color:"#F0AD4E",selectedColor:"#3cc51f",borderStyle:"black",backgroundColor:"#F8F8F8",list:[{pagePath:"pages/tabbar/index/index",iconPath:"/static/index/index.png",selectedIconPath:"/static/index/indexselect.png",text:"首页"},{pagePath:"pages/tabbar/daily/daily",iconPath:"/static/daily/daily.png",selectedIconPath:"/static/daily/dailyselect.png",text:"日常"},{pagePath:"pages/tabbar/message/message",iconPath:"/static/message/message.png",selectedIconPath:"/static/message/messageselect.png",text:"消息"},{pagePath:"pages/tabbar/home/<USER>",iconPath:"/static/home/<USER>",selectedIconPath:"/static/home/<USER>",text:"主页"}]}};function w(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var v=w((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),s={},o=s.lib={},r=o.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},i=o.WordArray=r.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,n=e.words,s=this.sigBytes,o=e.sigBytes;if(this.clamp(),s%4)for(var r=0;r<o;r++){var i=n[r>>>2]>>>24-r%4*8&255;t[s+r>>>2]|=i<<24-(s+r)%4*8}else for(r=0;r<o;r+=4)t[s+r>>>2]=n[r>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=r.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,s=[],o=function(t){var n=987654321,s=4294967295;return function(){var o=((n=36969*(65535&n)+(n>>16)&s)<<16)+(t=18e3*(65535&t)+(t>>16)&s)&s;return o/=4294967296,(o+=.5)*(e.random()>.5?1:-1)}},r=0;r<t;r+=4){var a=o(4294967296*(n||e.random()));n=987654071*a(),s.push(4294967296*a()|0)}return new i.init(s,t)}}),a=s.enc={},c=a.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],o=0;o<n;o++){var r=t[o>>>2]>>>24-o%4*8&255;s.push((r>>>4).toString(16)),s.push((15&r).toString(16))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s+=2)n[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new i.init(n,t/2)}},l=a.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],o=0;o<n;o++){var r=t[o>>>2]>>>24-o%4*8&255;s.push(String.fromCharCode(r))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s++)n[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new i.init(n,t)}},u=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(l.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return l.parse(unescape(encodeURIComponent(e)))}},d=o.BufferedBlockAlgorithm=r.extend({reset:function(){this._data=new i.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=u.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,s=n.words,o=n.sigBytes,r=this.blockSize,a=o/(4*r),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*r,l=e.min(4*c,o);if(c){for(var u=0;u<c;u+=r)this._doProcessBlock(s,u);var d=s.splice(0,c);n.sigBytes-=l}return new i.init(d,l)},clone:function(){var e=r.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=d.extend({cfg:r.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}});var h=s.algo={};return s}(Math),n)})),k=v,S=(w((function(e,t){var n;e.exports=(n=k,function(e){var t=n,s=t.lib,o=s.WordArray,r=s.Hasher,i=t.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=i.MD5=r.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var s=t+n,o=e[s];e[s]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var r=this._hash.words,i=e[t+0],c=e[t+1],g=e[t+2],p=e[t+3],f=e[t+4],m=e[t+5],y=e[t+6],_=e[t+7],w=e[t+8],v=e[t+9],k=e[t+10],S=e[t+11],T=e[t+12],b=e[t+13],I=e[t+14],A=e[t+15],P=r[0],E=r[1],x=r[2],C=r[3];P=l(P,E,x,C,i,7,a[0]),C=l(C,P,E,x,c,12,a[1]),x=l(x,C,P,E,g,17,a[2]),E=l(E,x,C,P,p,22,a[3]),P=l(P,E,x,C,f,7,a[4]),C=l(C,P,E,x,m,12,a[5]),x=l(x,C,P,E,y,17,a[6]),E=l(E,x,C,P,_,22,a[7]),P=l(P,E,x,C,w,7,a[8]),C=l(C,P,E,x,v,12,a[9]),x=l(x,C,P,E,k,17,a[10]),E=l(E,x,C,P,S,22,a[11]),P=l(P,E,x,C,T,7,a[12]),C=l(C,P,E,x,b,12,a[13]),x=l(x,C,P,E,I,17,a[14]),P=u(P,E=l(E,x,C,P,A,22,a[15]),x,C,c,5,a[16]),C=u(C,P,E,x,y,9,a[17]),x=u(x,C,P,E,S,14,a[18]),E=u(E,x,C,P,i,20,a[19]),P=u(P,E,x,C,m,5,a[20]),C=u(C,P,E,x,k,9,a[21]),x=u(x,C,P,E,A,14,a[22]),E=u(E,x,C,P,f,20,a[23]),P=u(P,E,x,C,v,5,a[24]),C=u(C,P,E,x,I,9,a[25]),x=u(x,C,P,E,p,14,a[26]),E=u(E,x,C,P,w,20,a[27]),P=u(P,E,x,C,b,5,a[28]),C=u(C,P,E,x,g,9,a[29]),x=u(x,C,P,E,_,14,a[30]),P=d(P,E=u(E,x,C,P,T,20,a[31]),x,C,m,4,a[32]),C=d(C,P,E,x,w,11,a[33]),x=d(x,C,P,E,S,16,a[34]),E=d(E,x,C,P,I,23,a[35]),P=d(P,E,x,C,c,4,a[36]),C=d(C,P,E,x,f,11,a[37]),x=d(x,C,P,E,_,16,a[38]),E=d(E,x,C,P,k,23,a[39]),P=d(P,E,x,C,b,4,a[40]),C=d(C,P,E,x,i,11,a[41]),x=d(x,C,P,E,p,16,a[42]),E=d(E,x,C,P,y,23,a[43]),P=d(P,E,x,C,v,4,a[44]),C=d(C,P,E,x,T,11,a[45]),x=d(x,C,P,E,A,16,a[46]),P=h(P,E=d(E,x,C,P,g,23,a[47]),x,C,i,6,a[48]),C=h(C,P,E,x,_,10,a[49]),x=h(x,C,P,E,I,15,a[50]),E=h(E,x,C,P,m,21,a[51]),P=h(P,E,x,C,T,6,a[52]),C=h(C,P,E,x,p,10,a[53]),x=h(x,C,P,E,k,15,a[54]),E=h(E,x,C,P,c,21,a[55]),P=h(P,E,x,C,w,6,a[56]),C=h(C,P,E,x,A,10,a[57]),x=h(x,C,P,E,y,15,a[58]),E=h(E,x,C,P,b,21,a[59]),P=h(P,E,x,C,f,6,a[60]),C=h(C,P,E,x,S,10,a[61]),x=h(x,C,P,E,g,15,a[62]),E=h(E,x,C,P,v,21,a[63]),r[0]=r[0]+P|0,r[1]=r[1]+E|0,r[2]=r[2]+x|0,r[3]=r[3]+C|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,o=8*t.sigBytes;n[o>>>5]|=128<<24-o%32;var r=e.floor(s/4294967296),i=s;n[15+(o+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),n[14+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),t.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,c=a.words,l=0;l<4;l++){var u=c[l];c[l]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return a},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,n,s,o,r,i){var a=e+(t&n|~t&s)+o+i;return(a<<r|a>>>32-r)+t}function u(e,t,n,s,o,r,i){var a=e+(t&s|n&~s)+o+i;return(a<<r|a>>>32-r)+t}function d(e,t,n,s,o,r,i){var a=e+(t^n^s)+o+i;return(a<<r|a>>>32-r)+t}function h(e,t,n,s,o,r,i){var a=e+(n^(t|~s))+o+i;return(a<<r|a>>>32-r)+t}t.MD5=r._createHelper(c),t.HmacMD5=r._createHmacHelper(c)}(Math),n.MD5)})),w((function(e,t){var n,s,o;e.exports=(s=(n=k).lib.Base,o=n.enc.Utf8,void(n.algo.HMAC=s.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=o.parse(t));var n=e.blockSize,s=4*n;t.sigBytes>s&&(t=e.finalize(t)),t.clamp();for(var r=this._oKey=t.clone(),i=this._iKey=t.clone(),a=r.words,c=i.words,l=0;l<n;l++)a[l]^=1549556828,c[l]^=909522486;r.sigBytes=i.sigBytes=s,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})))})),w((function(e,t){e.exports=k.HmacMD5}))),T=w((function(e,t){e.exports=k.enc.Utf8})),b=w((function(e,t){var n,s,o;e.exports=(o=(s=n=k).lib.WordArray,s.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,s=this._map;e.clamp();for(var o=[],r=0;r<n;r+=3)for(var i=(t[r>>>2]>>>24-r%4*8&255)<<16|(t[r+1>>>2]>>>24-(r+1)%4*8&255)<<8|t[r+2>>>2]>>>24-(r+2)%4*8&255,a=0;a<4&&r+.75*a<n;a++)o.push(s.charAt(i>>>6*(3-a)&63));var c=s.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,n=this._map,s=this._reverseMap;if(!s){s=this._reverseMap=[];for(var r=0;r<n.length;r++)s[n.charCodeAt(r)]=r}var i=n.charAt(64);if(i){var a=e.indexOf(i);-1!==a&&(t=a)}return function(e,t,n){for(var s=[],r=0,i=0;i<t;i++)if(i%4){var a=n[e.charCodeAt(i-1)]<<i%4*2,c=n[e.charCodeAt(i)]>>>6-i%4*2;s[r>>>2]|=(a|c)<<24-r%4*8,r++}return o.create(s,r)}(e,t,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},n.enc.Base64)}));const I="uni_id_token",A="uni_id_token_expired",P="FUNCTION",E="OBJECT",x="CLIENT_DB",C="pending",N="rejected";function O(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function L(e){return"object"===O(e)}function U(e){return"function"==typeof e}function D(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}const R="REJECTED",M="NOT_PENDING";class q{constructor({createPromise:e,retryRule:t=R}={}){this.createPromise=e,this.status=null,this.promise=null,this.retryRule=t}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case R:return this.status===N;case M:return this.status!==C}}exec(){return this.needRetry?(this.status=C,this.promise=this.createPromise().then((e=>(this.status="fulfilled",Promise.resolve(e))),(e=>(this.status=N,Promise.reject(e)))),this.promise):this.promise}}function F(e){return e&&"string"==typeof e?JSON.parse(e):e}const j=F([]);F("");const V=F('[{"provider":"aliyun","spaceName":"suakitsu","spaceId":"mp-2e6e8eae-0872-49fd-b869-9ea34baed9f1","clientSecret":"nJUVHv9y7feXJzGSaPlaVg==","endpoint":"https://api.next.bspapp.com"}]')||[];let $="";try{$="__UNI__1F0BDA5"}catch(Ne){}let B,K={};function z(e,t={}){var n,s;return n=K,s=e,Object.prototype.hasOwnProperty.call(n,s)||(K[e]=t),K[e]}K=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={};const J=["invoke","success","fail","complete"],H=z("_globalUniCloudInterceptor");function W(e,t){H[e]||(H[e]={}),L(t)&&Object.keys(t).forEach((n=>{J.indexOf(n)>-1&&function(e,t,n){let s=H[e][t];s||(s=H[e][t]=[]),-1===s.indexOf(n)&&U(n)&&s.push(n)}(e,n,t[n])}))}function G(e,t){H[e]||(H[e]={}),L(t)?Object.keys(t).forEach((n=>{J.indexOf(n)>-1&&function(e,t,n){const s=H[e][t];if(!s)return;const o=s.indexOf(n);o>-1&&s.splice(o,1)}(e,n,t[n])})):delete H[e]}function Q(e,t){return e&&0!==e.length?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function Y(e,t){return H[e]&&H[e][t]||[]}function X(e){W("callObject",e)}const Z=z("_globalUniCloudListener"),ee="response",te="needLogin",ne="refreshToken",se="clientdb",oe="cloudfunction",re="cloudobject";function ie(e){return Z[e]||(Z[e]=[]),Z[e]}function ae(e,t){const n=ie(e);n.includes(t)||n.push(t)}function ce(e,t){const n=ie(e),s=n.indexOf(t);-1!==s&&n.splice(s,1)}function le(e,t){const n=ie(e);for(let s=0;s<n.length;s++)(0,n[s])(t)}let ue,de=!1;function he(){return ue||(ue=new Promise((e=>{de&&e(),function t(){if("function"==typeof getCurrentPages){const t=getCurrentPages();t&&t[0]&&(de=!0,e())}de||setTimeout((()=>{t()}),30)}()})),ue)}function ge(e){const t={};for(const n in e){const s=e[n];U(s)&&(t[n]=D(s))}return t}class pe extends Error{constructor(e){super(e.message),this.errMsg=e.message||e.errMsg||"unknown system error",this.code=this.errCode=e.code||e.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=e.subject||e.errSubject,this.cause=e.cause,this.requestId=e.requestId}toJson(e=0){if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}var fe={request:e=>uni.request(e),uploadFile:e=>uni.uploadFile(e),setStorageSync:(e,t)=>uni.setStorageSync(e,t),getStorageSync:e=>uni.getStorageSync(e),removeStorageSync:e=>uni.removeStorageSync(e),clearStorageSync:()=>uni.clearStorageSync(),connectSocket:e=>uni.connectSocket(e)};function me(e){return e&&me(e.__v_raw)||e}function ye(){return{token:fe.getStorageSync(I)||fe.getStorageSync("uniIdToken"),tokenExpired:fe.getStorageSync(A)}}function _e({token:e,tokenExpired:t}={}){e&&fe.setStorageSync(I,e),t&&fe.setStorageSync(A,t)}let we,ve;function ke(){return we||(we=uni.getSystemInfoSync()),we}function Se(){let e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;const{scene:n,channel:s}=uni.getLaunchOptionsSync();e=s,t=n}}catch(n){}return{channel:e,scene:t}}let Te={};function be(){const e=uni.getLocale&&uni.getLocale()||"en";if(ve)return{...Te,...ve,locale:e,LOCALE:e};const t=ke(),{deviceId:n,osName:s,uniPlatform:o,appId:r}=t,i=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(const a in t)Object.hasOwnProperty.call(t,a)&&-1===i.indexOf(a)&&delete t[a];return ve={PLATFORM:o,OS:s,APPID:r,DEVICEID:n,...Se(),...t},{...Te,...ve,locale:e,LOCALE:e}}var Ie=function(e,t){let n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),S(n,t).toString()},Ae=function(e,t){return new Promise(((n,s)=>{t(Object.assign(e,{complete(e){e||(e={});const t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){const n=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",o=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return s(new pe({code:n,message:o,requestId:t}))}const o=e.data;if(o.error)return s(new pe({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,n(o)}}))}))},Pe=function(e){return b.stringify(T.parse(e))},Ee={init(e){const t=new class{constructor(e){["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),this.config=Object.assign({},{endpoint:0===e.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=fe,this._getAccessTokenPromiseHub=new q({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((e=>{if(!e.result||!e.result.accessToken)throw new pe({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(e.result.accessToken)})),retryRule:M})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return Ae(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then((()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch((t=>new Promise(((e,n)=>{!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((()=>this.getAccessToken())).then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)})))):this.getAccessToken().then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)}))))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=Ie(t.data,this.config.clientSecret),t}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),s={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,s["x-basement-token"]=this.accessToken),s["x-serverless-sign"]=Ie(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:s}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request({...this.setupRequest(t),timeout:e.timeout})}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:n,filePath:s,fileType:o,onUploadProgress:r}){return new Promise(((i,a)=>{const c=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:s,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success(e){e&&e.statusCode<400?i(e):a(new pe({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){a(new pe({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof r&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{r({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:n="image",cloudPathAsRealPath:s=!1,onUploadProgress:o,config:r}){if("string"!==O(t))throw new pe({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new pe({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new pe({code:"INVALID_PARAM",message:"cloudPath不合法"});const i=r&&r.envType||this.config.envType;if(s&&("/"!==t[0]&&(t="/"+t),t.indexOf("\\")>-1))throw new pe({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const a=(await this.getOSSUploadOptionsFromPath({env:i,filename:s?t.split("/").pop():t,fileId:s?t:void 0})).result,c="https://"+a.cdnDomain+"/"+a.ossPath,{securityToken:l,accessKeyId:u,signature:d,host:h,ossPath:g,id:p,policy:f,ossCallbackUrl:m}=a,y={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:u,Signature:d,host:h,id:p,key:g,policy:f,success_action_status:200};if(l&&(y["x-oss-security-token"]=l),m){const e=JSON.stringify({callbackUrl:m,callbackBody:JSON.stringify({fileId:p,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});y.callback=Pe(e)}const _={url:"https://"+a.host,formData:y,fileName:"file",name:"file",filePath:e,fileType:n};if(await this.uploadFileToOSS(Object.assign({},_,{onUploadProgress:o})),m)return{success:!0,filePath:e,fileID:c};if((await this.reportOSSUpload({id:p})).success)return{success:!0,filePath:e,fileID:c};throw new pe({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise(((t,n)=>{Array.isArray(e)&&0!==e.length||n(new pe({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map((e=>({fileID:e,tempFileURL:e})))})}))}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||0===e.length)throw new pe({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map((e=>e.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}}(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}};const xe="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var Ce,Ne;(Ne=Ce||(Ce={})).local="local",Ne.none="none",Ne.session="session";var Oe=function(){},Le=w((function(e,t){var n;e.exports=(n=k,function(e){var t=n,s=t.lib,o=s.WordArray,r=s.Hasher,i=t.algo,a=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),s=2;s<=n;s++)if(!(t%s))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var s=2,o=0;o<64;)t(s)&&(o<8&&(a[o]=n(e.pow(s,.5))),c[o]=n(e.pow(s,1/3)),o++),s++}();var l=[],u=i.SHA256=r.extend({_doReset:function(){this._hash=new o.init(a.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,s=n[0],o=n[1],r=n[2],i=n[3],a=n[4],u=n[5],d=n[6],h=n[7],g=0;g<64;g++){if(g<16)l[g]=0|e[t+g];else{var p=l[g-15],f=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,m=l[g-2],y=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[g]=f+l[g-7]+y+l[g-16]}var _=s&o^s&r^o&r,w=(s<<30|s>>>2)^(s<<19|s>>>13)^(s<<10|s>>>22),v=h+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&u^~a&d)+c[g]+l[g];h=d,d=u,u=a,a=i+v|0,i=r,r=o,o=s,s=v+(w+_)|0}n[0]=n[0]+s|0,n[1]=n[1]+o|0,n[2]=n[2]+r|0,n[3]=n[3]+i|0,n[4]=n[4]+a|0,n[5]=n[5]+u|0,n[6]=n[6]+d|0,n[7]=n[7]+h|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,o=8*t.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(o+64>>>9<<4)]=e.floor(s/4294967296),n[15+(o+64>>>9<<4)]=s,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=r._createHelper(u),t.HmacSHA256=r._createHmacHelper(u)}(Math),n.SHA256)})),Ue=Le,De=w((function(e,t){e.exports=k.HmacSHA256}));const Re=()=>{let e;if(!Promise){e=()=>{},e.promise={};const t=()=>{throw new pe({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}const t=new Promise(((t,n)=>{e=(e,s)=>e?n(e):t(s)}));return e.promise=t,e};function Me(e){return void 0===e}function qe(e){return"[object Null]"===Object.prototype.toString.call(e)}function Fe(e=""){return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function je(e=32){let t="";for(let n=0;n<e;n++)t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(Math.floor(62*Math.random()));return t}var Ve;!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Ve||(Ve={}));const $e={adapter:null,runtime:void 0},Be=["anonymousUuidKey"];class Ke extends Oe{constructor(){super(),$e.adapter.root.tcbObject||($e.adapter.root.tcbObject={})}setItem(e,t){$e.adapter.root.tcbObject[e]=t}getItem(e){return $e.adapter.root.tcbObject[e]}removeItem(e){delete $e.adapter.root.tcbObject[e]}clear(){delete $e.adapter.root.tcbObject}}function ze(e,t){switch(e){case"local":return t.localStorage||new Ke;case"none":return new Ke;default:return t.sessionStorage||new Ke}}class Je{constructor(e){if(!this._storage){this._persistence=$e.adapter.primaryStorage||e.persistence,this._storage=ze(this._persistence,$e.adapter);const t=`access_token_${e.env}`,n=`access_token_expire_${e.env}`,s=`refresh_token_${e.env}`,o=`anonymous_uuid_${e.env}`,r=`login_type_${e.env}`,i="device_id",a=`token_type_${e.env}`,c=`user_info_${e.env}`;this.keys={accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s,anonymousUuidKey:o,loginTypeKey:r,userInfoKey:c,deviceIdKey:i,tokenTypeKey:a}}}updatePersistence(e){if(e===this._persistence)return;const t="local"===this._persistence;this._persistence=e;const n=ze(e,$e.adapter);for(const s in this.keys){const e=this.keys[s];if(t&&Be.includes(s))continue;const o=this._storage.getItem(e);Me(o)||qe(o)||(n.setItem(e,o),this._storage.removeItem(e))}this._storage=n}setStore(e,t,n){if(!this._storage)return;const s={version:n||"localCachev1",content:t},o=JSON.stringify(s);try{this._storage.setItem(e,o)}catch(r){throw r}}getStore(e,t){try{if(!this._storage)return}catch(s){return""}t=t||"localCachev1";const n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}removeStore(e){this._storage.removeItem(e)}}const He={},We={};function Ge(e){return He[e]}class Qe{constructor(e,t){this.data=t||null,this.name=e}}class Ye extends Qe{constructor(e,t){super("error",{error:e,data:t}),this.error=e}}const Xe=new class{constructor(){this._listeners={}}on(e,t){return n=e,s=t,(o=this._listeners)[n]=o[n]||[],o[n].push(s),this;var n,s,o}off(e,t){return function(e,t,n){if(n&&n[e]){const s=n[e].indexOf(t);-1!==s&&n[e].splice(s,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof Ye)return console.error(e.error),this;const n="string"==typeof e?new Qe(e,t||{}):e,s=n.name;if(this._listens(s)){n.target=this;const e=this._listeners[s]?[...this._listeners[s]]:[];for(const t of e)t.call(this,n)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function Ze(e,t){Xe.on(e,t)}function et(e,t={}){Xe.fire(e,t)}function tt(e,t){Xe.off(e,t)}const nt="loginStateChanged",st="loginStateExpire",ot="loginTypeChanged",rt="anonymousConverted",it="refreshAccessToken";var at;!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(at||(at={}));class ct{constructor(){this._fnPromiseMap=new Map}async run(e,t){let n=this._fnPromiseMap.get(e);return n||(n=new Promise((async(n,s)=>{try{await this._runIdlePromise();const s=t();n(await s)}catch(o){s(o)}finally{this._fnPromiseMap.delete(e)}})),this._fnPromiseMap.set(e,n)),n}_runIdlePromise(){return Promise.resolve()}}class lt{constructor(e){this._singlePromise=new ct,this._cache=Ge(e.env),this._baseURL=`https://${e.env}.ap-shanghai.tcb-api.tencentcloudapi.com`,this._reqClass=new $e.adapter.reqClass({timeout:e.timeout,timeoutMsg:`请求在${e.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]})}_getDeviceId(){if(this._deviceID)return this._deviceID;const{deviceIdKey:e}=this._cache.keys;let t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=je(),this._cache.setStore(e,t)),this._deviceID=t,t}async _request(e,t,n={}){const s={"x-request-id":je(),"x-device-id":this._getDeviceId()};if(n.withAccessToken){const{tokenTypeKey:e}=this._cache.keys,t=await this.getAccessToken(),n=this._cache.getStore(e);s.authorization=`${n} ${t}`}return this._reqClass["get"===n.method?"get":"post"]({url:`${this._baseURL}${e}`,data:t,headers:s})}async _fetchAccessToken(){const{loginTypeKey:e,accessTokenKey:t,accessTokenExpireKey:n,tokenTypeKey:s}=this._cache.keys,o=this._cache.getStore(e);if(o&&o!==at.ANONYMOUS)throw new pe({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});const r=await this._singlePromise.run("fetchAccessToken",(async()=>(await this._request("/auth/v1/signin/anonymously",{},{method:"post"})).data)),{access_token:i,expires_in:a,token_type:c}=r;return this._cache.setStore(s,c),this._cache.setStore(t,i),this._cache.setStore(n,Date.now()+1e3*a),i}isAccessTokenExpired(e,t){let n=!0;return e&&t&&(n=t<Date.now()),n}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),s=this._cache.getStore(t);return this.isAccessTokenExpired(n,s)?this._fetchAccessToken():n}async refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,loginTypeKey:n}=this._cache.keys;return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.setStore(n,at.ANONYMOUS),this.getAccessToken()}async getUserInfo(){return this._singlePromise.run("getUserInfo",(async()=>(await this._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"})).data))}}const ut=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],dt={"X-SDK-Version":"1.3.5"};function ht(e,t,n){const s=e[t];e[t]=function(t){const o={},r={};n.forEach((n=>{const{data:s,headers:i}=n.call(e,t);Object.assign(o,s),Object.assign(r,i)}));const i=t.data;return i&&(()=>{var e;if(e=i,"[object FormData]"!==Object.prototype.toString.call(e))t.data={...i,...o};else for(const t in o)i.append(t,o[t])})(),t.headers={...t.headers||{},...r},s.call(e,t)}}function gt(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...dt,"x-seqid":e}}}class pt{constructor(e={}){var t;this.config=e,this._reqClass=new $e.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=Ge(this.config.env),this._localCache=(t=this.config.env,We[t]),this.oauth=new lt(this.config),ht(this._reqClass,"post",[gt]),ht(this._reqClass,"upload",[gt]),ht(this._reqClass,"download",[gt])}async post(e){return await this._reqClass.post(e)}async upload(e){return await this._reqClass.upload(e)}async download(e){return await this._reqClass.download(e)}async refreshAccessToken(){let e,t;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{e=await this._refreshAccessTokenPromise}catch(n){t=n}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,t)throw t;return e}async _refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n,loginTypeKey:s,anonymousUuidKey:o}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(t);let r=this._cache.getStore(n);if(!r)throw new pe({message:"未登录CloudBase"});const i={refresh_token:r},a=await this.request("auth.fetchAccessTokenWithRefreshToken",i);if(a.data.code){const{code:e}=a.data;if("SIGN_PARAM_INVALID"===e||"REFRESH_TOKEN_EXPIRED"===e||"INVALID_REFRESH_TOKEN"===e){if(this._cache.getStore(s)===at.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===e){const e=this._cache.getStore(o),t=this._cache.getStore(n),s=await this.send("auth.signInAnonymously",{anonymous_uuid:e,refresh_token:t});return this.setRefreshToken(s.refresh_token),this._refreshAccessToken()}et(st),this._cache.removeStore(n)}throw new pe({code:a.data.code,message:`刷新access token失败：${a.data.code}`})}if(a.data.access_token)return et(it),this._cache.setStore(e,a.data.access_token),this._cache.setStore(t,a.data.access_token_expire+Date.now()),{accessToken:a.data.access_token,accessTokenExpire:a.data.access_token_expire};a.data.refresh_token&&(this._cache.removeStore(n),this._cache.setStore(n,a.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n}=this._cache.keys;if(!this._cache.getStore(n))throw new pe({message:"refresh token不存在，登录状态异常"});let s=this._cache.getStore(e),o=this._cache.getStore(t),r=!0;return this._shouldRefreshAccessTokenHook&&!(await this._shouldRefreshAccessTokenHook(s,o))&&(r=!1),(!s||!o||o<Date.now())&&r?this.refreshAccessToken():{accessToken:s,accessTokenExpire:o}}async request(e,t,n){const s=`x-tcb-trace_${this.config.env}`;let o="application/x-www-form-urlencoded";const r={action:e,env:this.config.env,dataVersion:"2019-08-16",...t};let i;if(-1===ut.indexOf(e)&&(this._cache.keys,r.access_token=await this.oauth.getAccessToken()),"storage.uploadFile"===e){i=new FormData;for(let e in i)i.hasOwnProperty(e)&&void 0!==i[e]&&i.append(e,r[e]);o="multipart/form-data"}else{o="application/json",i={};for(let e in r)void 0!==r[e]&&(i[e]=r[e])}let a={headers:{"content-type":o}};n&&n.timeout&&(a.timeout=n.timeout),n&&n.onUploadProgress&&(a.onUploadProgress=n.onUploadProgress);const c=this._localCache.getStore(s);c&&(a.headers["X-TCB-Trace"]=c);const{parse:l,inQuery:u,search:d}=t;let h={env:this.config.env};l&&(h.parse=!0),u&&(h={...u,...h});let g=function(e,t,n={}){const s=/\?/.test(t);let o="";for(let r in n)""===o?!s&&(t+="?"):o+="&",o+=`${r}=${encodeURIComponent(n[r])}`;return/^http(s)?\:\/\//.test(t+=o)?t:`${e}${t}`}(xe,"//tcb-api.tencentcloudapi.com/web",h);d&&(g+=d);const p=await this.post({url:g,data:i,...a}),f=p.header&&p.header["x-tcb-trace"];if(f&&this._localCache.setStore(s,f),200!==Number(p.status)&&200!==Number(p.statusCode)||!p.data)throw new pe({code:"NETWORK_ERROR",message:"network request error"});return p}async send(e,t={},n={}){const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(("ACCESS_TOKEN_DISABLED"===s.data.code||"ACCESS_TOKEN_EXPIRED"===s.data.code)&&-1===ut.indexOf(e)){await this.oauth.refreshAccessToken();const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(s.data.code)throw new pe({code:s.data.code,message:Fe(s.data.message)});return s.data}if(s.data.code)throw new pe({code:s.data.code,message:Fe(s.data.message)});return s.data}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}}const ft={};function mt(e){return ft[e]}class yt{constructor(e){this.config=e,this._cache=Ge(e.env),this._request=mt(e.env)}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}setAccessToken(e,t){const{accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys;this._cache.setStore(n,e),this._cache.setStore(s,t)}async refreshUserInfo(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e)}}class _t{constructor(e){if(!e)throw new pe({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=e,this._cache=Ge(this._envId),this._request=mt(this._envId),this.setUserInfo()}linkWithTicket(e){if("string"!=typeof e)throw new pe({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}linkWithRedirect(e){e.signInWithRedirect()}updatePassword(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}updateEmail(e){return this._request.send("auth.updateEmail",{newEmail:e})}updateUsername(e){if("string"!=typeof e)throw new pe({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}async getLinkedUidList(){const{data:e}=await this._request.send("auth.getLinkedUidList",{});let t=!1;const{users:n}=e;return n.forEach((e=>{e.wxOpenId&&e.wxPublicId&&(t=!0)})),{users:n,hasPrimaryUid:t}}setPrimaryUid(e){return this._request.send("auth.setPrimaryUid",{uid:e})}unlink(e){return this._request.send("auth.unlink",{platform:e})}async update(e){const{nickName:t,gender:n,avatarUrl:s,province:o,country:r,city:i}=e,{data:a}=await this._request.send("auth.updateUserInfo",{nickName:t,gender:n,avatarUrl:s,province:o,country:r,city:i});this.setLocalUserInfo(a)}async refresh(){const e=await this._request.oauth.getUserInfo();return this.setLocalUserInfo(e),e}setUserInfo(){const{userInfoKey:e}=this._cache.keys,t=this._cache.getStore(e);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((e=>{this[e]=t[e]})),this.location={country:t.country,province:t.province,city:t.city}}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e),this.setUserInfo()}}class wt{constructor(e){if(!e)throw new pe({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Ge(e);const{refreshTokenKey:t,accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys,o=this._cache.getStore(t),r=this._cache.getStore(n),i=this._cache.getStore(s);this.credential={refreshToken:o,accessToken:r,accessTokenExpire:i},this.user=new _t(e)}get isAnonymousAuth(){return this.loginType===at.ANONYMOUS}get isCustomAuth(){return this.loginType===at.CUSTOM}get isWeixinAuth(){return this.loginType===at.WECHAT||this.loginType===at.WECHAT_OPEN||this.loginType===at.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class vt extends yt{async signIn(){this._cache.updatePersistence("local"),await this._request.oauth.getAccessToken(),et(nt),et(ot,{env:this.config.env,loginType:at.ANONYMOUS,persistence:"local"});const e=new wt(this.config.env);return await e.user.refresh(),e}async linkAndRetrieveDataWithTicket(e){const{anonymousUuidKey:t,refreshTokenKey:n}=this._cache.keys,s=this._cache.getStore(t),o=this._cache.getStore(n),r=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:s,refresh_token:o,ticket:e});if(r.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(r.refresh_token),await this._request.refreshAccessToken(),et(rt,{env:this.config.env}),et(ot,{loginType:at.CUSTOM,persistence:"local"}),{credential:{refreshToken:r.refresh_token}};throw new pe({message:"匿名转化失败"})}_setAnonymousUUID(e){const{anonymousUuidKey:t,loginTypeKey:n}=this._cache.keys;this._cache.removeStore(t),this._cache.setStore(t,e),this._cache.setStore(n,at.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class kt extends yt{async signIn(e){if("string"!=typeof e)throw new pe({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:t}=this._cache.keys,n=await this._request.send("auth.signInWithTicket",{ticket:e,refresh_token:this._cache.getStore(t)||""});if(n.refresh_token)return this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),et(nt),et(ot,{env:this.config.env,loginType:at.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new wt(this.config.env);throw new pe({message:"自定义登录失败"})}}class St extends yt{async signIn(e,t){if("string"!=typeof e)throw new pe({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:"EMAIL",email:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token:r,access_token_expire:i}=s;if(o)return this.setRefreshToken(o),r&&i?this.setAccessToken(r,i):await this._request.refreshAccessToken(),await this.refreshUserInfo(),et(nt),et(ot,{env:this.config.env,loginType:at.EMAIL,persistence:this.config.persistence}),new wt(this.config.env);throw s.code?new pe({code:s.code,message:`邮箱登录失败: ${s.message}`}):new pe({message:"邮箱登录失败"})}async activate(e){return this._request.send("auth.activateEndUserMail",{token:e})}async resetPasswordWithToken(e,t){return this._request.send("auth.resetPasswordWithToken",{token:e,newPassword:t})}}class Tt extends yt{async signIn(e,t){if("string"!=typeof e)throw new pe({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof t&&(t="",console.warn("password is empty"));const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:at.USERNAME,username:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token_expire:r,access_token:i}=s;if(o)return this.setRefreshToken(o),i&&r?this.setAccessToken(i,r):await this._request.refreshAccessToken(),await this.refreshUserInfo(),et(nt),et(ot,{env:this.config.env,loginType:at.USERNAME,persistence:this.config.persistence}),new wt(this.config.env);throw s.code?new pe({code:s.code,message:`用户名密码登录失败: ${s.message}`}):new pe({message:"用户名密码登录失败"})}}class bt{constructor(e){this.config=e,this._cache=Ge(e.env),this._request=mt(e.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Ze(ot,this._onLoginTypeChanged)}get currentUser(){const e=this.hasLoginState();return e&&e.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new vt(this.config)}customAuthProvider(){return new kt(this.config)}emailAuthProvider(){return new St(this.config)}usernameAuthProvider(){return new Tt(this.config)}async signInAnonymously(){return new vt(this.config).signIn()}async signInWithEmailAndPassword(e,t){return new St(this.config).signIn(e,t)}signInWithUsernameAndPassword(e,t){return new Tt(this.config).signIn(e,t)}async linkAndRetrieveDataWithTicket(e){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new vt(this.config)),Ze(rt,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(e)}async signOut(){if(this.loginType===at.ANONYMOUS)throw new pe({message:"匿名用户不支持登出操作"});const{refreshTokenKey:e,accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,s=this._cache.getStore(e);if(!s)return;const o=await this._request.send("auth.logout",{refresh_token:s});return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.removeStore(n),et(nt),et(ot,{env:this.config.env,loginType:at.NULL,persistence:this.config.persistence}),o}async signUpWithEmailAndPassword(e,t){return this._request.send("auth.signUpWithEmailAndPassword",{email:e,password:t})}async sendPasswordResetEmail(e){return this._request.send("auth.sendPasswordResetEmail",{email:e})}onLoginStateChanged(e){Ze(nt,(()=>{const t=this.hasLoginState();e.call(this,t)}));const t=this.hasLoginState();e.call(this,t)}onLoginStateExpired(e){Ze(st,e.bind(this))}onAccessTokenRefreshed(e){Ze(it,e.bind(this))}onAnonymousConverted(e){Ze(rt,e.bind(this))}onLoginTypeChanged(e){Ze(ot,(()=>{const t=this.hasLoginState();e.call(this,t)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),s=this._cache.getStore(t);return this._request.oauth.isAccessTokenExpired(n,s)?null:new wt(this.config.env)}async isUsernameRegistered(e){if("string"!=typeof e)throw new pe({code:"PARAM_ERROR",message:"username must be a string"});const{data:t}=await this._request.send("auth.isUsernameRegistered",{username:e});return t&&t.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(e){return new kt(this.config).signIn(e)}shouldRefreshAccessToken(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((e=>e.code?e:{...e.data,requestId:e.seqId}))}getAuthHeader(){const{refreshTokenKey:e,accessTokenKey:t}=this._cache.keys,n=this._cache.getStore(e);return{"x-cloudbase-credentials":this._cache.getStore(t)+"/@@/"+n}}_onAnonymousConverted(e){const{env:t}=e.data;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(e){const{loginType:t,persistence:n,env:s}=e.data;s===this.config.env&&(this._cache.updatePersistence(n),this._cache.setStore(this._cache.keys.loginTypeKey,t))}}const It=function(e,t){t=t||Re();const n=mt(this.config.env),{cloudPath:s,filePath:o,onUploadProgress:r,fileType:i="image"}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{const{data:{url:a,authorization:c,token:l,fileId:u,cosFileId:d},requestId:h}=e,g={key:s,signature:c,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":l};n.upload({url:a,data:g,file:o,name:s,fileType:i,onUploadProgress:r}).then((e=>{201===e.statusCode?t(null,{fileID:u,requestId:h}):t(new pe({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${e.data}`}))})).catch((e=>{t(e)}))})).catch((e=>{t(e)})),t.promise},At=function(e,t){t=t||Re();const n=mt(this.config.env),{cloudPath:s}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{t(null,e)})).catch((e=>{t(e)})),t.promise},Pt=function({fileList:e},t){if(t=t||Re(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let s of e)if(!s||"string"!=typeof s)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const n={fileid_list:e};return mt(this.config.env).send("storage.batchDeleteFile",n).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},Et=function({fileList:e},t){t=t||Re(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let n=[];for(let o of e)"object"==typeof o?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),n.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?n.push({fileid:o}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const s={file_list:n};return mt(this.config.env).send("storage.batchGetDownloadUrl",s).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},xt=async function({fileID:e},t){const n=(await Et.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if("SUCCESS"!==n.code)return t?t(n):new Promise((e=>{e(n)}));const s=mt(this.config.env);let o=n.download_url;if(o=encodeURI(o),!t)return s.download({url:o});t(await s.download({url:o}))},Ct=function({name:e,data:t,query:n,parse:s,search:o,timeout:r},i){const a=i||Re();let c;try{c=t?JSON.stringify(t):""}catch(u){return Promise.reject(u)}if(!e)return Promise.reject(new pe({code:"PARAM_ERROR",message:"函数名不能为空"}));const l={inQuery:n,parse:s,search:o,function_name:e,request_data:c};return mt(this.config.env).send("functions.invokeFunction",l,{timeout:r}).then((e=>{if(e.code)a(null,e);else{let n=e.data.response_data;if(s)a(null,{result:n,requestId:e.requestId});else try{n=JSON.parse(e.data.response_data),a(null,{result:n,requestId:e.requestId})}catch(t){a(new pe({message:"response data must be json"}))}}return a.promise})).catch((e=>{a(e)})),a.promise},Nt={timeout:15e3,persistence:"session"},Ot=6e5,Lt={};class Ut{constructor(e){this.config=e||this.config,this.authObj=void 0}init(e){switch($e.adapter||(this.requestClient=new $e.adapter.reqClass({timeout:e.timeout||5e3,timeoutMsg:`请求在${(e.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...Nt,...e},!0){case this.config.timeout>Ot:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=Ot;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new Ut(this.config)}auth({persistence:e}={}){if(this.authObj)return this.authObj;const t=e||$e.adapter.primaryStorage||Nt.persistence;var n;return t!==this.config.persistence&&(this.config.persistence=t),function(e){const{env:t}=e;He[t]=new Je(e),We[t]=new Je({...e,persistence:"local"})}(this.config),n=this.config,ft[n.env]=new pt(n),this.authObj=new bt(this.config),this.authObj}on(e,t){return Ze.apply(this,[e,t])}off(e,t){return tt.apply(this,[e,t])}callFunction(e,t){return Ct.apply(this,[e,t])}deleteFile(e,t){return Pt.apply(this,[e,t])}getTempFileURL(e,t){return Et.apply(this,[e,t])}downloadFile(e,t){return xt.apply(this,[e,t])}uploadFile(e,t){return It.apply(this,[e,t])}getUploadMetadata(e,t){return At.apply(this,[e,t])}registerExtension(e){Lt[e.name]=e}async invokeExtension(e,t){const n=Lt[e];if(!n)throw new pe({message:`扩展${e} 必须先注册`});return await n.invoke(t,this)}useAdapters(e){const{adapter:t,runtime:n}=function(e){const t=(n=e,"[object Array]"===Object.prototype.toString.call(n)?e:[e]);var n;for(const s of t){const{isMatch:e,genAdapter:t,runtime:n}=s;if(e())return{adapter:t(),runtime:n}}}(e)||{};t&&($e.adapter=t),n&&($e.runtime=n)}}var Dt=new Ut;function Rt(e,t,n){void 0===n&&(n={});var s=/\?/.test(t),o="";for(var r in n)""===o?!s&&(t+="?"):o+="&",o+=r+"="+encodeURIComponent(n[r]);return/^http(s)?:\/\//.test(t+=o)?t:""+e+t}class Mt{get(e){const{url:t,data:n,headers:s,timeout:o}=e;return new Promise(((e,r)=>{fe.request({url:Rt("https:",t),data:n,method:"GET",header:s,timeout:o,success(t){e(t)},fail(e){r(e)}})}))}post(e){const{url:t,data:n,headers:s,timeout:o}=e;return new Promise(((e,r)=>{fe.request({url:Rt("https:",t),data:n,method:"POST",header:s,timeout:o,success(t){e(t)},fail(e){r(e)}})}))}upload(e){return new Promise(((t,n)=>{const{url:s,file:o,data:r,headers:i,fileType:a}=e,c=fe.uploadFile({url:Rt("https:",s),name:"file",formData:Object.assign({},r),filePath:o,fileType:a,header:i,success(e){const n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&r.success_action_status&&(n.statusCode=parseInt(r.success_action_status,10)),t(n)},fail(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((t=>{e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}const qt={setItem(e,t){fe.setStorageSync(e,t)},getItem:e=>fe.getStorageSync(e),removeItem(e){fe.removeStorageSync(e)},clear(){fe.clearStorageSync()}};var Ft={genAdapter:function(){return{root:{},reqClass:Mt,localStorage:qt,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Dt.useAdapters(Ft);const jt=Dt,Vt=jt.init;jt.init=function(e){e.env=e.spaceId;const t=Vt.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const n=t.auth;return t.auth=function(e){const t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((e=>{var n;t[e]=(n=t[e],function(e){e=e||{};const{success:t,fail:s,complete:o}=ge(e);if(!(t||s||o))return n.call(this,e);n.call(this,e).then((e=>{t&&t(e),o&&o(e)}),(e=>{s&&s(e),o&&o(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var $t=jt;async function Bt(e,t){const n=`http://${e}:${t}/system/ping`;try{const e=await(s={url:n,timeout:500},new Promise(((e,t)=>{fe.request({...s,success(t){e(t)},fail(e){t(e)}})})));return!(!e.data||0!==e.data.code)}catch(o){return!1}var s}const Kt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"};var zt={init(e){const t=new class{constructor(e){if(["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),!e.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},e),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=fe}async request(e,t=!0){return e=this.setupRequest(e),Promise.resolve().then((()=>Ae(e,this.adapter.request)))}requestLocal(e){return new Promise(((t,n)=>{this.adapter.request(Object.assign(e,{complete(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){const t=e.data&&e.data.code||"SYS_ERR",s=e.data&&e.data.message||"request:fail";return n(new pe({code:t,message:s}))}t({success:!0,result:e.data})}}))}))}setupRequest(e){const t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=Ie(t,this.config.clientSecret);const s=be();n["x-client-info"]=encodeURIComponent(JSON.stringify(s));const{token:o}=ye();return n["x-client-token"]=o,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}async setupLocalRequest(e){const t=be(),{token:n}=ye(),s=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:t,token:n}),{address:o,servePort:r}=this.__dev__&&this.__dev__.debugInfo||{},{address:i}=await async function(e,t){let n;for(let s=0;s<e.length;s++){const o=e[s];if(await Bt(o,t)){n=o;break}}return{address:n,port:t}}(o,r);return{url:`http://${i}:${r}/${Kt[e.method]}`,method:"POST",data:s,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))}}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}getUploadFileOptions(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}reportUploadFile(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}uploadFile({filePath:e,cloudPath:t,fileType:n="image",onUploadProgress:s}){if(!t)throw new pe({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let o;return this.getUploadFileOptions({cloudPath:t}).then((t=>{const{url:r,formData:i,name:a}=t.result;return o=t.result.fileUrl,new Promise(((t,o)=>{const c=this.adapter.uploadFile({url:r,formData:i,name:a,filePath:e,fileType:n,success(e){e&&e.statusCode<400?t(e):o(new pe({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){o(new pe({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((()=>this.reportUploadFile({cloudPath:t}))).then((t=>new Promise(((n,s)=>{t.success?n({success:!0,filePath:e,fileID:o}):s(new pe({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(t).then((e=>{if(e.success)return e.result;throw new pe({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||0===e.length)throw new pe({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(n).then((e=>{if(e.success)return{fileList:e.result.fileList.map((e=>({fileID:e.fileID,tempFileURL:e.tempFileURL})))};throw new pe({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Jt=w((function(e,t){e.exports=k.enc.Hex}));function Ht(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Wt(e="",t={}){const{data:n,functionName:s,method:o,headers:r,signHeaderKeys:i=[],config:a}=t,c=String(Date.now()),l=Ht(),u=Object.assign({},r,{"x-from-app-id":a.spaceAppId,"x-from-env-id":a.spaceId,"x-to-env-id":a.spaceId,"x-from-instance-id":c,"x-from-function-name":s,"x-client-timestamp":c,"x-alipay-source":"client","x-request-id":l,"x-alipay-callid":l,"x-trace-id":l}),d=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(i),[h="",g=""]=e.split("?")||[],p=function(e){const t="HMAC-SHA256",n=e.signedHeaders.join(";"),s=e.signedHeaders.map((t=>`${t.toLowerCase()}:${e.headers[t]}\n`)).join(""),o=Ue(e.body).toString(Jt),r=`${e.method.toUpperCase()}\n${e.path}\n${e.query}\n${s}\n${n}\n${o}\n`,i=Ue(r).toString(Jt),a=`${t}\n${e.timestamp}\n${i}\n`,c=De(a,e.secretKey).toString(Jt);return`${t} Credential=${e.secretId}, SignedHeaders=${n}, Signature=${c}`}({path:h,query:g,method:o,headers:u,timestamp:c,body:JSON.stringify(n),secretId:a.accessKey,secretKey:a.secretKey,signedHeaders:d.sort()});return{url:`${a.endpoint}${e}`,headers:Object.assign({},u,{Authorization:p})}}function Gt({url:e,data:t,method:n="POST",headers:s={},timeout:o}){return new Promise(((r,i)=>{fe.request({url:e,method:n,data:"object"==typeof t?JSON.stringify(t):t,header:s,dataType:"json",timeout:o,complete:(e={})=>{const t=s["x-trace-id"]||"";if(!e.statusCode||e.statusCode>=400){const{message:n,errMsg:s,trace_id:o}=e.data||{};return i(new pe({code:"SYS_ERR",message:n||s||"request:fail",requestId:o||t}))}r({status:e.statusCode,data:e.data,headers:e.header,requestId:t})}})}))}function Qt(e,t){const{path:n,data:s,method:o="GET"}=e,{url:r,headers:i}=Wt(n,{functionName:"",data:s,method:o,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return Gt({url:r,data:s,method:o,headers:i}).then((e=>{const t=e.data||{};if(!t.success)throw new pe({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((e=>{throw new pe({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Yt(e=""){const t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new pe({code:"INVALID_PARAM",message:"fileID不合法"});const s=t.substring(0,n),o=t.substring(n+1);return s!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),o}function Xt(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}class Zt{constructor(e){this.config=e}signedURL(e,t={}){const n=`/ws/function/${e}`,s=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),o=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Ht(),timestamp:""+Date.now()}),r=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return o[e]?"".concat(e,"=").concat(o[e]):null})).filter(Boolean).join("&"),`host:${s}`].join("\n"),i=["HMAC-SHA256",Ue(r).toString(Jt)].join("\n"),a=De(i,this.config.secretKey).toString(Jt),c=Object.keys(o).map((e=>`${e}=${encodeURIComponent(o[e])}`)).join("&");return`${this.config.wsEndpoint}${n}?${c}&signature=${a}`}}var en={init:e=>{e.provider="alipay";const t=new class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),e.endpoint){if("string"!=typeof e.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`,wsEndpoint:e.wsEndpoint||`wss://${e.spaceId}.api-hz.cloudbasefunction.cn`}),this._websocket=new Zt(this.config)}callFunction(e){return function(e,t){const{name:n,data:s,async:o=!1,timeout:r}=e,i="POST",a={"x-to-function-name":n};o&&(a["x-function-invoke-type"]="async");const{url:c,headers:l}=Wt("/functions/invokeFunction",{functionName:n,data:s,method:i,headers:a,signHeaderKeys:["x-to-function-name"],config:t});return Gt({url:c,data:s,method:i,headers:l,timeout:r}).then((e=>{let t=0;if(o){const n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new pe({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((e=>{throw new pe({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:n,formData:s,onUploadProgress:o}){return new Promise(((r,i)=>{const a=fe.uploadFile({url:e,filePath:t,fileType:n,formData:s,name:"file",success(e){e&&e.statusCode<400?r(e):i(new pe({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){i(new pe({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&a&&"function"==typeof a.onProgressUpdate&&a.onProgressUpdate((e=>{o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:e,cloudPath:t="",fileType:n="image",onUploadProgress:s}){if("string"!==O(t))throw new pe({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new pe({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new pe({code:"INVALID_PARAM",message:"cloudPath不合法"});const o=await Qt({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:r,upload_url:i,form_data:a}=o,c=a&&a.reduce(((e,t)=>(e[t.key]=t.value,e)),{});return this.uploadFileToOSS({url:i,filePath:e,fileType:n,formData:c,onUploadProgress:s}).then((()=>({fileID:r})))}async getTempFileURL({fileList:e}){return new Promise(((t,n)=>{(!e||e.length<0)&&t({code:"INVALID_PARAM",message:"fileList不能为空数组"}),e.length>50&&t({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});const s=[];for(const r of e){let e;"string"!==O(r)&&t({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{e=Yt.call(this,r)}catch(o){console.warn(o.errCode,o.errMsg),e=r}s.push({file_id:e,expire:600})}Qt({path:"/?download_url",data:{file_list:s},method:"POST"},this.config).then((e=>{const{file_list:n=[]}=e;t({fileList:n.map((e=>({fileID:Xt.call(this,e.file_id),tempFileURL:e.download_url})))})})).catch((e=>n(e)))}))}async connectWebSocket(e){const{name:t,query:n}=e;return fe.connectSocket({url:this._websocket.signedURL(t,n),complete:()=>{}})}}(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function tn({data:e}){let t;t=be();const n=JSON.parse(JSON.stringify(e||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){const{token:e}=ye();e&&(n.uniIdToken=e)}return n}const nn=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var sn=/[\\^$.*+?()[\]{}|]/g,on=RegExp(sn.source);function rn(e,t,n){return e.replace(new RegExp((s=t)&&on.test(s)?s.replace(sn,"\\$&"):s,"g"),n);var s}const an="request",cn="response",ln="both",un="_globalUniCloudStatus",dn={code:2e4,message:"System error"},hn={code:20101,message:"Invalid client"};function gn(e){const{errSubject:t,subject:n,errCode:s,errMsg:o,code:r,message:i,cause:a}=e||{};return new pe({subject:t||n||"uni-secure-network",code:s||r||dn.code,message:o||i,cause:a})}let pn;function fn({secretType:e}={}){return e===an||e===cn||e===ln}function mn({name:e,data:t={}}={}){return"DCloud-clientDB"===e&&"encryption"===t.redirectTo&&"getAppClientKey"===t.action}function yn({functionName:e,result:t,logPvd:n}){}function _n(e){const t=e.callFunction,n=function(n){const s=n.name;n.data=tn.call(e,{data:n.data});const o={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],r=fn(n),i=mn(n),a=r||i;return t.call(this,n).then((e=>(e.errCode=0,!a&&yn.call(this,{functionName:s,result:e,logPvd:o}),Promise.resolve(e))),(e=>(!a&&yn.call(this,{functionName:s,result:e,logPvd:o}),e&&e.message&&(e.message=function({message:e="",extraInfo:t={},formatter:n=[]}={}){for(let s=0;s<n.length;s++){const{rule:o,content:r,mode:i}=n[s],a=e.match(o);if(!a)continue;let c=r;for(let e=1;e<a.length;e++)c=rn(c,`{$${e}}`,a[e]);for(const e in t)c=rn(c,`{${e}}`,t[e]);return"replace"===i?c:e+c}return e}({message:`[${n.name}]: ${e.message}`,formatter:nn,extraInfo:{functionName:s}})),Promise.reject(e))))};e.callFunction=function(t){const{provider:s,spaceId:o}=e.config,r=t.name;let i,a;return t.data=t.data||{},i=n,i=i.bind(e),a=mn(t)?n.call(e,t):fn(t)?new pn({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function({provider:e,spaceId:t,functionName:n}={}){const{appId:s,uniPlatform:o,osName:r}=ke();let i=o;"app"===o&&(i=r);const a=function({provider:e,spaceId:t}={}){const n=j;if(!n)return{};e=function(e){return"tencent"===e?"tcb":e}(e);const s=n.find((n=>n.provider===e&&n.spaceId===t));return s&&s.config}({provider:e,spaceId:t});if(!a||!a.accessControl||!a.accessControl.enable)return!1;const c=a.accessControl.function||{},l=Object.keys(c);if(0===l.length)return!0;const u=function(e,t){let n,s,o;for(let r=0;r<e.length;r++){const i=e[r];i!==t?"*"!==i?i.split(",").map((e=>e.trim())).indexOf(t)>-1&&(s=i):o=i:n=i}return n||s||o}(l,n);if(!u)return!1;if((c[u]||[]).find(((e={})=>e.appId===s&&(e.platform||"").toLowerCase()===i.toLowerCase())))return!0;throw console.error(`此应用[appId: ${s}, platform: ${i}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),gn(hn)}({provider:s,spaceId:o,functionName:r})?new pn({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):i(t),Object.defineProperty(a,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),a.then((e=>("undefined"!=typeof UTSJSONObject&&(e.result=new UTSJSONObject(e.result)),e)))}}pn=class{constructor(){throw gn({message:"Platform app is not enabled, please check whether secure network module is enabled in your manifest.json"})}};const wn=Symbol("CLIENT_DB_INTERNAL");function vn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=wn,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(e,n,s){if("_uniClient"===n)return null;if("symbol"==typeof n)return e[n];if(n in e||"string"!=typeof n){const t=e[n];return"function"==typeof t?t.bind(e):t}return t.get(e,n,s)}})}function kn(e){return{on:(t,n)=>{e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:(t,n)=>{e[t]=e[t]||[];const s=e[t].indexOf(n);-1!==s&&e[t].splice(s,1)}}}const Sn=["db.Geo","db.command","command.aggregate"];function Tn(e,t){return Sn.indexOf(`${e}.${t}`)>-1}function bn(e){switch(O(e=me(e))){case"array":return e.map((e=>bn(e)));case"object":return e._internalType===wn||Object.keys(e).forEach((t=>{e[t]=bn(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function In(e){return e&&e.content&&e.content.$method}class An{constructor(e,t,n){this.content=e,this.prevStage=t||null,this.udb=null,this._database=n}toJSON(){let e=this;const t=[e.content];for(;e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((e=>({$method:e.$method,$param:bn(e.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const e=this.toJSON().$db.find((e=>"action"===e.$method));return e&&e.$param&&e.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((e=>"action"!==e.$method))}}get isAggregate(){let e=this;for(;e;){const t=In(e),n=In(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}get isCommand(){let e=this;for(;e;){if("command"===In(e))return!0;e=e.prevStage}return!1}get isAggregateCommand(){let e=this;for(;e;){const t=In(e),n=In(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}getNextStageFn(e){const t=this;return function(){return Pn({$method:e,$param:bn(Array.from(arguments))},t,t._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(e,t){const n=this.getAction(),s=this.getCommand();return s.$db.push({$method:e,$param:bn(t)}),this._database._callCloudFunction({action:n,command:s})}}function Pn(e,t,n){return vn(new An(e,t,n),{get(e,t){let s="db";return e&&e.content&&(s=e.content.$method),Tn(s,t)?Pn({$method:t},e,n):function(){return Pn({$method:t,$param:bn(Array.from(arguments))},e,n)}}})}function En({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map((e=>({$method:e}))),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function xn(e,t={}){return vn(new e(t),{get:(e,t)=>Tn("db",t)?Pn({$method:t},null,e):function(){return Pn({$method:t,$param:bn(Array.from(arguments))},null,e)}})}class Cn extends class{constructor({uniClient:e={},isJQL:t=!1}={}){this._uniClient=e,this._authCallBacks={},this._dbCallBacks={},e._isDefault&&(this._dbCallBacks=z("_globalUniCloudDatabaseCallback")),t||(this.auth=kn(this._authCallBacks)),this._isJQL=t,Object.assign(this,kn(this._dbCallBacks)),this.env=vn({},{get:(e,t)=>({$env:t})}),this.Geo=vn({},{get:(e,t)=>En({path:["Geo"],method:t})}),this.serverDate=En({path:[],method:"serverDate"}),this.RegExp=En({path:[],method:"RegExp"})}getCloudEnv(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}_callback(e,t){const n=this._dbCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}_callbackAuth(e,t){const n=this._authCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}multiSend(){const e=Array.from(arguments),t=e.map((e=>{const t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}{_parseResult(e){return this._isJQL?e.result:e}_callCloudFunction({action:e,command:t,multiCommand:n,queryList:s}){function o(e,t){if(n&&s)for(let n=0;n<s.length;n++){const o=s[n];o.udb&&"function"==typeof o.udb.setResult&&(t?o.udb.setResult(t):o.udb.setResult(e.result.dataList[n]))}}const r=this,i=this._isJQL?"databaseForJQL":"database";function a(e){return r._callback("error",[e]),Q(Y(i,"fail"),e).then((()=>Q(Y(i,"complete"),e))).then((()=>(o(null,e),le(ee,{type:se,content:e}),Promise.reject(e))))}const c=Q(Y(i,"invoke")),l=this._uniClient;return c.then((()=>l.callFunction({name:"DCloud-clientDB",type:x,data:{action:e,command:t,multiCommand:n}}))).then((e=>{const{code:t,message:n,token:s,tokenExpired:c,systemInfo:l=[]}=e.result;if(l)for(let o=0;o<l.length;o++){const{level:e,message:t,detail:n}=l[o];let s="[System Info]"+t;n&&(s=`${s}\n详细信息：${n}`),(console["warn"===e?"error":e]||console.log)(s)}if(t)return a(new pe({code:t,message:n,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,s&&c&&(_e({token:s,tokenExpired:c}),this._callbackAuth("refreshToken",[{token:s,tokenExpired:c}]),this._callback("refreshToken",[{token:s,tokenExpired:c}]),le(ne,{token:s,tokenExpired:c}));const u=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let o=0;o<u.length;o++){const{prop:t,tips:n}=u[o];if(t in e.result){const s=e.result[t];Object.defineProperty(e.result,t,{get:()=>(console.warn(n),s)})}}return d=e,Q(Y(i,"success"),d).then((()=>Q(Y(i,"complete"),d))).then((()=>{o(d,null);const e=r._parseResult(d);return le(ee,{type:se,content:e}),Promise.resolve(e)}));var d}),(e=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),a(new pe({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId})))))}}const Nn="token无效，跳转登录页面",On="token过期，跳转登录页面",Ln={TOKEN_INVALID_TOKEN_EXPIRED:On,TOKEN_INVALID_INVALID_CLIENTID:Nn,TOKEN_INVALID:Nn,TOKEN_INVALID_WRONG_TOKEN:Nn,TOKEN_INVALID_ANONYMOUS_USER:Nn},Un={"uni-id-token-expired":On,"uni-id-check-token-failed":Nn,"uni-id-token-not-exist":Nn,"uni-id-check-device-feature-failed":Nn};function Dn(e,t){let n="";return n=e?`${e}/${t}`:t,n.replace(/^\//,"")}function Rn(e=[],t=""){const n=[],s=[];return e.forEach((e=>{!0===e.needLogin?n.push(Dn(t,e.path)):!1===e.needLogin&&s.push(Dn(t,e.path))})),{needLoginPage:n,notNeedLoginPage:s}}function Mn(e){return e.split("?")[0].replace(/^\//,"")}function qn(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){const e=getCurrentPages();return e[e.length-1]}())}function Fn(){return Mn(qn())}function jn(e="",t={}){if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;const n=t.list,s=Mn(e);return n.some((e=>e.pagePath===s))}const Vn=!!_.uniIdRouter,{loginPage:$n,routerNeedLogin:Bn,resToLogin:Kn,needLoginPage:zn,notNeedLoginPage:Jn,loginPageInTabBar:Hn}=function({pages:e=[],subPackages:t=[],uniIdRouter:n={},tabBar:s={}}=_){const{loginPage:o,needLogin:r=[],resToLogin:i=!0}=n,{needLoginPage:a,notNeedLoginPage:c}=Rn(e),{needLoginPage:l,notNeedLoginPage:u}=function(e=[]){const t=[],n=[];return e.forEach((e=>{const{root:s,pages:o=[]}=e,{needLoginPage:r,notNeedLoginPage:i}=Rn(o,s);t.push(...r),n.push(...i)})),{needLoginPage:t,notNeedLoginPage:n}}(t);return{loginPage:o,routerNeedLogin:r,resToLogin:i,needLoginPage:[...a,...l],notNeedLoginPage:[...c,...u],loginPageInTabBar:jn(o,s)}}();if(zn.indexOf($n)>-1)throw new Error(`Login page [${$n}] should not be "needLogin", please check your pages.json`);function Wn(e){const t=Fn();if("/"===e.charAt(0))return e;const[n,s]=e.split("?"),o=n.replace(/^\//,"").split("/"),r=t.split("/");r.pop();for(let i=0;i<o.length;i++){const e=o[i];".."===e?r.pop():"."!==e&&r.push(e)}return""===r[0]&&r.shift(),"/"+r.join("/")+(s?"?"+s:"")}function Gn({redirect:e}){const t=Mn(e),n=Mn($n);return Fn()!==n&&t!==n}function Qn({api:e,redirect:t}={}){if(!t||!Gn({redirect:t}))return;const n=(o=t,"/"!==(s=$n).charAt(0)&&(s="/"+s),o?s.indexOf("?")>-1?s+`&uniIdRedirectUrl=${encodeURIComponent(o)}`:s+`?uniIdRedirectUrl=${encodeURIComponent(o)}`:s);var s,o;Hn?"navigateTo"!==e&&"redirectTo"!==e||(e="switchTab"):"switchTab"===e&&(e="navigateTo");const r={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((()=>{r[e]({url:n})}),0)}function Yn({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){const{token:e,tokenExpired:t}=ye();let n;if(e){if(t<Date.now()){const e="uni-id-token-expired";n={errCode:e,errMsg:Un[e]}}}else{const e="uni-id-check-token-failed";n={errCode:e,errMsg:Un[e]}}return n}();if(function(e){const t=Mn(Wn(e));return!(Jn.indexOf(t)>-1)&&(zn.indexOf(t)>-1||Bn.some((t=>{return n=e,new RegExp(t).test(n);var n})))}(e)&&n){if(n.uniIdRedirectUrl=e,ie(te).length>0)return setTimeout((()=>{le(te,n)}),0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function Xn(){!function(){const e=qn(),{abortLoginPageJump:t,autoToLoginPage:n}=Yn({url:e});t||n&&Qn({api:"redirectTo",redirect:e})}();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const n=e[t];uni.addInterceptor(n,{invoke(e){const{abortLoginPageJump:t,autoToLoginPage:s}=Yn({url:e.url});return t?e:s?(Qn({api:n,redirect:Wn(e.url)}),!1):e}})}}function Zn(){this.onResponse((e=>{const{type:t,content:n}=e;let s=!1;switch(t){case"cloudobject":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in Un}(n);break;case"clientdb":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in Ln}(n)}s&&function(e={}){const t=ie(te);he().then((()=>{const n=qn();if(n&&Gn({redirect:n}))return t.length>0?le(te,Object.assign({uniIdRedirectUrl:n},e)):void($n&&Qn({api:"navigateTo",redirect:n}))}))}(n)}))}function es(e){var t;(t=e).onResponse=function(e){ae(ee,e)},t.offResponse=function(e){ce(ee,e)},function(e){e.onNeedLogin=function(e){ae(te,e)},e.offNeedLogin=function(e){ce(te,e)},Vn&&(z(un).needLoginInit||(z(un).needLoginInit=!0,he().then((()=>{Xn.call(e)})),Kn&&Zn.call(e)))}(e),function(e){e.onRefreshToken=function(e){ae(ne,e)},e.offRefreshToken=function(e){ce(ne,e)}}(e)}let ts;const ns="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",ss=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function os(){const e=ye().token||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((s=t[1],decodeURIComponent(ts(s).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}var s;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}ts="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!ss.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,s,o="",r=0;r<e.length;)t=ns.indexOf(e.charAt(r++))<<18|ns.indexOf(e.charAt(r++))<<12|(n=ns.indexOf(e.charAt(r++)))<<6|(s=ns.indexOf(e.charAt(r++))),o+=64===n?String.fromCharCode(t>>16&255):64===s?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}:atob;var rs=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(w((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const n="chooseAndUploadFile:ok",s="chooseAndUploadFile:fail";function o(e,t){return e.tempFiles.forEach(((e,n)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}function r(e,t,{onChooseFile:s,onUploadProgress:o}){return t.then((e=>{if(s){const t=s(e);if(void 0!==t)return Promise.resolve(t).then((t=>void 0===t?e:t))}return e})).then((t=>!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t,s=5,o){(t=Object.assign({},t)).errMsg=n;const r=t.tempFiles,i=r.length;let a=0;return new Promise((n=>{for(;a<s;)c();function c(){const s=a++;if(s>=i)return void(!r.find((e=>!e.url&&!e.errMsg))&&n(t));const l=r[s];e.uploadFile({provider:l.provider,filePath:l.path,cloudPath:l.cloudPath,fileType:l.fileType,cloudPathAsRealPath:l.cloudPathAsRealPath,onUploadProgress(e){e.index=s,e.tempFile=l,e.tempFilePath=l.path,o&&o(e)}}).then((e=>{l.url=e.fileID,s<i&&c()})).catch((e=>{l.errMsg=e.errMsg||e.message,s<i&&c()}))}}))}(e,t,5,o)))}t.initChooseAndUploadFile=function(e){return function(t={type:"all"}){return"image"===t.type?r(e,function(e){const{count:t,sizeType:n,sourceType:r=["album","camera"],extension:i}=e;return new Promise(((e,a)=>{uni.chooseImage({count:t,sizeType:n,sourceType:r,extension:i,success(t){e(o(t,"image"))},fail(e){a({errMsg:e.errMsg.replace("chooseImage:fail",s)})}})}))}(t),t):"video"===t.type?r(e,function(e){const{camera:t,compressed:n,maxDuration:r,sourceType:i=["album","camera"],extension:a}=e;return new Promise(((e,c)=>{uni.chooseVideo({camera:t,compressed:n,maxDuration:r,sourceType:i,extension:a,success(t){const{tempFilePath:n,duration:s,size:r,height:i,width:a}=t;e(o({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:r,type:t.tempFile&&t.tempFile.type||"",width:a,height:i,duration:s,fileType:"video",cloudPath:""}]},"video"))},fail(e){c({errMsg:e.errMsg.replace("chooseVideo:fail",s)})}})}))}(t),t):r(e,function(e){const{count:t,extension:n}=e;return new Promise(((e,r)=>{let i=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(i=wx.chooseMessageFile),"function"!=typeof i)return r({errMsg:s+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});i({type:"all",count:t,extension:n,success(t){e(o(t))},fail(e){r({errMsg:e.errMsg.replace("chooseFile:fail",s)})}})}))}(t),t)}}})));const is="manual";function as(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var e=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((t=>{e.push(this[t])})),e}),((e,t)=>{if(this.loadtime===is)return;let n=!1;const s=[];for(let o=2;o<e.length;o++)e[o]!==t[o]&&(s.push(e[o]),n=!0);e[0]!==t[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(n,s)}))},methods:{onMixinDatacomPropsChange(e,t){},mixinDatacomEasyGet({getone:e=!1,success:t,fail:n}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((n=>{this.mixinDatacomLoading=!1;const{data:s,count:o}=n.result;this.getcount&&(this.mixinDatacomPage.count=o),this.mixinDatacomHasMore=s.length<this.pageSize;const r=e?s.length?s[0]:void 0:s;this.mixinDatacomResData=r,t&&t(r)})).catch((e=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e,this.mixinDatacomError=e,n&&n(e)})))},mixinDatacomGet(t={}){let n;t=t||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);const s=t.action||this.action;s&&(n=n.action(s));const o=t.collection||this.collection;n=Array.isArray(o)?n.collection(...o):n.collection(o);const r=t.where||this.where;r&&Object.keys(r).length&&(n=n.where(r));const i=t.field||this.field;i&&(n=n.field(i));const a=t.foreignKey||this.foreignKey;a&&(n=n.foreignKey(a));const c=t.groupby||this.groupby;c&&(n=n.groupBy(c));const l=t.groupField||this.groupField;l&&(n=n.groupField(l)),!0===(void 0!==t.distinct?t.distinct:this.distinct)&&(n=n.distinct());const u=t.orderby||this.orderby;u&&(n=n.orderBy(u));const d=void 0!==t.pageCurrent?t.pageCurrent:this.mixinDatacomPage.current,h=void 0!==t.pageSize?t.pageSize:this.mixinDatacomPage.size,g=void 0!==t.getcount?t.getcount:this.getcount,p=void 0!==t.gettree?t.gettree:this.gettree,f=void 0!==t.gettreepath?t.gettreepath:this.gettreepath,m={getCount:g},y={limitLevel:void 0!==t.limitlevel?t.limitlevel:this.limitlevel,startWith:void 0!==t.startwith?t.startwith:this.startwith};return p&&(m.getTree=y),f&&(m.getTreePath=y),n=n.skip(h*(d-1)).limit(h).get(m),n}}}}function cs(e){return z("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function ls({openid:e,callLoginByWeixin:t=!1}={}){throw cs(this),new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `app`")}async function us(e){const t=cs(this);return t.initPromise||(t.initPromise=ls.call(this,e).then((e=>e)).catch((e=>{throw delete t.initPromise,e}))),t.initPromise}function ds(e){Te=e}function hs(e){const t={getSystemInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(n){return new Promise(((s,o)=>{t[e]({...n,success(e){s(e)},fail(e){o(e)}})}))}}class gs extends class{constructor(){this._callback={}}addListener(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}on(e,t){return this.addListener(e,t)}removeListener(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');const n=this._callback[e];if(!n)return;const s=function(e,t){for(let n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(s,1)}off(e,t){return this.removeListener(e,t)}removeAllListener(e){delete this._callback[e]}emit(e,...t){const n=this._callback[e];if(n)for(let s=0;s<n.length;s++)n[s](...t)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([hs("getSystemInfo")(),hs("getPushClientId")()]).then((([{appId:e}={},{cid:t}={}]=[])=>{if(!e)throw new Error("Invalid appId, please check the manifest.json file");if(!t)throw new Error("Invalid push client id");this._appId=e,this._pushClientId=t,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(e=>{throw this.emit("error",e),this.close(),e}))}async open(){return this.init()}_isUniCloudSSE(e){if("receive"!==e.type)return!1;const t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}_receivePushMessage(e){if(!this._isUniCloudSSE(e))return;const t=e&&e.data&&e.data.payload,{action:n,messageId:s,message:o}=t;this._payloadQueue.push({action:n,messageId:s,message:o}),this._consumMessage()}_consumMessage(){for(;;){const e=this._payloadQueue.find((e=>e.messageId===this._currentMessageId+1));if(!e)break;this._currentMessageId++,this._parseMessagePayload(e)}}_parseMessagePayload(e){const{action:t,messageId:n,message:s}=e;"end"===t?this._end({messageId:n,message:s}):"message"===t&&this._appendMessage({messageId:n,message:s})}_appendMessage({messageId:e,message:t}={}){this.emit("message",t)}_end({messageId:e,message:t}={}){this.emit("end",t),this.close()}_initMessageListener(){uni.onPushMessage(this._uniPushMessageCallback)}_destroy(){uni.offPushMessage(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}const ps={tcb:$t,tencent:$t,aliyun:Ee,private:zt,dcloud:zt,alipay:en};let fs=new class{init(e){let t={};const n=ps[e.provider];if(!n)throw new Error("未提供正确的provider参数");var s;return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new q({createPromise:function(){let t=Promise.resolve();t=new Promise((e=>{setTimeout((()=>{e()}),1)}));const n=e.auth();return t.then((()=>n.getLoginState())).then((e=>e?Promise.resolve():n.signInAnonymously()))}}))}(t),_n(t),function(e){const t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),(s=t).database=function(e){if(e&&Object.keys(e).length>0)return s.init(e).database();if(this._database)return this._database;const t=xn(Cn,{uniClient:s});return this._database=t,t},s.databaseForJQL=function(e){if(e&&Object.keys(e).length>0)return s.init(e).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const t=xn(Cn,{uniClient:s,isJQL:!0});return this._databaseForJQL=t,t},function(e){e.getCurrentUserInfo=os,e.chooseAndUploadFile=rs.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return as(e)}}),e.SSEChannel=gs,e.initSecureNetworkByWeixin=function(e){return function({openid:t,callLoginByWeixin:n=!1}={}){return us.call(e,{openid:t,callLoginByWeixin:n})}}(e),e.setCustomClientInfo=ds,e.importObject=function(t){return function(n,s={}){s=function(e,t={}){return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==typeof t.secretMethods&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},s);const{customUI:o,loadingOptions:r,errorOptions:i,parseSystemError:a}=s,c=!o;return new Proxy({},{get(o,l){switch(l){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:e,interceptorName:t,getCallbackArgs:n}={}){return async function(...s){const o=n?n({params:s}):{};let r,i;try{return await Q(Y(t,"invoke"),{...o}),r=await e(...s),await Q(Y(t,"success"),{...o,result:r}),r}catch(a){throw i=a,await Q(Y(t,"fail"),{...o,error:i}),i}finally{await Q(Y(t,"complete"),i?{...o,error:i}:{...o,result:r})}}}({fn:async function o(...u){let d;c&&uni.showLoading({title:r.title,mask:r.mask});const h={name:n,type:E,data:{method:l,params:u}};"object"==typeof s.secretMethods&&function(e,t){const n=t.data.method,s=e.secretMethods||{},o=s[n]||s["*"];o&&(t.secretType=o)}(s,h);let g=!1;try{d=await t.callFunction(h)}catch(e){g=!0,d={result:new pe(e)}}const{errSubject:p,errCode:f,errMsg:m,newToken:y}=d.result||{};if(c&&uni.hideLoading(),y&&y.token&&y.tokenExpired&&(_e(y),le(ne,{...y})),f){let e=m;if(g&&a&&(e=(await a({objectName:n,methodName:l,params:u,errSubject:p,errCode:f,errMsg:m})).errMsg||m),c)if("toast"===i.type)uni.showToast({title:e,icon:"none"});else{if("modal"!==i.type)throw new Error(`Invalid errorOptions.type: ${i.type}`);{const{confirm:t}=await async function({title:e,content:t,showCancel:n,cancelText:s,confirmText:o}={}){return new Promise(((r,i)=>{uni.showModal({title:e,content:t,showCancel:n,cancelText:s,confirmText:o,success(e){r(e)},fail(){r({confirm:!1,cancel:!0})}})}))}({title:"提示",content:e,showCancel:i.retry,cancelText:"取消",confirmText:i.retry?"重试":"确定"});if(i.retry&&t)return o(...u)}}const t=new pe({subject:p,code:f,message:m,requestId:d.requestId});throw t.detail=d.result,le(ee,{type:re,content:t}),t}return le(ee,{type:re,content:d.result}),d.result},interceptorName:"callObject",getCallbackArgs:function({params:e}={}){return{objectName:n,methodName:l,params:e}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((e=>{if(!t[e])return;const n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){let s=!1;if("callFunction"===t){const e=n&&n.type||P;s=e!==P}const o="callFunction"===t&&!s,r=this._initPromiseHub.exec();n=n||{};const{success:i,fail:a,complete:c}=ge(n),l=r.then((()=>s?Promise.resolve():Q(Y(t,"invoke"),n))).then((()=>e.call(this,n))).then((e=>s?Promise.resolve(e):Q(Y(t,"success"),e).then((()=>Q(Y(t,"complete"),e))).then((()=>(o&&le(ee,{type:oe,content:e}),Promise.resolve(e))))),(e=>s?Promise.reject(e):Q(Y(t,"fail"),e).then((()=>Q(Y(t,"complete"),e))).then((()=>(le(ee,{type:oe,content:e}),Promise.reject(e))))));if(!(i||a||c))return l;l.then((e=>{i&&i(e),c&&c(e),o&&le(ee,{type:oe,content:e})}),(e=>{a&&a(e),c&&c(e),o&&le(ee,{type:oe,content:e})}))}}(t[e],e).bind(t)})),t.init=this.init,t}};(()=>{const e=V;let t={};if(e&&1===e.length)t=e[0],fs=fs.init(t),fs._isDefault=!0;else{const t=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let n;n=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",t.forEach((e=>{fs[e]=function(){return console.error(n),Promise.reject(new pe({code:"SYS_ERR",message:n}))}}))}Object.assign(fs,{get mixinDatacom(){return as(fs)}}),es(fs),fs.addInterceptor=W,fs.removeInterceptor=G,fs.interceptObject=X,uni.__uniCloud=fs;{const e=B||(B=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),B);e.uniCloud=fs,e.UniCloudError=pe}})();const{app:ms,Vuex:ys,Pinia:_s}=function(){const t=e.createVueApp(g);return t.config.globalProperties.$request=y.request,t.config.globalProperties.$http=y.http,t.config.globalProperties.baseURL=BASE_URL,{app:t}}();uni.Vuex=ys,uni.Pinia=_s,ms.provide("__globalStyles",__uniConfig.styles),ms._component.mpType="app",ms._component.render=()=>{},ms.mount("#app")}(Vue);
