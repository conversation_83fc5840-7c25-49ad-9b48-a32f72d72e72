if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...n){uni.__log__?uni.__log__(e,t,...n):console[e].apply(console,[...n,t])}const n=(t=>(n,s=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,n,s)})("onLoad"),s="/static/pet/rabbit.png",o={BASE_URL:"http://124.223.80.197:8080/",API_TIMEOUT:15e3,ENV:"production"},a=o.BASE_URL,r=o.API_TIMEOUT,i=o.ENV,c={__name:"splash",setup(n){const o=e.ref("正在启动...");e.onMounted((async()=>{t("log","at pages/splash/splash.vue:22","启动页面加载"),await r()}));const r=async()=>{const e=uni.getStorageSync("authorization");if(e){o.value="验证登录状态...",t("log","at pages/splash/splash.vue:33","检测到本地cookie，验证有效性...");try{const n=await uni.request({url:a+"user",method:"GET",header:{Cookie:`Authorization=${e}`,"Content-Type":"application/json"},withCredentials:!0});t("log","at pages/splash/splash.vue:46","Cookie验证结果:",n),200===n.statusCode&&n.data&&0===n.data.code?(o.value="登录成功，进入首页...",t("log","at pages/splash/splash.vue:51","Cookie有效，自动登录成功"),setTimeout((()=>{uni.reLaunch({url:"/pages/tabbar/index/index"})}),1e3)):i("Cookie无效")}catch(n){t("log","at pages/splash/splash.vue:63","Cookie验证失败:",n),i("网络错误")}}else i("未登录")},i=e=>{o.value="需要登录...",t("log","at pages/splash/splash.vue:74","跳转到登录页，原因:",e),uni.removeStorageSync("authorization"),setTimeout((()=>{uni.reLaunch({url:"/pages/login/login"})}),1e3)};return(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"splash-container"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("image",{class:"logo",src:s}),e.createElementVNode("text",{class:"app-title"},"GuiYuan"),e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(o.value),1)]),e.createElementVNode("view",{class:"loading-indicator"},[e.createElementVNode("view",{class:"spinner"})])]))}},l={__name:"login",setup(n){const o=e.ref(""),r=e.ref(""),i=e.ref(!1),c=e.ref(60),l=()=>{o.value=o.value.replace(/\D/g,""),o.value.length>11&&(o.value=o.value.slice(0,11))};e.onMounted((async()=>{t("log","at pages/login/login.vue:53","登录页面加载完成")}));const u=()=>{o.value?11===o.value.length?i.value||((()=>{i.value=!0,c.value=60;const e=setInterval((()=>{c.value--,c.value<=0&&(clearInterval(e),i.value=!1)}),1e3)})(),uni.request({url:a+"user",method:"POST",data:{phone:o.value},header:{"Content-Type":"application/json"}}).then((e=>{t("log","at pages/login/login.vue:108",e),uni.showToast({title:"验证码已发送",icon:"success"})})).catch((e=>{t("log","at pages/login/login.vue:114",e),uni.showToast({title:"发送失败，请重试",icon:"none"}),i.value=!1}))):uni.showToast({title:"请输入11位手机号",icon:"none"}):uni.showToast({title:"请输入手机号",icon:"none"})},d=()=>{o.value&&r.value?11===o.value.length?6===r.value.length?uni.request({url:a+"user",method:"POST",data:{phone:o.value,code:r.value},header:{"Content-Type":"application/json"}}).then((e=>{t("log","at pages/login/login.vue:161",e),uni.setStorageSync("authorization",e.data.data.authorization),t("log","at pages/login/login.vue:165","保存的authorization:",uni.getStorageSync("authorization")),uni.reLaunch({url:"/pages/tabbar/index/index",success:function(){setTimeout((()=>{uni.showToast({title:"登录成功",icon:"success"})}),200)},fail:function(e){t("error","at pages/login/login.vue:180","跳转到首页失败:",e)}})})).catch((e=>{t("log","at pages/login/login.vue:184",e),uni.showToast({title:"登录失败，请重试",icon:"none"})})):uni.showToast({title:"验证码格式错误",icon:"none"}):uni.showToast({title:"请输入11位手机号",icon:"none"}):uni.showToast({title:"请输入手机号和验证码",icon:"none"})};return(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"login-page"},[e.createElementVNode("image",{class:"bg-image",src:s,mode:"aspectFill"}),e.createElementVNode("view",{class:"login-container"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("image",{class:"logo",src:s}),e.createElementVNode("text",{class:"app-title"},"GuiYuan")]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-item"},[e.withDirectives(e.createElementVNode("input",{type:"number",maxlength:"11","onUpdate:modelValue":n[0]||(n[0]=e=>o.value=e),placeholder:"请输入手机号","placeholder-class":"placeholder",onInput:l},null,544),[[e.vModelText,o.value]])]),e.createElementVNode("view",{class:"code-container"},[e.createElementVNode("view",{class:"input-item code-input"},[e.withDirectives(e.createElementVNode("input",{type:"number",maxlength:"6","onUpdate:modelValue":n[1]||(n[1]=e=>r.value=e),placeholder:"请输入验证码","placeholder-class":"placeholder"},null,512),[[e.vModelText,r.value]])]),e.createElementVNode("button",{class:"send-code-btn",onClick:u,disabled:i.value},e.toDisplayString(i.value?`${c.value}s后重试`:"发送验证码"),9,["disabled"])]),e.createElementVNode("button",{class:"login-btn",onClick:d},"登录")])])]))}},u="/static/user/avatar.jpg",d={__name:"index",setup(o){const r=e.ref([]),i=e.ref(1),c=e.ref(10),l=e.ref(0),d=e.ref(0),h=e.ref(!1),p=e.ref(!1),g=e=>{var t,n;const s=320+.8*(((null==(t=e.title)?void 0:t.length)||0)+((null==(n=e.content)?void 0:n.length)||0)),o=100*Math.random()-50;return Math.max(240,Math.min(500,s+o))+"rpx"},f=e.computed((()=>r.value.filter(((e,t)=>t%2==0)))),m=e.computed((()=>r.value.filter(((e,t)=>t%2==1)))),y=async(e=1,n=!1)=>{if(!h.value){h.value=!0;try{const s=uni.getStorageSync("authorization"),o=await uni.request({url:a+"recommend",method:"GET",header:{Cookie:`Authorization=${s}`},data:{pageNum:e,pageSize:c.value},withCredentials:!0});if(t("log","at pages/tabbar/index/index.vue:162","推荐列表响应:",o),o.data&&o.data.data){const e=o.data.data;l.value=e.total,d.value=e.pages;const t=(e.records||[]).map((e=>({...e,content:e.content||"这是一段随机生成的内容，用来展示瀑布流布局效果。内容长度不同，会影响卡片高度。"})));r.value=n?t:[...r.value,...t],p.value=i.value>=d.value}}catch(s){t("error","at pages/tabbar/index/index.vue:191","获取推荐列表失败:",s)}finally{h.value=!1}}},v=()=>{p.value||h.value||(i.value++,y(i.value))},_=e=>{uni.navigateTo({url:`/pages/recommend/detail?id=${e.id}`})};return e.onMounted((async()=>{await y(1,!0)})),n((()=>{const e=getApp();t("log","at pages/tabbar/index/index.vue:224","当前页面authorization值:",uni.getStorageSync("authorization")),e.checkLogin()&&(t("log","at pages/tabbar/index/index.vue:229","登录成功"),y(1,!0))})),(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("image",{class:"logo",src:s}),e.createElementVNode("text",{class:"title"},"推荐")]),e.createElementVNode("view",{class:"search-box"},[e.createElementVNode("text",{class:"search-placeholder"},"搜索")])]),e.createElementVNode("scroll-view",{class:"waterfall","scroll-y":"",onScrolltolower:v},[e.createElementVNode("view",{class:"waterfall-wrapper"},[e.createElementVNode("view",{class:"waterfall-column left-column"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(f.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"waterfall-item",key:n,onClick:e=>_(t),style:e.normalizeStyle({marginBottom:10+Math.floor(15*Math.random())+"rpx"})},[e.createElementVNode("view",{class:"media-wrapper",style:e.normalizeStyle({height:g(t)})},[e.createElementVNode("image",{class:"item-image",src:t.img,mode:"aspectFill"},null,8,["src"])],4),e.createElementVNode("view",{class:"item-content"},[e.createElementVNode("text",{class:"item-title",style:e.normalizeStyle({"-webkit-line-clamp":n%2==0?2:1})},e.toDisplayString(t.title),5),e.createElementVNode("text",{class:"item-desc",style:e.normalizeStyle({"-webkit-line-clamp":n%5==0?4:n%3==0?3:2})},e.toDisplayString(t.content),5),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("image",{class:"user-avatar",src:u}),e.createElementVNode("text",{class:"user-name"},"用户"+e.toDisplayString(t.userid),1)]),e.createElementVNode("view",{class:"like-info"},[e.createElementVNode("image",{class:"icon-rabbit",src:s})])])])],12,["onClick"])))),128))]),e.createElementVNode("view",{class:"waterfall-column right-column"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(m.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"waterfall-item",key:n,onClick:e=>_(t),style:e.normalizeStyle({marginBottom:10+Math.floor(15*Math.random())+"rpx"})},[e.createElementVNode("view",{class:"media-wrapper",style:e.normalizeStyle({height:g(t)})},[e.createElementVNode("image",{class:"item-image",src:t.img,mode:"aspectFill"},null,8,["src"]),n%7==0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"floating-title"},[e.createElementVNode("text",null,e.toDisplayString(t.title),1)])):e.createCommentVNode("",!0)],4),e.createElementVNode("view",{class:"item-content"},[e.createElementVNode("text",{class:"item-title",style:e.normalizeStyle({"-webkit-line-clamp":n%2==0?1:2})},e.toDisplayString(t.title),5),e.createElementVNode("text",{class:"item-desc",style:e.normalizeStyle({"-webkit-line-clamp":n%4==0?3:2})},e.toDisplayString(t.content),5),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("image",{class:"user-avatar",src:u}),e.createElementVNode("text",{class:"user-name"},"用户"+e.toDisplayString(t.userid),1)]),e.createElementVNode("view",{class:"like-info"},[e.createElementVNode("image",{class:"icon-rabbit",src:s})])])])],12,["onClick"])))),128))])]),h.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading"},[e.createElementVNode("text",{class:"loading-text"},"加载中...")])):e.createCommentVNode("",!0),p.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-more"},[e.createElementVNode("text",{class:"no-more-text"},"没有更多了~")])):e.createCommentVNode("",!0)],32)]))}},h={__name:"home",setup(n){const s=e.ref({}),o=e.ref({nickname:"",gender:"",birthday:""}),r=["男","女","其他"],i=async()=>{try{const e=await uni.request({url:a+"user",method:"GET",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`},withCredentials:!0});e.data&&e.data.data&&(s.value=e.data.data,o.value={id:s.value.id,nickname:s.value.nickname,gender:s.value.gender,birthday:s.value.birthday})}catch(e){t("error","at pages/tabbar/home/<USER>","获取用户信息失败:",e)}},c=async()=>{var e;try{const n=await uni.request({url:a+"user",method:"PUT",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`,"Content-Type":"application/json"},data:JSON.stringify(o.value),withCredentials:!0});if(n.data&&0===n.data.code)uni.showToast({title:"保存成功",icon:"success"}),i();else{const s=(null==(e=n.data)?void 0:e.message)||"保存失败";t("error","at pages/tabbar/home/<USER>","保存设置失败:",s),uni.showToast({title:s,icon:"none"})}}catch(n){t("error","at pages/tabbar/home/<USER>","保存设置失败:",n),uni.showToast({title:n.message||"保存失败",icon:"none",duration:2e3})}},l=e=>new Promise(((n,s)=>{uni.getFileSystemManager().readFile({filePath:e,encoding:"base64",success:e=>{const o={avatar:`data:image/jpeg;base64,${e.data}`,type:"avatar"};uni.request({url:a+"user",method:"PUT",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`,"Content-Type":"application/json"},data:o,success:e=>{try{const t=e.data;if(t&&0===t.code)uni.showToast({title:"头像更新成功",icon:"success"}),i(),n(t);else{const e=(null==t?void 0:t.message)||"头像更新失败";uni.showToast({title:e,icon:"none",duration:2e3}),s(new Error(e))}}catch(o){t("error","at pages/tabbar/home/<USER>","处理响应数据失败:",o),uni.showToast({title:"处理响应数据失败",icon:"none",duration:2e3}),s(o)}},fail:e=>{t("error","at pages/tabbar/home/<USER>","上传头像失败:",e),uni.showToast({title:"上传头像失败",icon:"none",duration:2e3}),s(e)}})},fail:e=>{t("error","at pages/tabbar/home/<USER>","读取图片失败:",e),uni.showToast({title:"读取图片失败",icon:"none",duration:2e3}),s(e)}})})),u=async()=>{try{const e=await uni.request({url:a+"user",method:"DELETE",header:{Cookie:`Authorization=${uni.getStorageSync("authorization")}`},withCredentials:!0});e.data&&0===e.data.code?(uni.removeStorageSync("authorization"),uni.reLaunch({url:"/pages/login/login"})):uni.showToast({title:e.data.message||"退出失败",icon:"none"})}catch(e){t("error","at pages/tabbar/home/<USER>","退出登录失败:",e),uni.showToast({title:"退出失败",icon:"none"})}},d=()=>{uni.showActionSheet({itemList:["从相册选择","拍照"],success:e=>{0===e.tapIndex?h():1===e.tapIndex&&p()}})},h=async()=>{try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album"]});e.tempFilePaths.length>0&&await l(e.tempFilePaths[0])}catch(e){t("error","at pages/tabbar/home/<USER>","选择头像失败:",e)}},p=async()=>{try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["camera"]});e.tempFilePaths.length>0&&await l(e.tempFilePaths[0])}catch(e){t("error","at pages/tabbar/home/<USER>","拍照失败:",e)}},g=e=>{o.value.gender=r[e.detail.value]},f=e=>{o.value.birthday=e.detail.value};return e.onMounted((()=>{i()})),(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"user-card"},[e.createElementVNode("view",{class:"avatar-container"},[e.createElementVNode("image",{class:"avatar",src:s.value.avatar||"/static/user/avatar.jpg"},null,8,["src"]),e.createElementVNode("button",{class:"edit-avatar-btn",onClick:d},"更换头像")]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"nickname"},e.toDisplayString(s.value.nickname||"未设置昵称"),1),e.createElementVNode("text",{class:"username"},"ID: "+e.toDisplayString(s.value.id),1)])]),e.createElementVNode("view",{class:"settings-form"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"昵称"),e.withDirectives(e.createElementVNode("input",{class:"input","onUpdate:modelValue":n[0]||(n[0]=e=>o.value.nickname=e),placeholder:"请输入昵称"},null,512),[[e.vModelText,o.value.nickname]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"性别"),e.createElementVNode("picker",{class:"picker",mode:"selector",range:r,onChange:g},[e.createElementVNode("text",null,e.toDisplayString(o.value.gender||"请选择性别"),1)],32)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"label"},"生日"),e.createElementVNode("picker",{class:"picker",mode:"date",onChange:f},[e.createElementVNode("text",null,e.toDisplayString(o.value.birthday||"请选择生日"),1)],32)]),e.createElementVNode("button",{class:"save-btn",onClick:c},"保存设置"),e.createElementVNode("button",{class:"logout-btn",onClick:u},"退出登录")])]))}};const p=((e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n})({data:()=>({}),methods:{}},[["render",function(t,n,s,o,a,r){return e.openBlock(),e.createElementBlock("view")}]]),g={__name:"daily",setup(s){const o=getApp(),r=uni.getStorageSync("authorization"),i=e.ref([]),c=e.ref(!1),l=e.reactive({id:null,userId:null,title:"",content:"",image:""}),u=async()=>{try{const n=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],maxSize:5242880});if(!n||!n.tempFilePaths||!n.tempFilePaths.length)throw new Error("选择图片失败");const s=n.tempFilePaths[0],o=await uni.uploadFile({url:a+"daily/upload",filePath:s,name:"file",header:{Cookie:`Authorization=${r}`,"Content-Type":"multipart/form-data"},timeout:6e4,formData:{type:"image"}});if(!o||!o.statusCode||200!==o.statusCode)throw new Error(`图片上传失败: ${(null==o?void 0:o.errMsg)||"未知错误"}`);try{const e=JSON.parse(o.data);if(0!==e.code||!e.data)throw new Error(e.message||"图片上传失败");l.image=e.data}catch(e){throw t("error","at pages/tabbar/daily/daily.vue:105","解析上传响应失败:",e),new Error("服务器响应格式错误")}}catch(n){t("error","at pages/tabbar/daily/daily.vue:109","图片处理失败:",n),uni.showToast({title:n.message||"图片处理失败",icon:"none",duration:2e3})}},d=e=>{uni.previewImage({urls:[e]})},h=async()=>{try{const e=await uni.request({url:a+"daily",method:"GET",header:{Cookie:`Authorization=${r}`},data:{userId:o.globalData.userId}});0===e.data.code?i.value=e.data.data:uni.showToast({title:e.data.message||"获取日记失败",icon:"none"})}catch(e){t("error","at pages/tabbar/daily/daily.vue:142","获取日记列表失败:",e),uni.showToast({title:"获取日记列表失败",icon:"none"})}},p=()=>{g(),c.value=!1},g=()=>{l.id=null,l.title="",l.content="",l.image=""};return n((()=>{o.checkLogin()&&h()})),(n,s)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[i.value&&i.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"daily-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.value,((n,s)=>(e.openBlock(),e.createElementBlock("view",{class:"daily-item",key:s},[e.createElementVNode("view",{class:"daily-header"},[e.createElementVNode("text",{class:"daily-title"},e.toDisplayString(n.title),1),e.createElementVNode("view",{class:"daily-actions"},[e.createElementVNode("button",{class:"action-btn edit",onClick:e=>{return t=n,Object.assign(l,t),void(c.value=!0);var t}},"编辑",8,["onClick"]),e.createElementVNode("button",{class:"action-btn delete",onClick:e=>(async e=>{try{const[t,n]=await uni.showModal({title:"确认删除",content:"确定要删除这条日记吗？"});if(n.confirm){const t=await uni.request({url:a+"daily",method:"DELETE",header:{Cookie:`Authorization=${r}`},data:{id:e,userId:o.globalData.userId}});0===t.data.code?(uni.showToast({title:"删除成功",icon:"success"}),h()):uni.showToast({title:t.data.message||"删除失败",icon:"none"})}}catch(n){t("error","at pages/tabbar/daily/daily.vue:259","删除日记失败:",n),uni.showToast({title:"删除失败",icon:"none"})}})(n.id)},"删除",8,["onClick"])])]),e.createElementVNode("view",{class:"daily-content"},e.toDisplayString(n.content),1),n.image?(e.openBlock(),e.createElementBlock("image",{key:0,src:n.image,class:"daily-image",mode:"aspectFill",onClick:e=>d(n.image)},null,8,["src","onClick"])):e.createCommentVNode("",!0)])))),128))])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"form-header"},[e.createElementVNode("text",{class:"form-title"},e.toDisplayString(c.value?"编辑日记":"新建日记"),1)]),e.withDirectives(e.createElementVNode("input",{class:"input-field","onUpdate:modelValue":s[0]||(s[0]=e=>l.title=e),placeholder:"请输入标题"},null,512),[[e.vModelText,l.title]]),e.withDirectives(e.createElementVNode("textarea",{class:"textarea-field","onUpdate:modelValue":s[1]||(s[1]=e=>l.content=e),placeholder:"写下你的想法..."},null,512),[[e.vModelText,l.content]]),e.createElementVNode("view",{class:"image-upload"},[l.image?(e.openBlock(),e.createElementBlock("image",{key:0,src:l.image,class:"preview-image",mode:"aspectFill",onClick:s[2]||(s[2]=e=>d(l.image))},null,8,["src"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"upload-placeholder",onClick:u},[e.createElementVNode("text",{class:"iconfont icon-camera"}),e.createElementVNode("text",null,"点击添加图片")]))]),e.createElementVNode("view",{class:"form-actions"},[e.createElementVNode("button",{class:"submit-btn",onClick:s[3]||(s[3]=e=>c.value?(async()=>{if(l.title&&l.content)try{const e=await uni.request({url:a+"daily",method:"PUT",header:{Cookie:`Authorization=${r}`},data:l});0===e.data.code?(uni.showToast({title:"更新成功",icon:"success"}),h(),g(),c.value=!1):uni.showToast({title:e.data.message||"更新失败",icon:"none"})}catch(e){t("error","at pages/tabbar/daily/daily.vue:222","更新日记失败:",e),uni.showToast({title:"更新失败",icon:"none"})}else uni.showToast({title:"请填写标题和内容",icon:"none"})})():(async()=>{if(l.title&&l.content)try{l.userId=o.globalData.userId;const e=await uni.request({url:a+"daily",method:"POST",header:{Cookie:`Authorization=${r}`},data:l});0===e.data.code?(uni.showToast({title:"发布成功",icon:"success"}),h(),g()):uni.showToast({title:e.data.message||"发布失败",icon:"none"})}catch(e){t("error","at pages/tabbar/daily/daily.vue:182","添加日记失败:",e),uni.showToast({title:"发布失败",icon:"none"})}else uni.showToast({title:"请填写标题和内容",icon:"none"})})())},e.toDisplayString(c.value?"更新":"发布"),1),c.value?(e.openBlock(),e.createElementBlock("button",{key:0,class:"cancel-btn",onClick:p},"取消")):e.createCommentVNode("",!0)])])]))}};__definePage("pages/splash/splash",c),__definePage("pages/login/login",l),__definePage("pages/tabbar/index/index",d),__definePage("pages/tabbar/home/<USER>",h),__definePage("pages/tabbar/message/message",p),__definePage("pages/tabbar/daily/daily",g);const f=async()=>{t("log","at utils/app-validator.js:119","=== 应用健康检查开始 ===");const e=(()=>{const e=[];return a.startsWith("http")?a.endsWith("/")||e.push("BASE_URL 应以 / 结尾"):e.push("BASE_URL 格式不正确，应以 http:// 或 https:// 开头"),{isValid:0===e.length,issues:e,config:{BASE_URL:a,API_TIMEOUT:r,ENV:i}}})();t("log","at utils/app-validator.js:123","配置验证:",e);const n=await(async()=>{try{return{success:!0,message:"网络连接正常",response:await uni.request({url:a+"health",method:"GET",timeout:5e3})}}catch(e){return{success:!1,message:"网络连接失败",error:e}}})();t("log","at utils/app-validator.js:127","网络连接测试:",n);const s=await(async()=>{const e=uni.getStorageSync("authorization");if(!e)return{valid:!1,message:"未找到授权token"};try{const t=await uni.request({url:a+"user",method:"GET",header:{Authorization:e,"Content-Type":"application/json"},timeout:1e4});return t.data&&0===t.data.code?{valid:!0,message:"Token有效",userInfo:t.data.data}:{valid:!1,message:"Token无效",response:t.data}}catch(t){return{valid:!1,message:"Token验证失败",error:t}}})();t("log","at utils/app-validator.js:131","Token验证:",s);const o={config:e.isValid,network:n.success,token:s.valid,timestamp:(new Date).toISOString()};return t("log","at utils/app-validator.js:140","=== 应用健康检查完成 ==="),t("log","at utils/app-validator.js:141","整体状态:",o),{overall:o,details:{config:e,network:n,token:s}}},m={globalData:{isLogin:!1,userInfo:null},onLaunch:function(){t("log","at App.vue:11","App Launch"),this.performHealthCheck()},onShow:function(){t("log","at App.vue:17","App Show")},onHide:function(){t("log","at App.vue:20","App Hide")},methods:{async performHealthCheck(){try{(await f()).overall.config||(t("error","at App.vue:28","配置验证失败，请检查config/index.js"),uni.showToast({title:"应用配置错误",icon:"none",duration:3e3}))}catch(e){t("error","at App.vue:36","健康检查失败:",e)}},async checkAutoLogin(){const e=uni.getStorageSync("authorization");if(e){t("log","at App.vue:45","检测到本地cookie，验证有效性...");try{const n=await uni.request({url:a+"user",method:"GET",header:{Cookie:`Authorization=${e}`,"Content-Type":"application/json"},withCredentials:!0});t("log","at App.vue:58","Cookie验证结果:",n),200===n.statusCode&&n.data&&0===n.data.code?(t("log","at App.vue:63","Cookie有效，自动登录成功，用户信息:",n.data.data),this.globalData.isLogin=!0,this.globalData.userInfo=n.data.data,this.navigateToIndex()):404===n.statusCode?(t("log","at App.vue:71","Cookie过期(404)，跳转到登录页"),this.handleExpiredAuth()):(t("log","at App.vue:75","Cookie验证失败，跳转到登录页"),this.handleExpiredAuth())}catch(n){t("log","at App.vue:79","Cookie验证请求失败:",n),this.handleExpiredAuth()}}else t("log","at App.vue:85","未找到cookie，跳转到登录页"),this.navigateToLogin()},navigateToIndex(){t("log","at App.vue:92","跳转到首页"),uni.reLaunch({url:"/pages/tabbar/index/index",success:()=>{t("log","at App.vue:96","成功跳转到首页")},fail:e=>{t("error","at App.vue:99","跳转到首页失败:",e)}})},navigateToLogin(){t("log","at App.vue:106","跳转到登录页"),uni.reLaunch({url:"/pages/login/login",success:()=>{t("log","at App.vue:110","成功跳转到登录页")},fail:e=>{t("error","at App.vue:113","跳转到登录页失败:",e)}})},handleExpiredAuth(){this.clearLoginData(),this.navigateToLogin()},clearLoginData(){uni.removeStorageSync("authorization"),this.globalData.isLogin=!1},checkLogin:()=>!!uni.getStorageSync("authorization")||(uni.showToast({title:"请先登录",icon:"none"}),uni.navigateTo({url:"/pages/login/login"}),!1),logout(){this.clearLoginData(),uni.reLaunch({url:"/pages/login/login"})}}},y={baseURL:a,timeout:r,header:{"Content-Type":"application/json"}},v=(e={})=>(e=(e=>{e.url=(e.baseURL||y.baseURL)+(e.url||""),e.header={...y.header,...e.header||{}},e.timeout=e.timeout||y.timeout;const n=uni.getStorageSync("authorization");return n&&(e.header.Authorization=n),t("log","at api/request.js:32","请求拦截器 ==>",{url:e.url,method:e.method,data:e.data,header:e.header}),e})(e),new Promise(((n,s)=>{uni.request({...e,success:o=>{((e,n)=>{var s;return t("log","at api/request.js:50","响应拦截器 ==>",{url:n.url,data:e,statusCode:e.statusCode}),e.statusCode>=200&&e.statusCode<300?Promise.resolve(e.data):401===e.statusCode?(uni.removeStorageSync("authorization"),uni.showToast({title:"登录已过期，请重新登录",icon:"none"}),uni.reLaunch({url:"/pages/login/login"}),Promise.reject(e)):(uni.showToast({title:(null==(s=e.data)?void 0:s.message)||`请求失败(${e.statusCode})`,icon:"none"}),Promise.reject(e))})(o,e).then(n).catch(s)},fail:n=>{((e,n)=>(t("error","at api/request.js:88","请求错误 ==>",{url:n.url,error:e}),uni.showToast({title:"网络异常，请稍后重试",icon:"none"}),Promise.reject(e)))(n,e).catch(s)}})}))),_={get:(e,t={},n={})=>v({url:e,data:t,method:"GET",...n}),post:(e,t={},n={})=>v({url:e,data:t,method:"POST",...n}),put:(e,t={},n={})=>v({url:e,data:t,method:"PUT",...n}),delete:(e,t={},n={})=>v({url:e,data:t,method:"DELETE",...n}),upload:(e,t,n={},s={})=>{const o={url:(s.baseURL||y.baseURL)+e,filePath:t,name:s.name||"file",formData:n,header:s.header||{}},a=uni.getStorageSync("authorization");return a&&(o.header.Authorization=a),new Promise(((e,t)=>{uni.uploadFile({...o,success:n=>{if(n.statusCode>=200&&n.statusCode<300)try{const t=JSON.parse(n.data);e(t)}catch(Ue){e(n.data)}else t(n)},fail:t})}))}},w={config:y,request:v,http:_},k={pages:[{path:"pages/splash/splash",style:{navigationBarTitleText:"启动页",navigationStyle:"custom"}},{path:"pages/login/login",style:{navigationBarTitleText:"账号登陆"}},{path:"pages/tabbar/index/index",style:{navigationBarTitleText:"桂圆可爱"}},{path:"pages/tabbar/home/<USER>",style:{navigationBarTitleText:"个人主页"}},{path:"pages/tabbar/message/message",style:{navigationBarTitleText:"我的消息"}},{path:"pages/tabbar/daily/daily",style:{navigationBarTitleText:"发布日常"}}],globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},tabBar:{color:"#F0AD4E",selectedColor:"#3cc51f",borderStyle:"black",backgroundColor:"#F8F8F8",list:[{pagePath:"pages/tabbar/index/index",iconPath:"/static/index/index.png",selectedIconPath:"/static/index/indexselect.png",text:"首页"},{pagePath:"pages/tabbar/daily/daily",iconPath:"/static/daily/daily.png",selectedIconPath:"/static/daily/dailyselect.png",text:"日常"},{pagePath:"pages/tabbar/message/message",iconPath:"/static/message/message.png",selectedIconPath:"/static/message/messageselect.png",text:"消息"},{pagePath:"pages/tabbar/home/<USER>",iconPath:"/static/home/<USER>",selectedIconPath:"/static/home/<USER>",text:"主页"}]}};function S(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var T=S((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),s={},o=s.lib={},a=o.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},r=o.WordArray=a.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,n=e.words,s=this.sigBytes,o=e.sigBytes;if(this.clamp(),s%4)for(var a=0;a<o;a++){var r=n[a>>>2]>>>24-a%4*8&255;t[s+a>>>2]|=r<<24-(s+a)%4*8}else for(a=0;a<o;a+=4)t[s+a>>>2]=n[a>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=a.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,s=[],o=function(t){var n=987654321,s=4294967295;return function(){var o=((n=36969*(65535&n)+(n>>16)&s)<<16)+(t=18e3*(65535&t)+(t>>16)&s)&s;return o/=4294967296,(o+=.5)*(e.random()>.5?1:-1)}},a=0;a<t;a+=4){var i=o(4294967296*(n||e.random()));n=987654071*i(),s.push(4294967296*i()|0)}return new r.init(s,t)}}),i=s.enc={},c=i.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],o=0;o<n;o++){var a=t[o>>>2]>>>24-o%4*8&255;s.push((a>>>4).toString(16)),s.push((15&a).toString(16))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s+=2)n[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new r.init(n,t/2)}},l=i.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],o=0;o<n;o++){var a=t[o>>>2]>>>24-o%4*8&255;s.push(String.fromCharCode(a))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s++)n[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new r.init(n,t)}},u=i.Utf8={stringify:function(e){try{return decodeURIComponent(escape(l.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return l.parse(unescape(encodeURIComponent(e)))}},d=o.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new r.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=u.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,s=n.words,o=n.sigBytes,a=this.blockSize,i=o/(4*a),c=(i=t?e.ceil(i):e.max((0|i)-this._minBufferSize,0))*a,l=e.min(4*c,o);if(c){for(var u=0;u<c;u+=a)this._doProcessBlock(s,u);var d=s.splice(0,c);n.sigBytes-=l}return new r.init(d,l)},clone:function(){var e=a.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=d.extend({cfg:a.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}});var h=s.algo={};return s}(Math),n)})),b=T,A=(S((function(e,t){var n;e.exports=(n=b,function(e){var t=n,s=t.lib,o=s.WordArray,a=s.Hasher,r=t.algo,i=[];!function(){for(var t=0;t<64;t++)i[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=r.MD5=a.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var s=t+n,o=e[s];e[s]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var a=this._hash.words,r=e[t+0],c=e[t+1],p=e[t+2],g=e[t+3],f=e[t+4],m=e[t+5],y=e[t+6],v=e[t+7],_=e[t+8],w=e[t+9],k=e[t+10],S=e[t+11],T=e[t+12],b=e[t+13],A=e[t+14],I=e[t+15],P=a[0],E=a[1],x=a[2],C=a[3];P=l(P,E,x,C,r,7,i[0]),C=l(C,P,E,x,c,12,i[1]),x=l(x,C,P,E,p,17,i[2]),E=l(E,x,C,P,g,22,i[3]),P=l(P,E,x,C,f,7,i[4]),C=l(C,P,E,x,m,12,i[5]),x=l(x,C,P,E,y,17,i[6]),E=l(E,x,C,P,v,22,i[7]),P=l(P,E,x,C,_,7,i[8]),C=l(C,P,E,x,w,12,i[9]),x=l(x,C,P,E,k,17,i[10]),E=l(E,x,C,P,S,22,i[11]),P=l(P,E,x,C,T,7,i[12]),C=l(C,P,E,x,b,12,i[13]),x=l(x,C,P,E,A,17,i[14]),P=u(P,E=l(E,x,C,P,I,22,i[15]),x,C,c,5,i[16]),C=u(C,P,E,x,y,9,i[17]),x=u(x,C,P,E,S,14,i[18]),E=u(E,x,C,P,r,20,i[19]),P=u(P,E,x,C,m,5,i[20]),C=u(C,P,E,x,k,9,i[21]),x=u(x,C,P,E,I,14,i[22]),E=u(E,x,C,P,f,20,i[23]),P=u(P,E,x,C,w,5,i[24]),C=u(C,P,E,x,A,9,i[25]),x=u(x,C,P,E,g,14,i[26]),E=u(E,x,C,P,_,20,i[27]),P=u(P,E,x,C,b,5,i[28]),C=u(C,P,E,x,p,9,i[29]),x=u(x,C,P,E,v,14,i[30]),P=d(P,E=u(E,x,C,P,T,20,i[31]),x,C,m,4,i[32]),C=d(C,P,E,x,_,11,i[33]),x=d(x,C,P,E,S,16,i[34]),E=d(E,x,C,P,A,23,i[35]),P=d(P,E,x,C,c,4,i[36]),C=d(C,P,E,x,f,11,i[37]),x=d(x,C,P,E,v,16,i[38]),E=d(E,x,C,P,k,23,i[39]),P=d(P,E,x,C,b,4,i[40]),C=d(C,P,E,x,r,11,i[41]),x=d(x,C,P,E,g,16,i[42]),E=d(E,x,C,P,y,23,i[43]),P=d(P,E,x,C,w,4,i[44]),C=d(C,P,E,x,T,11,i[45]),x=d(x,C,P,E,I,16,i[46]),P=h(P,E=d(E,x,C,P,p,23,i[47]),x,C,r,6,i[48]),C=h(C,P,E,x,v,10,i[49]),x=h(x,C,P,E,A,15,i[50]),E=h(E,x,C,P,m,21,i[51]),P=h(P,E,x,C,T,6,i[52]),C=h(C,P,E,x,g,10,i[53]),x=h(x,C,P,E,k,15,i[54]),E=h(E,x,C,P,c,21,i[55]),P=h(P,E,x,C,_,6,i[56]),C=h(C,P,E,x,I,10,i[57]),x=h(x,C,P,E,y,15,i[58]),E=h(E,x,C,P,b,21,i[59]),P=h(P,E,x,C,f,6,i[60]),C=h(C,P,E,x,S,10,i[61]),x=h(x,C,P,E,p,15,i[62]),E=h(E,x,C,P,w,21,i[63]),a[0]=a[0]+P|0,a[1]=a[1]+E|0,a[2]=a[2]+x|0,a[3]=a[3]+C|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,o=8*t.sigBytes;n[o>>>5]|=128<<24-o%32;var a=e.floor(s/4294967296),r=s;n[15+(o+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),n[14+(o+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(n.length+1),this._process();for(var i=this._hash,c=i.words,l=0;l<4;l++){var u=c[l];c[l]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return i},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,n,s,o,a,r){var i=e+(t&n|~t&s)+o+r;return(i<<a|i>>>32-a)+t}function u(e,t,n,s,o,a,r){var i=e+(t&s|n&~s)+o+r;return(i<<a|i>>>32-a)+t}function d(e,t,n,s,o,a,r){var i=e+(t^n^s)+o+r;return(i<<a|i>>>32-a)+t}function h(e,t,n,s,o,a,r){var i=e+(n^(t|~s))+o+r;return(i<<a|i>>>32-a)+t}t.MD5=a._createHelper(c),t.HmacMD5=a._createHmacHelper(c)}(Math),n.MD5)})),S((function(e,t){var n,s,o;e.exports=(s=(n=b).lib.Base,o=n.enc.Utf8,void(n.algo.HMAC=s.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=o.parse(t));var n=e.blockSize,s=4*n;t.sigBytes>s&&(t=e.finalize(t)),t.clamp();for(var a=this._oKey=t.clone(),r=this._iKey=t.clone(),i=a.words,c=r.words,l=0;l<n;l++)i[l]^=1549556828,c[l]^=909522486;a.sigBytes=r.sigBytes=s,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})))})),S((function(e,t){e.exports=b.HmacMD5}))),I=S((function(e,t){e.exports=b.enc.Utf8})),P=S((function(e,t){var n,s,o;e.exports=(o=(s=n=b).lib.WordArray,s.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,s=this._map;e.clamp();for(var o=[],a=0;a<n;a+=3)for(var r=(t[a>>>2]>>>24-a%4*8&255)<<16|(t[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|t[a+2>>>2]>>>24-(a+2)%4*8&255,i=0;i<4&&a+.75*i<n;i++)o.push(s.charAt(r>>>6*(3-i)&63));var c=s.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,n=this._map,s=this._reverseMap;if(!s){s=this._reverseMap=[];for(var a=0;a<n.length;a++)s[n.charCodeAt(a)]=a}var r=n.charAt(64);if(r){var i=e.indexOf(r);-1!==i&&(t=i)}return function(e,t,n){for(var s=[],a=0,r=0;r<t;r++)if(r%4){var i=n[e.charCodeAt(r-1)]<<r%4*2,c=n[e.charCodeAt(r)]>>>6-r%4*2;s[a>>>2]|=(i|c)<<24-a%4*8,a++}return o.create(s,a)}(e,t,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},n.enc.Base64)}));const E="uni_id_token",x="uni_id_token_expired",C="FUNCTION",N="OBJECT",O="CLIENT_DB",L="pending",U="rejected";function D(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function R(e){return"object"===D(e)}function M(e){return"function"==typeof e}function q(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}const F="REJECTED",j="NOT_PENDING";class V{constructor({createPromise:e,retryRule:t=F}={}){this.createPromise=e,this.status=null,this.promise=null,this.retryRule=t}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case F:return this.status===U;case j:return this.status!==L}}exec(){return this.needRetry?(this.status=L,this.promise=this.createPromise().then((e=>(this.status="fulfilled",Promise.resolve(e))),(e=>(this.status=U,Promise.reject(e)))),this.promise):this.promise}}function B(e){return e&&"string"==typeof e?JSON.parse(e):e}const $=B([]);B("");const K=B('[{"provider":"aliyun","spaceName":"suakitsu","spaceId":"mp-2e6e8eae-0872-49fd-b869-9ea34baed9f1","clientSecret":"nJUVHv9y7feXJzGSaPlaVg==","endpoint":"https://api.next.bspapp.com"}]')||[];let z="";try{z="__UNI__1F0BDA5"}catch(Ue){}let H,J={};function W(e,t={}){var n,s;return n=J,s=e,Object.prototype.hasOwnProperty.call(n,s)||(J[e]=t),J[e]}J=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={};const G=["invoke","success","fail","complete"],Q=W("_globalUniCloudInterceptor");function Y(e,t){Q[e]||(Q[e]={}),R(t)&&Object.keys(t).forEach((n=>{G.indexOf(n)>-1&&function(e,t,n){let s=Q[e][t];s||(s=Q[e][t]=[]),-1===s.indexOf(n)&&M(n)&&s.push(n)}(e,n,t[n])}))}function X(e,t){Q[e]||(Q[e]={}),R(t)?Object.keys(t).forEach((n=>{G.indexOf(n)>-1&&function(e,t,n){const s=Q[e][t];if(!s)return;const o=s.indexOf(n);o>-1&&s.splice(o,1)}(e,n,t[n])})):delete Q[e]}function Z(e,t){return e&&0!==e.length?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function ee(e,t){return Q[e]&&Q[e][t]||[]}function te(e){Y("callObject",e)}const ne=W("_globalUniCloudListener"),se="response",oe="needLogin",ae="refreshToken",re="clientdb",ie="cloudfunction",ce="cloudobject";function le(e){return ne[e]||(ne[e]=[]),ne[e]}function ue(e,t){const n=le(e);n.includes(t)||n.push(t)}function de(e,t){const n=le(e),s=n.indexOf(t);-1!==s&&n.splice(s,1)}function he(e,t){const n=le(e);for(let s=0;s<n.length;s++)(0,n[s])(t)}let pe,ge=!1;function fe(){return pe||(pe=new Promise((e=>{ge&&e(),function t(){if("function"==typeof getCurrentPages){const t=getCurrentPages();t&&t[0]&&(ge=!0,e())}ge||setTimeout((()=>{t()}),30)}()})),pe)}function me(e){const t={};for(const n in e){const s=e[n];M(s)&&(t[n]=q(s))}return t}class ye extends Error{constructor(e){super(e.message),this.errMsg=e.message||e.errMsg||"unknown system error",this.code=this.errCode=e.code||e.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=e.subject||e.errSubject,this.cause=e.cause,this.requestId=e.requestId}toJson(e=0){if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}var ve={request:e=>uni.request(e),uploadFile:e=>uni.uploadFile(e),setStorageSync:(e,t)=>uni.setStorageSync(e,t),getStorageSync:e=>uni.getStorageSync(e),removeStorageSync:e=>uni.removeStorageSync(e),clearStorageSync:()=>uni.clearStorageSync(),connectSocket:e=>uni.connectSocket(e)};function _e(e){return e&&_e(e.__v_raw)||e}function we(){return{token:ve.getStorageSync(E)||ve.getStorageSync("uniIdToken"),tokenExpired:ve.getStorageSync(x)}}function ke({token:e,tokenExpired:t}={}){e&&ve.setStorageSync(E,e),t&&ve.setStorageSync(x,t)}let Se,Te;function be(){return Se||(Se=uni.getSystemInfoSync()),Se}function Ae(){let e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;const{scene:n,channel:s}=uni.getLaunchOptionsSync();e=s,t=n}}catch(n){}return{channel:e,scene:t}}let Ie={};function Pe(){const e=uni.getLocale&&uni.getLocale()||"en";if(Te)return{...Ie,...Te,locale:e,LOCALE:e};const t=be(),{deviceId:n,osName:s,uniPlatform:o,appId:a}=t,r=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(const i in t)Object.hasOwnProperty.call(t,i)&&-1===r.indexOf(i)&&delete t[i];return Te={PLATFORM:o,OS:s,APPID:a,DEVICEID:n,...Ae(),...t},{...Ie,...Te,locale:e,LOCALE:e}}var Ee=function(e,t){let n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),A(n,t).toString()},xe=function(e,t){return new Promise(((n,s)=>{t(Object.assign(e,{complete(e){e||(e={});const t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){const n=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",o=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return s(new ye({code:n,message:o,requestId:t}))}const o=e.data;if(o.error)return s(new ye({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,n(o)}}))}))},Ce=function(e){return P.stringify(I.parse(e))},Ne={init(e){const t=new class{constructor(e){["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),this.config=Object.assign({},{endpoint:0===e.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=ve,this._getAccessTokenPromiseHub=new V({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((e=>{if(!e.result||!e.result.accessToken)throw new ye({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(e.result.accessToken)})),retryRule:j})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return xe(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then((()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch((t=>new Promise(((e,n)=>{!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((()=>this.getAccessToken())).then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)})))):this.getAccessToken().then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)}))))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=Ee(t.data,this.config.clientSecret),t}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),s={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,s["x-basement-token"]=this.accessToken),s["x-serverless-sign"]=Ee(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:s}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request({...this.setupRequest(t),timeout:e.timeout})}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:n,filePath:s,fileType:o,onUploadProgress:a}){return new Promise(((r,i)=>{const c=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:s,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success(e){e&&e.statusCode<400?r(e):i(new ye({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){i(new ye({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof a&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{a({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:n="image",cloudPathAsRealPath:s=!1,onUploadProgress:o,config:a}){if("string"!==D(t))throw new ye({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new ye({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new ye({code:"INVALID_PARAM",message:"cloudPath不合法"});const r=a&&a.envType||this.config.envType;if(s&&("/"!==t[0]&&(t="/"+t),t.indexOf("\\")>-1))throw new ye({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const i=(await this.getOSSUploadOptionsFromPath({env:r,filename:s?t.split("/").pop():t,fileId:s?t:void 0})).result,c="https://"+i.cdnDomain+"/"+i.ossPath,{securityToken:l,accessKeyId:u,signature:d,host:h,ossPath:p,id:g,policy:f,ossCallbackUrl:m}=i,y={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:u,Signature:d,host:h,id:g,key:p,policy:f,success_action_status:200};if(l&&(y["x-oss-security-token"]=l),m){const e=JSON.stringify({callbackUrl:m,callbackBody:JSON.stringify({fileId:g,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});y.callback=Ce(e)}const v={url:"https://"+i.host,formData:y,fileName:"file",name:"file",filePath:e,fileType:n};if(await this.uploadFileToOSS(Object.assign({},v,{onUploadProgress:o})),m)return{success:!0,filePath:e,fileID:c};if((await this.reportOSSUpload({id:g})).success)return{success:!0,filePath:e,fileID:c};throw new ye({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise(((t,n)=>{Array.isArray(e)&&0!==e.length||n(new ye({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map((e=>({fileID:e,tempFileURL:e})))})}))}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||0===e.length)throw new ye({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map((e=>e.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}}(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}};const Oe="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var Le,Ue;(Ue=Le||(Le={})).local="local",Ue.none="none",Ue.session="session";var De=function(){},Re=S((function(e,t){var n;e.exports=(n=b,function(e){var t=n,s=t.lib,o=s.WordArray,a=s.Hasher,r=t.algo,i=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),s=2;s<=n;s++)if(!(t%s))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var s=2,o=0;o<64;)t(s)&&(o<8&&(i[o]=n(e.pow(s,.5))),c[o]=n(e.pow(s,1/3)),o++),s++}();var l=[],u=r.SHA256=a.extend({_doReset:function(){this._hash=new o.init(i.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,s=n[0],o=n[1],a=n[2],r=n[3],i=n[4],u=n[5],d=n[6],h=n[7],p=0;p<64;p++){if(p<16)l[p]=0|e[t+p];else{var g=l[p-15],f=(g<<25|g>>>7)^(g<<14|g>>>18)^g>>>3,m=l[p-2],y=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[p]=f+l[p-7]+y+l[p-16]}var v=s&o^s&a^o&a,_=(s<<30|s>>>2)^(s<<19|s>>>13)^(s<<10|s>>>22),w=h+((i<<26|i>>>6)^(i<<21|i>>>11)^(i<<7|i>>>25))+(i&u^~i&d)+c[p]+l[p];h=d,d=u,u=i,i=r+w|0,r=a,a=o,o=s,s=w+(_+v)|0}n[0]=n[0]+s|0,n[1]=n[1]+o|0,n[2]=n[2]+a|0,n[3]=n[3]+r|0,n[4]=n[4]+i|0,n[5]=n[5]+u|0,n[6]=n[6]+d|0,n[7]=n[7]+h|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,o=8*t.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(o+64>>>9<<4)]=e.floor(s/4294967296),n[15+(o+64>>>9<<4)]=s,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=a._createHelper(u),t.HmacSHA256=a._createHmacHelper(u)}(Math),n.SHA256)})),Me=Re,qe=S((function(e,t){e.exports=b.HmacSHA256}));const Fe=()=>{let e;if(!Promise){e=()=>{},e.promise={};const t=()=>{throw new ye({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}const t=new Promise(((t,n)=>{e=(e,s)=>e?n(e):t(s)}));return e.promise=t,e};function je(e){return void 0===e}function Ve(e){return"[object Null]"===Object.prototype.toString.call(e)}function Be(e=""){return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function $e(e=32){let t="";for(let n=0;n<e;n++)t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(Math.floor(62*Math.random()));return t}var Ke;!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Ke||(Ke={}));const ze={adapter:null,runtime:void 0},He=["anonymousUuidKey"];class Je extends De{constructor(){super(),ze.adapter.root.tcbObject||(ze.adapter.root.tcbObject={})}setItem(e,t){ze.adapter.root.tcbObject[e]=t}getItem(e){return ze.adapter.root.tcbObject[e]}removeItem(e){delete ze.adapter.root.tcbObject[e]}clear(){delete ze.adapter.root.tcbObject}}function We(e,t){switch(e){case"local":return t.localStorage||new Je;case"none":return new Je;default:return t.sessionStorage||new Je}}class Ge{constructor(e){if(!this._storage){this._persistence=ze.adapter.primaryStorage||e.persistence,this._storage=We(this._persistence,ze.adapter);const t=`access_token_${e.env}`,n=`access_token_expire_${e.env}`,s=`refresh_token_${e.env}`,o=`anonymous_uuid_${e.env}`,a=`login_type_${e.env}`,r="device_id",i=`token_type_${e.env}`,c=`user_info_${e.env}`;this.keys={accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s,anonymousUuidKey:o,loginTypeKey:a,userInfoKey:c,deviceIdKey:r,tokenTypeKey:i}}}updatePersistence(e){if(e===this._persistence)return;const t="local"===this._persistence;this._persistence=e;const n=We(e,ze.adapter);for(const s in this.keys){const e=this.keys[s];if(t&&He.includes(s))continue;const o=this._storage.getItem(e);je(o)||Ve(o)||(n.setItem(e,o),this._storage.removeItem(e))}this._storage=n}setStore(e,t,n){if(!this._storage)return;const s={version:n||"localCachev1",content:t},o=JSON.stringify(s);try{this._storage.setItem(e,o)}catch(a){throw a}}getStore(e,t){try{if(!this._storage)return}catch(s){return""}t=t||"localCachev1";const n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}removeStore(e){this._storage.removeItem(e)}}const Qe={},Ye={};function Xe(e){return Qe[e]}class Ze{constructor(e,t){this.data=t||null,this.name=e}}class et extends Ze{constructor(e,t){super("error",{error:e,data:t}),this.error=e}}const tt=new class{constructor(){this._listeners={}}on(e,t){return n=e,s=t,(o=this._listeners)[n]=o[n]||[],o[n].push(s),this;var n,s,o}off(e,t){return function(e,t,n){if(n&&n[e]){const s=n[e].indexOf(t);-1!==s&&n[e].splice(s,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof et)return console.error(e.error),this;const n="string"==typeof e?new Ze(e,t||{}):e,s=n.name;if(this._listens(s)){n.target=this;const e=this._listeners[s]?[...this._listeners[s]]:[];for(const t of e)t.call(this,n)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function nt(e,t){tt.on(e,t)}function st(e,t={}){tt.fire(e,t)}function ot(e,t){tt.off(e,t)}const at="loginStateChanged",rt="loginStateExpire",it="loginTypeChanged",ct="anonymousConverted",lt="refreshAccessToken";var ut;!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(ut||(ut={}));class dt{constructor(){this._fnPromiseMap=new Map}async run(e,t){let n=this._fnPromiseMap.get(e);return n||(n=new Promise((async(n,s)=>{try{await this._runIdlePromise();const s=t();n(await s)}catch(o){s(o)}finally{this._fnPromiseMap.delete(e)}})),this._fnPromiseMap.set(e,n)),n}_runIdlePromise(){return Promise.resolve()}}class ht{constructor(e){this._singlePromise=new dt,this._cache=Xe(e.env),this._baseURL=`https://${e.env}.ap-shanghai.tcb-api.tencentcloudapi.com`,this._reqClass=new ze.adapter.reqClass({timeout:e.timeout,timeoutMsg:`请求在${e.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]})}_getDeviceId(){if(this._deviceID)return this._deviceID;const{deviceIdKey:e}=this._cache.keys;let t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=$e(),this._cache.setStore(e,t)),this._deviceID=t,t}async _request(e,t,n={}){const s={"x-request-id":$e(),"x-device-id":this._getDeviceId()};if(n.withAccessToken){const{tokenTypeKey:e}=this._cache.keys,t=await this.getAccessToken(),n=this._cache.getStore(e);s.authorization=`${n} ${t}`}return this._reqClass["get"===n.method?"get":"post"]({url:`${this._baseURL}${e}`,data:t,headers:s})}async _fetchAccessToken(){const{loginTypeKey:e,accessTokenKey:t,accessTokenExpireKey:n,tokenTypeKey:s}=this._cache.keys,o=this._cache.getStore(e);if(o&&o!==ut.ANONYMOUS)throw new ye({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});const a=await this._singlePromise.run("fetchAccessToken",(async()=>(await this._request("/auth/v1/signin/anonymously",{},{method:"post"})).data)),{access_token:r,expires_in:i,token_type:c}=a;return this._cache.setStore(s,c),this._cache.setStore(t,r),this._cache.setStore(n,Date.now()+1e3*i),r}isAccessTokenExpired(e,t){let n=!0;return e&&t&&(n=t<Date.now()),n}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),s=this._cache.getStore(t);return this.isAccessTokenExpired(n,s)?this._fetchAccessToken():n}async refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,loginTypeKey:n}=this._cache.keys;return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.setStore(n,ut.ANONYMOUS),this.getAccessToken()}async getUserInfo(){return this._singlePromise.run("getUserInfo",(async()=>(await this._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"})).data))}}const pt=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],gt={"X-SDK-Version":"1.3.5"};function ft(e,t,n){const s=e[t];e[t]=function(t){const o={},a={};n.forEach((n=>{const{data:s,headers:r}=n.call(e,t);Object.assign(o,s),Object.assign(a,r)}));const r=t.data;return r&&(()=>{var e;if(e=r,"[object FormData]"!==Object.prototype.toString.call(e))t.data={...r,...o};else for(const t in o)r.append(t,o[t])})(),t.headers={...t.headers||{},...a},s.call(e,t)}}function mt(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...gt,"x-seqid":e}}}class yt{constructor(e={}){var t;this.config=e,this._reqClass=new ze.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=Xe(this.config.env),this._localCache=(t=this.config.env,Ye[t]),this.oauth=new ht(this.config),ft(this._reqClass,"post",[mt]),ft(this._reqClass,"upload",[mt]),ft(this._reqClass,"download",[mt])}async post(e){return await this._reqClass.post(e)}async upload(e){return await this._reqClass.upload(e)}async download(e){return await this._reqClass.download(e)}async refreshAccessToken(){let e,t;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{e=await this._refreshAccessTokenPromise}catch(n){t=n}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,t)throw t;return e}async _refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n,loginTypeKey:s,anonymousUuidKey:o}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(t);let a=this._cache.getStore(n);if(!a)throw new ye({message:"未登录CloudBase"});const r={refresh_token:a},i=await this.request("auth.fetchAccessTokenWithRefreshToken",r);if(i.data.code){const{code:e}=i.data;if("SIGN_PARAM_INVALID"===e||"REFRESH_TOKEN_EXPIRED"===e||"INVALID_REFRESH_TOKEN"===e){if(this._cache.getStore(s)===ut.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===e){const e=this._cache.getStore(o),t=this._cache.getStore(n),s=await this.send("auth.signInAnonymously",{anonymous_uuid:e,refresh_token:t});return this.setRefreshToken(s.refresh_token),this._refreshAccessToken()}st(rt),this._cache.removeStore(n)}throw new ye({code:i.data.code,message:`刷新access token失败：${i.data.code}`})}if(i.data.access_token)return st(lt),this._cache.setStore(e,i.data.access_token),this._cache.setStore(t,i.data.access_token_expire+Date.now()),{accessToken:i.data.access_token,accessTokenExpire:i.data.access_token_expire};i.data.refresh_token&&(this._cache.removeStore(n),this._cache.setStore(n,i.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n}=this._cache.keys;if(!this._cache.getStore(n))throw new ye({message:"refresh token不存在，登录状态异常"});let s=this._cache.getStore(e),o=this._cache.getStore(t),a=!0;return this._shouldRefreshAccessTokenHook&&!(await this._shouldRefreshAccessTokenHook(s,o))&&(a=!1),(!s||!o||o<Date.now())&&a?this.refreshAccessToken():{accessToken:s,accessTokenExpire:o}}async request(e,t,n){const s=`x-tcb-trace_${this.config.env}`;let o="application/x-www-form-urlencoded";const a={action:e,env:this.config.env,dataVersion:"2019-08-16",...t};let r;if(-1===pt.indexOf(e)&&(this._cache.keys,a.access_token=await this.oauth.getAccessToken()),"storage.uploadFile"===e){r=new FormData;for(let e in r)r.hasOwnProperty(e)&&void 0!==r[e]&&r.append(e,a[e]);o="multipart/form-data"}else{o="application/json",r={};for(let e in a)void 0!==a[e]&&(r[e]=a[e])}let i={headers:{"content-type":o}};n&&n.timeout&&(i.timeout=n.timeout),n&&n.onUploadProgress&&(i.onUploadProgress=n.onUploadProgress);const c=this._localCache.getStore(s);c&&(i.headers["X-TCB-Trace"]=c);const{parse:l,inQuery:u,search:d}=t;let h={env:this.config.env};l&&(h.parse=!0),u&&(h={...u,...h});let p=function(e,t,n={}){const s=/\?/.test(t);let o="";for(let a in n)""===o?!s&&(t+="?"):o+="&",o+=`${a}=${encodeURIComponent(n[a])}`;return/^http(s)?\:\/\//.test(t+=o)?t:`${e}${t}`}(Oe,"//tcb-api.tencentcloudapi.com/web",h);d&&(p+=d);const g=await this.post({url:p,data:r,...i}),f=g.header&&g.header["x-tcb-trace"];if(f&&this._localCache.setStore(s,f),200!==Number(g.status)&&200!==Number(g.statusCode)||!g.data)throw new ye({code:"NETWORK_ERROR",message:"network request error"});return g}async send(e,t={},n={}){const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(("ACCESS_TOKEN_DISABLED"===s.data.code||"ACCESS_TOKEN_EXPIRED"===s.data.code)&&-1===pt.indexOf(e)){await this.oauth.refreshAccessToken();const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(s.data.code)throw new ye({code:s.data.code,message:Be(s.data.message)});return s.data}if(s.data.code)throw new ye({code:s.data.code,message:Be(s.data.message)});return s.data}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}}const vt={};function _t(e){return vt[e]}class wt{constructor(e){this.config=e,this._cache=Xe(e.env),this._request=_t(e.env)}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}setAccessToken(e,t){const{accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys;this._cache.setStore(n,e),this._cache.setStore(s,t)}async refreshUserInfo(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e)}}class kt{constructor(e){if(!e)throw new ye({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=e,this._cache=Xe(this._envId),this._request=_t(this._envId),this.setUserInfo()}linkWithTicket(e){if("string"!=typeof e)throw new ye({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}linkWithRedirect(e){e.signInWithRedirect()}updatePassword(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}updateEmail(e){return this._request.send("auth.updateEmail",{newEmail:e})}updateUsername(e){if("string"!=typeof e)throw new ye({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}async getLinkedUidList(){const{data:e}=await this._request.send("auth.getLinkedUidList",{});let t=!1;const{users:n}=e;return n.forEach((e=>{e.wxOpenId&&e.wxPublicId&&(t=!0)})),{users:n,hasPrimaryUid:t}}setPrimaryUid(e){return this._request.send("auth.setPrimaryUid",{uid:e})}unlink(e){return this._request.send("auth.unlink",{platform:e})}async update(e){const{nickName:t,gender:n,avatarUrl:s,province:o,country:a,city:r}=e,{data:i}=await this._request.send("auth.updateUserInfo",{nickName:t,gender:n,avatarUrl:s,province:o,country:a,city:r});this.setLocalUserInfo(i)}async refresh(){const e=await this._request.oauth.getUserInfo();return this.setLocalUserInfo(e),e}setUserInfo(){const{userInfoKey:e}=this._cache.keys,t=this._cache.getStore(e);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((e=>{this[e]=t[e]})),this.location={country:t.country,province:t.province,city:t.city}}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e),this.setUserInfo()}}class St{constructor(e){if(!e)throw new ye({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Xe(e);const{refreshTokenKey:t,accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys,o=this._cache.getStore(t),a=this._cache.getStore(n),r=this._cache.getStore(s);this.credential={refreshToken:o,accessToken:a,accessTokenExpire:r},this.user=new kt(e)}get isAnonymousAuth(){return this.loginType===ut.ANONYMOUS}get isCustomAuth(){return this.loginType===ut.CUSTOM}get isWeixinAuth(){return this.loginType===ut.WECHAT||this.loginType===ut.WECHAT_OPEN||this.loginType===ut.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class Tt extends wt{async signIn(){this._cache.updatePersistence("local"),await this._request.oauth.getAccessToken(),st(at),st(it,{env:this.config.env,loginType:ut.ANONYMOUS,persistence:"local"});const e=new St(this.config.env);return await e.user.refresh(),e}async linkAndRetrieveDataWithTicket(e){const{anonymousUuidKey:t,refreshTokenKey:n}=this._cache.keys,s=this._cache.getStore(t),o=this._cache.getStore(n),a=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:s,refresh_token:o,ticket:e});if(a.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(a.refresh_token),await this._request.refreshAccessToken(),st(ct,{env:this.config.env}),st(it,{loginType:ut.CUSTOM,persistence:"local"}),{credential:{refreshToken:a.refresh_token}};throw new ye({message:"匿名转化失败"})}_setAnonymousUUID(e){const{anonymousUuidKey:t,loginTypeKey:n}=this._cache.keys;this._cache.removeStore(t),this._cache.setStore(t,e),this._cache.setStore(n,ut.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class bt extends wt{async signIn(e){if("string"!=typeof e)throw new ye({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:t}=this._cache.keys,n=await this._request.send("auth.signInWithTicket",{ticket:e,refresh_token:this._cache.getStore(t)||""});if(n.refresh_token)return this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),st(at),st(it,{env:this.config.env,loginType:ut.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new St(this.config.env);throw new ye({message:"自定义登录失败"})}}class At extends wt{async signIn(e,t){if("string"!=typeof e)throw new ye({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:"EMAIL",email:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token:a,access_token_expire:r}=s;if(o)return this.setRefreshToken(o),a&&r?this.setAccessToken(a,r):await this._request.refreshAccessToken(),await this.refreshUserInfo(),st(at),st(it,{env:this.config.env,loginType:ut.EMAIL,persistence:this.config.persistence}),new St(this.config.env);throw s.code?new ye({code:s.code,message:`邮箱登录失败: ${s.message}`}):new ye({message:"邮箱登录失败"})}async activate(e){return this._request.send("auth.activateEndUserMail",{token:e})}async resetPasswordWithToken(e,t){return this._request.send("auth.resetPasswordWithToken",{token:e,newPassword:t})}}class It extends wt{async signIn(e,t){if("string"!=typeof e)throw new ye({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof t&&(t="",console.warn("password is empty"));const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:ut.USERNAME,username:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token_expire:a,access_token:r}=s;if(o)return this.setRefreshToken(o),r&&a?this.setAccessToken(r,a):await this._request.refreshAccessToken(),await this.refreshUserInfo(),st(at),st(it,{env:this.config.env,loginType:ut.USERNAME,persistence:this.config.persistence}),new St(this.config.env);throw s.code?new ye({code:s.code,message:`用户名密码登录失败: ${s.message}`}):new ye({message:"用户名密码登录失败"})}}class Pt{constructor(e){this.config=e,this._cache=Xe(e.env),this._request=_t(e.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),nt(it,this._onLoginTypeChanged)}get currentUser(){const e=this.hasLoginState();return e&&e.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new Tt(this.config)}customAuthProvider(){return new bt(this.config)}emailAuthProvider(){return new At(this.config)}usernameAuthProvider(){return new It(this.config)}async signInAnonymously(){return new Tt(this.config).signIn()}async signInWithEmailAndPassword(e,t){return new At(this.config).signIn(e,t)}signInWithUsernameAndPassword(e,t){return new It(this.config).signIn(e,t)}async linkAndRetrieveDataWithTicket(e){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new Tt(this.config)),nt(ct,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(e)}async signOut(){if(this.loginType===ut.ANONYMOUS)throw new ye({message:"匿名用户不支持登出操作"});const{refreshTokenKey:e,accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,s=this._cache.getStore(e);if(!s)return;const o=await this._request.send("auth.logout",{refresh_token:s});return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.removeStore(n),st(at),st(it,{env:this.config.env,loginType:ut.NULL,persistence:this.config.persistence}),o}async signUpWithEmailAndPassword(e,t){return this._request.send("auth.signUpWithEmailAndPassword",{email:e,password:t})}async sendPasswordResetEmail(e){return this._request.send("auth.sendPasswordResetEmail",{email:e})}onLoginStateChanged(e){nt(at,(()=>{const t=this.hasLoginState();e.call(this,t)}));const t=this.hasLoginState();e.call(this,t)}onLoginStateExpired(e){nt(rt,e.bind(this))}onAccessTokenRefreshed(e){nt(lt,e.bind(this))}onAnonymousConverted(e){nt(ct,e.bind(this))}onLoginTypeChanged(e){nt(it,(()=>{const t=this.hasLoginState();e.call(this,t)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),s=this._cache.getStore(t);return this._request.oauth.isAccessTokenExpired(n,s)?null:new St(this.config.env)}async isUsernameRegistered(e){if("string"!=typeof e)throw new ye({code:"PARAM_ERROR",message:"username must be a string"});const{data:t}=await this._request.send("auth.isUsernameRegistered",{username:e});return t&&t.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(e){return new bt(this.config).signIn(e)}shouldRefreshAccessToken(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((e=>e.code?e:{...e.data,requestId:e.seqId}))}getAuthHeader(){const{refreshTokenKey:e,accessTokenKey:t}=this._cache.keys,n=this._cache.getStore(e);return{"x-cloudbase-credentials":this._cache.getStore(t)+"/@@/"+n}}_onAnonymousConverted(e){const{env:t}=e.data;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(e){const{loginType:t,persistence:n,env:s}=e.data;s===this.config.env&&(this._cache.updatePersistence(n),this._cache.setStore(this._cache.keys.loginTypeKey,t))}}const Et=function(e,t){t=t||Fe();const n=_t(this.config.env),{cloudPath:s,filePath:o,onUploadProgress:a,fileType:r="image"}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{const{data:{url:i,authorization:c,token:l,fileId:u,cosFileId:d},requestId:h}=e,p={key:s,signature:c,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":l};n.upload({url:i,data:p,file:o,name:s,fileType:r,onUploadProgress:a}).then((e=>{201===e.statusCode?t(null,{fileID:u,requestId:h}):t(new ye({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${e.data}`}))})).catch((e=>{t(e)}))})).catch((e=>{t(e)})),t.promise},xt=function(e,t){t=t||Fe();const n=_t(this.config.env),{cloudPath:s}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{t(null,e)})).catch((e=>{t(e)})),t.promise},Ct=function({fileList:e},t){if(t=t||Fe(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let s of e)if(!s||"string"!=typeof s)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const n={fileid_list:e};return _t(this.config.env).send("storage.batchDeleteFile",n).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},Nt=function({fileList:e},t){t=t||Fe(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let n=[];for(let o of e)"object"==typeof o?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),n.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?n.push({fileid:o}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const s={file_list:n};return _t(this.config.env).send("storage.batchGetDownloadUrl",s).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},Ot=async function({fileID:e},t){const n=(await Nt.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if("SUCCESS"!==n.code)return t?t(n):new Promise((e=>{e(n)}));const s=_t(this.config.env);let o=n.download_url;if(o=encodeURI(o),!t)return s.download({url:o});t(await s.download({url:o}))},Lt=function({name:e,data:t,query:n,parse:s,search:o,timeout:a},r){const i=r||Fe();let c;try{c=t?JSON.stringify(t):""}catch(u){return Promise.reject(u)}if(!e)return Promise.reject(new ye({code:"PARAM_ERROR",message:"函数名不能为空"}));const l={inQuery:n,parse:s,search:o,function_name:e,request_data:c};return _t(this.config.env).send("functions.invokeFunction",l,{timeout:a}).then((e=>{if(e.code)i(null,e);else{let n=e.data.response_data;if(s)i(null,{result:n,requestId:e.requestId});else try{n=JSON.parse(e.data.response_data),i(null,{result:n,requestId:e.requestId})}catch(t){i(new ye({message:"response data must be json"}))}}return i.promise})).catch((e=>{i(e)})),i.promise},Ut={timeout:15e3,persistence:"session"},Dt=6e5,Rt={};class Mt{constructor(e){this.config=e||this.config,this.authObj=void 0}init(e){switch(ze.adapter||(this.requestClient=new ze.adapter.reqClass({timeout:e.timeout||5e3,timeoutMsg:`请求在${(e.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...Ut,...e},!0){case this.config.timeout>Dt:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=Dt;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new Mt(this.config)}auth({persistence:e}={}){if(this.authObj)return this.authObj;const t=e||ze.adapter.primaryStorage||Ut.persistence;var n;return t!==this.config.persistence&&(this.config.persistence=t),function(e){const{env:t}=e;Qe[t]=new Ge(e),Ye[t]=new Ge({...e,persistence:"local"})}(this.config),n=this.config,vt[n.env]=new yt(n),this.authObj=new Pt(this.config),this.authObj}on(e,t){return nt.apply(this,[e,t])}off(e,t){return ot.apply(this,[e,t])}callFunction(e,t){return Lt.apply(this,[e,t])}deleteFile(e,t){return Ct.apply(this,[e,t])}getTempFileURL(e,t){return Nt.apply(this,[e,t])}downloadFile(e,t){return Ot.apply(this,[e,t])}uploadFile(e,t){return Et.apply(this,[e,t])}getUploadMetadata(e,t){return xt.apply(this,[e,t])}registerExtension(e){Rt[e.name]=e}async invokeExtension(e,t){const n=Rt[e];if(!n)throw new ye({message:`扩展${e} 必须先注册`});return await n.invoke(t,this)}useAdapters(e){const{adapter:t,runtime:n}=function(e){const t=(n=e,"[object Array]"===Object.prototype.toString.call(n)?e:[e]);var n;for(const s of t){const{isMatch:e,genAdapter:t,runtime:n}=s;if(e())return{adapter:t(),runtime:n}}}(e)||{};t&&(ze.adapter=t),n&&(ze.runtime=n)}}var qt=new Mt;function Ft(e,t,n){void 0===n&&(n={});var s=/\?/.test(t),o="";for(var a in n)""===o?!s&&(t+="?"):o+="&",o+=a+"="+encodeURIComponent(n[a]);return/^http(s)?:\/\//.test(t+=o)?t:""+e+t}class jt{get(e){const{url:t,data:n,headers:s,timeout:o}=e;return new Promise(((e,a)=>{ve.request({url:Ft("https:",t),data:n,method:"GET",header:s,timeout:o,success(t){e(t)},fail(e){a(e)}})}))}post(e){const{url:t,data:n,headers:s,timeout:o}=e;return new Promise(((e,a)=>{ve.request({url:Ft("https:",t),data:n,method:"POST",header:s,timeout:o,success(t){e(t)},fail(e){a(e)}})}))}upload(e){return new Promise(((t,n)=>{const{url:s,file:o,data:a,headers:r,fileType:i}=e,c=ve.uploadFile({url:Ft("https:",s),name:"file",formData:Object.assign({},a),filePath:o,fileType:i,header:r,success(e){const n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&a.success_action_status&&(n.statusCode=parseInt(a.success_action_status,10)),t(n)},fail(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((t=>{e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}const Vt={setItem(e,t){ve.setStorageSync(e,t)},getItem:e=>ve.getStorageSync(e),removeItem(e){ve.removeStorageSync(e)},clear(){ve.clearStorageSync()}};var Bt={genAdapter:function(){return{root:{},reqClass:jt,localStorage:Vt,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};qt.useAdapters(Bt);const $t=qt,Kt=$t.init;$t.init=function(e){e.env=e.spaceId;const t=Kt.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const n=t.auth;return t.auth=function(e){const t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((e=>{var n;t[e]=(n=t[e],function(e){e=e||{};const{success:t,fail:s,complete:o}=me(e);if(!(t||s||o))return n.call(this,e);n.call(this,e).then((e=>{t&&t(e),o&&o(e)}),(e=>{s&&s(e),o&&o(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var zt=$t;async function Ht(e,t){const n=`http://${e}:${t}/system/ping`;try{const e=await(s={url:n,timeout:500},new Promise(((e,t)=>{ve.request({...s,success(t){e(t)},fail(e){t(e)}})})));return!(!e.data||0!==e.data.code)}catch(o){return!1}var s}const Jt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"};var Wt={init(e){const t=new class{constructor(e){if(["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),!e.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},e),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=ve}async request(e,t=!0){return e=this.setupRequest(e),Promise.resolve().then((()=>xe(e,this.adapter.request)))}requestLocal(e){return new Promise(((t,n)=>{this.adapter.request(Object.assign(e,{complete(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){const t=e.data&&e.data.code||"SYS_ERR",s=e.data&&e.data.message||"request:fail";return n(new ye({code:t,message:s}))}t({success:!0,result:e.data})}}))}))}setupRequest(e){const t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=Ee(t,this.config.clientSecret);const s=Pe();n["x-client-info"]=encodeURIComponent(JSON.stringify(s));const{token:o}=we();return n["x-client-token"]=o,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}async setupLocalRequest(e){const t=Pe(),{token:n}=we(),s=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:t,token:n}),{address:o,servePort:a}=this.__dev__&&this.__dev__.debugInfo||{},{address:r}=await async function(e,t){let n;for(let s=0;s<e.length;s++){const o=e[s];if(await Ht(o,t)){n=o;break}}return{address:n,port:t}}(o,a);return{url:`http://${r}:${a}/${Jt[e.method]}`,method:"POST",data:s,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))}}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}getUploadFileOptions(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}reportUploadFile(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}uploadFile({filePath:e,cloudPath:t,fileType:n="image",onUploadProgress:s}){if(!t)throw new ye({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let o;return this.getUploadFileOptions({cloudPath:t}).then((t=>{const{url:a,formData:r,name:i}=t.result;return o=t.result.fileUrl,new Promise(((t,o)=>{const c=this.adapter.uploadFile({url:a,formData:r,name:i,filePath:e,fileType:n,success(e){e&&e.statusCode<400?t(e):o(new ye({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){o(new ye({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((()=>this.reportUploadFile({cloudPath:t}))).then((t=>new Promise(((n,s)=>{t.success?n({success:!0,filePath:e,fileID:o}):s(new ye({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(t).then((e=>{if(e.success)return e.result;throw new ye({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||0===e.length)throw new ye({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(n).then((e=>{if(e.success)return{fileList:e.result.fileList.map((e=>({fileID:e.fileID,tempFileURL:e.tempFileURL})))};throw new ye({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Gt=S((function(e,t){e.exports=b.enc.Hex}));function Qt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Yt(e="",t={}){const{data:n,functionName:s,method:o,headers:a,signHeaderKeys:r=[],config:i}=t,c=String(Date.now()),l=Qt(),u=Object.assign({},a,{"x-from-app-id":i.spaceAppId,"x-from-env-id":i.spaceId,"x-to-env-id":i.spaceId,"x-from-instance-id":c,"x-from-function-name":s,"x-client-timestamp":c,"x-alipay-source":"client","x-request-id":l,"x-alipay-callid":l,"x-trace-id":l}),d=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(r),[h="",p=""]=e.split("?")||[],g=function(e){const t="HMAC-SHA256",n=e.signedHeaders.join(";"),s=e.signedHeaders.map((t=>`${t.toLowerCase()}:${e.headers[t]}\n`)).join(""),o=Me(e.body).toString(Gt),a=`${e.method.toUpperCase()}\n${e.path}\n${e.query}\n${s}\n${n}\n${o}\n`,r=Me(a).toString(Gt),i=`${t}\n${e.timestamp}\n${r}\n`,c=qe(i,e.secretKey).toString(Gt);return`${t} Credential=${e.secretId}, SignedHeaders=${n}, Signature=${c}`}({path:h,query:p,method:o,headers:u,timestamp:c,body:JSON.stringify(n),secretId:i.accessKey,secretKey:i.secretKey,signedHeaders:d.sort()});return{url:`${i.endpoint}${e}`,headers:Object.assign({},u,{Authorization:g})}}function Xt({url:e,data:t,method:n="POST",headers:s={},timeout:o}){return new Promise(((a,r)=>{ve.request({url:e,method:n,data:"object"==typeof t?JSON.stringify(t):t,header:s,dataType:"json",timeout:o,complete:(e={})=>{const t=s["x-trace-id"]||"";if(!e.statusCode||e.statusCode>=400){const{message:n,errMsg:s,trace_id:o}=e.data||{};return r(new ye({code:"SYS_ERR",message:n||s||"request:fail",requestId:o||t}))}a({status:e.statusCode,data:e.data,headers:e.header,requestId:t})}})}))}function Zt(e,t){const{path:n,data:s,method:o="GET"}=e,{url:a,headers:r}=Yt(n,{functionName:"",data:s,method:o,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return Xt({url:a,data:s,method:o,headers:r}).then((e=>{const t=e.data||{};if(!t.success)throw new ye({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((e=>{throw new ye({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function en(e=""){const t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new ye({code:"INVALID_PARAM",message:"fileID不合法"});const s=t.substring(0,n),o=t.substring(n+1);return s!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),o}function tn(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}class nn{constructor(e){this.config=e}signedURL(e,t={}){const n=`/ws/function/${e}`,s=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),o=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Qt(),timestamp:""+Date.now()}),a=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return o[e]?"".concat(e,"=").concat(o[e]):null})).filter(Boolean).join("&"),`host:${s}`].join("\n"),r=["HMAC-SHA256",Me(a).toString(Gt)].join("\n"),i=qe(r,this.config.secretKey).toString(Gt),c=Object.keys(o).map((e=>`${e}=${encodeURIComponent(o[e])}`)).join("&");return`${this.config.wsEndpoint}${n}?${c}&signature=${i}`}}var sn={init:e=>{e.provider="alipay";const t=new class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),e.endpoint){if("string"!=typeof e.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`,wsEndpoint:e.wsEndpoint||`wss://${e.spaceId}.api-hz.cloudbasefunction.cn`}),this._websocket=new nn(this.config)}callFunction(e){return function(e,t){const{name:n,data:s,async:o=!1,timeout:a}=e,r="POST",i={"x-to-function-name":n};o&&(i["x-function-invoke-type"]="async");const{url:c,headers:l}=Yt("/functions/invokeFunction",{functionName:n,data:s,method:r,headers:i,signHeaderKeys:["x-to-function-name"],config:t});return Xt({url:c,data:s,method:r,headers:l,timeout:a}).then((e=>{let t=0;if(o){const n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new ye({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((e=>{throw new ye({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:n,formData:s,onUploadProgress:o}){return new Promise(((a,r)=>{const i=ve.uploadFile({url:e,filePath:t,fileType:n,formData:s,name:"file",success(e){e&&e.statusCode<400?a(e):r(new ye({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){r(new ye({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&i&&"function"==typeof i.onProgressUpdate&&i.onProgressUpdate((e=>{o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:e,cloudPath:t="",fileType:n="image",onUploadProgress:s}){if("string"!==D(t))throw new ye({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new ye({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new ye({code:"INVALID_PARAM",message:"cloudPath不合法"});const o=await Zt({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:a,upload_url:r,form_data:i}=o,c=i&&i.reduce(((e,t)=>(e[t.key]=t.value,e)),{});return this.uploadFileToOSS({url:r,filePath:e,fileType:n,formData:c,onUploadProgress:s}).then((()=>({fileID:a})))}async getTempFileURL({fileList:e}){return new Promise(((t,n)=>{(!e||e.length<0)&&t({code:"INVALID_PARAM",message:"fileList不能为空数组"}),e.length>50&&t({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});const s=[];for(const a of e){let e;"string"!==D(a)&&t({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{e=en.call(this,a)}catch(o){console.warn(o.errCode,o.errMsg),e=a}s.push({file_id:e,expire:600})}Zt({path:"/?download_url",data:{file_list:s},method:"POST"},this.config).then((e=>{const{file_list:n=[]}=e;t({fileList:n.map((e=>({fileID:tn.call(this,e.file_id),tempFileURL:e.download_url})))})})).catch((e=>n(e)))}))}async connectWebSocket(e){const{name:t,query:n}=e;return ve.connectSocket({url:this._websocket.signedURL(t,n),complete:()=>{}})}}(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function on({data:e}){let t;t=Pe();const n=JSON.parse(JSON.stringify(e||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){const{token:e}=we();e&&(n.uniIdToken=e)}return n}const an=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var rn=/[\\^$.*+?()[\]{}|]/g,cn=RegExp(rn.source);function ln(e,t,n){return e.replace(new RegExp((s=t)&&cn.test(s)?s.replace(rn,"\\$&"):s,"g"),n);var s}const un="request",dn="response",hn="both",pn="_globalUniCloudStatus",gn={code:2e4,message:"System error"},fn={code:20101,message:"Invalid client"};function mn(e){const{errSubject:t,subject:n,errCode:s,errMsg:o,code:a,message:r,cause:i}=e||{};return new ye({subject:t||n||"uni-secure-network",code:s||a||gn.code,message:o||r,cause:i})}let yn;function vn({secretType:e}={}){return e===un||e===dn||e===hn}function _n({name:e,data:t={}}={}){return"DCloud-clientDB"===e&&"encryption"===t.redirectTo&&"getAppClientKey"===t.action}function wn({functionName:e,result:t,logPvd:n}){}function kn(e){const t=e.callFunction,n=function(n){const s=n.name;n.data=on.call(e,{data:n.data});const o={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],a=vn(n),r=_n(n),i=a||r;return t.call(this,n).then((e=>(e.errCode=0,!i&&wn.call(this,{functionName:s,result:e,logPvd:o}),Promise.resolve(e))),(e=>(!i&&wn.call(this,{functionName:s,result:e,logPvd:o}),e&&e.message&&(e.message=function({message:e="",extraInfo:t={},formatter:n=[]}={}){for(let s=0;s<n.length;s++){const{rule:o,content:a,mode:r}=n[s],i=e.match(o);if(!i)continue;let c=a;for(let e=1;e<i.length;e++)c=ln(c,`{$${e}}`,i[e]);for(const e in t)c=ln(c,`{${e}}`,t[e]);return"replace"===r?c:e+c}return e}({message:`[${n.name}]: ${e.message}`,formatter:an,extraInfo:{functionName:s}})),Promise.reject(e))))};e.callFunction=function(t){const{provider:s,spaceId:o}=e.config,a=t.name;let r,i;return t.data=t.data||{},r=n,r=r.bind(e),i=_n(t)?n.call(e,t):vn(t)?new yn({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function({provider:e,spaceId:t,functionName:n}={}){const{appId:s,uniPlatform:o,osName:a}=be();let r=o;"app"===o&&(r=a);const i=function({provider:e,spaceId:t}={}){const n=$;if(!n)return{};e=function(e){return"tencent"===e?"tcb":e}(e);const s=n.find((n=>n.provider===e&&n.spaceId===t));return s&&s.config}({provider:e,spaceId:t});if(!i||!i.accessControl||!i.accessControl.enable)return!1;const c=i.accessControl.function||{},l=Object.keys(c);if(0===l.length)return!0;const u=function(e,t){let n,s,o;for(let a=0;a<e.length;a++){const r=e[a];r!==t?"*"!==r?r.split(",").map((e=>e.trim())).indexOf(t)>-1&&(s=r):o=r:n=r}return n||s||o}(l,n);if(!u)return!1;if((c[u]||[]).find(((e={})=>e.appId===s&&(e.platform||"").toLowerCase()===r.toLowerCase())))return!0;throw console.error(`此应用[appId: ${s}, platform: ${r}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),mn(fn)}({provider:s,spaceId:o,functionName:a})?new yn({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):r(t),Object.defineProperty(i,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),i.then((e=>("undefined"!=typeof UTSJSONObject&&(e.result=new UTSJSONObject(e.result)),e)))}}yn=class{constructor(){throw mn({message:"Platform app is not enabled, please check whether secure network module is enabled in your manifest.json"})}};const Sn=Symbol("CLIENT_DB_INTERNAL");function Tn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=Sn,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(e,n,s){if("_uniClient"===n)return null;if("symbol"==typeof n)return e[n];if(n in e||"string"!=typeof n){const t=e[n];return"function"==typeof t?t.bind(e):t}return t.get(e,n,s)}})}function bn(e){return{on:(t,n)=>{e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:(t,n)=>{e[t]=e[t]||[];const s=e[t].indexOf(n);-1!==s&&e[t].splice(s,1)}}}const An=["db.Geo","db.command","command.aggregate"];function In(e,t){return An.indexOf(`${e}.${t}`)>-1}function Pn(e){switch(D(e=_e(e))){case"array":return e.map((e=>Pn(e)));case"object":return e._internalType===Sn||Object.keys(e).forEach((t=>{e[t]=Pn(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function En(e){return e&&e.content&&e.content.$method}class xn{constructor(e,t,n){this.content=e,this.prevStage=t||null,this.udb=null,this._database=n}toJSON(){let e=this;const t=[e.content];for(;e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((e=>({$method:e.$method,$param:Pn(e.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const e=this.toJSON().$db.find((e=>"action"===e.$method));return e&&e.$param&&e.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((e=>"action"!==e.$method))}}get isAggregate(){let e=this;for(;e;){const t=En(e),n=En(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}get isCommand(){let e=this;for(;e;){if("command"===En(e))return!0;e=e.prevStage}return!1}get isAggregateCommand(){let e=this;for(;e;){const t=En(e),n=En(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}getNextStageFn(e){const t=this;return function(){return Cn({$method:e,$param:Pn(Array.from(arguments))},t,t._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(e,t){const n=this.getAction(),s=this.getCommand();return s.$db.push({$method:e,$param:Pn(t)}),this._database._callCloudFunction({action:n,command:s})}}function Cn(e,t,n){return Tn(new xn(e,t,n),{get(e,t){let s="db";return e&&e.content&&(s=e.content.$method),In(s,t)?Cn({$method:t},e,n):function(){return Cn({$method:t,$param:Pn(Array.from(arguments))},e,n)}}})}function Nn({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map((e=>({$method:e}))),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function On(e,t={}){return Tn(new e(t),{get:(e,t)=>In("db",t)?Cn({$method:t},null,e):function(){return Cn({$method:t,$param:Pn(Array.from(arguments))},null,e)}})}class Ln extends class{constructor({uniClient:e={},isJQL:t=!1}={}){this._uniClient=e,this._authCallBacks={},this._dbCallBacks={},e._isDefault&&(this._dbCallBacks=W("_globalUniCloudDatabaseCallback")),t||(this.auth=bn(this._authCallBacks)),this._isJQL=t,Object.assign(this,bn(this._dbCallBacks)),this.env=Tn({},{get:(e,t)=>({$env:t})}),this.Geo=Tn({},{get:(e,t)=>Nn({path:["Geo"],method:t})}),this.serverDate=Nn({path:[],method:"serverDate"}),this.RegExp=Nn({path:[],method:"RegExp"})}getCloudEnv(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}_callback(e,t){const n=this._dbCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}_callbackAuth(e,t){const n=this._authCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}multiSend(){const e=Array.from(arguments),t=e.map((e=>{const t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}{_parseResult(e){return this._isJQL?e.result:e}_callCloudFunction({action:e,command:t,multiCommand:n,queryList:s}){function o(e,t){if(n&&s)for(let n=0;n<s.length;n++){const o=s[n];o.udb&&"function"==typeof o.udb.setResult&&(t?o.udb.setResult(t):o.udb.setResult(e.result.dataList[n]))}}const a=this,r=this._isJQL?"databaseForJQL":"database";function i(e){return a._callback("error",[e]),Z(ee(r,"fail"),e).then((()=>Z(ee(r,"complete"),e))).then((()=>(o(null,e),he(se,{type:re,content:e}),Promise.reject(e))))}const c=Z(ee(r,"invoke")),l=this._uniClient;return c.then((()=>l.callFunction({name:"DCloud-clientDB",type:O,data:{action:e,command:t,multiCommand:n}}))).then((e=>{const{code:t,message:n,token:s,tokenExpired:c,systemInfo:l=[]}=e.result;if(l)for(let o=0;o<l.length;o++){const{level:e,message:t,detail:n}=l[o];let s="[System Info]"+t;n&&(s=`${s}\n详细信息：${n}`),(console["warn"===e?"error":e]||console.log)(s)}if(t)return i(new ye({code:t,message:n,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,s&&c&&(ke({token:s,tokenExpired:c}),this._callbackAuth("refreshToken",[{token:s,tokenExpired:c}]),this._callback("refreshToken",[{token:s,tokenExpired:c}]),he(ae,{token:s,tokenExpired:c}));const u=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let o=0;o<u.length;o++){const{prop:t,tips:n}=u[o];if(t in e.result){const s=e.result[t];Object.defineProperty(e.result,t,{get:()=>(console.warn(n),s)})}}return d=e,Z(ee(r,"success"),d).then((()=>Z(ee(r,"complete"),d))).then((()=>{o(d,null);const e=a._parseResult(d);return he(se,{type:re,content:e}),Promise.resolve(e)}));var d}),(e=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),i(new ye({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId})))))}}const Un="token无效，跳转登录页面",Dn="token过期，跳转登录页面",Rn={TOKEN_INVALID_TOKEN_EXPIRED:Dn,TOKEN_INVALID_INVALID_CLIENTID:Un,TOKEN_INVALID:Un,TOKEN_INVALID_WRONG_TOKEN:Un,TOKEN_INVALID_ANONYMOUS_USER:Un},Mn={"uni-id-token-expired":Dn,"uni-id-check-token-failed":Un,"uni-id-token-not-exist":Un,"uni-id-check-device-feature-failed":Un};function qn(e,t){let n="";return n=e?`${e}/${t}`:t,n.replace(/^\//,"")}function Fn(e=[],t=""){const n=[],s=[];return e.forEach((e=>{!0===e.needLogin?n.push(qn(t,e.path)):!1===e.needLogin&&s.push(qn(t,e.path))})),{needLoginPage:n,notNeedLoginPage:s}}function jn(e){return e.split("?")[0].replace(/^\//,"")}function Vn(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){const e=getCurrentPages();return e[e.length-1]}())}function Bn(){return jn(Vn())}function $n(e="",t={}){if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;const n=t.list,s=jn(e);return n.some((e=>e.pagePath===s))}const Kn=!!k.uniIdRouter,{loginPage:zn,routerNeedLogin:Hn,resToLogin:Jn,needLoginPage:Wn,notNeedLoginPage:Gn,loginPageInTabBar:Qn}=function({pages:e=[],subPackages:t=[],uniIdRouter:n={},tabBar:s={}}=k){const{loginPage:o,needLogin:a=[],resToLogin:r=!0}=n,{needLoginPage:i,notNeedLoginPage:c}=Fn(e),{needLoginPage:l,notNeedLoginPage:u}=function(e=[]){const t=[],n=[];return e.forEach((e=>{const{root:s,pages:o=[]}=e,{needLoginPage:a,notNeedLoginPage:r}=Fn(o,s);t.push(...a),n.push(...r)})),{needLoginPage:t,notNeedLoginPage:n}}(t);return{loginPage:o,routerNeedLogin:a,resToLogin:r,needLoginPage:[...i,...l],notNeedLoginPage:[...c,...u],loginPageInTabBar:$n(o,s)}}();if(Wn.indexOf(zn)>-1)throw new Error(`Login page [${zn}] should not be "needLogin", please check your pages.json`);function Yn(e){const t=Bn();if("/"===e.charAt(0))return e;const[n,s]=e.split("?"),o=n.replace(/^\//,"").split("/"),a=t.split("/");a.pop();for(let r=0;r<o.length;r++){const e=o[r];".."===e?a.pop():"."!==e&&a.push(e)}return""===a[0]&&a.shift(),"/"+a.join("/")+(s?"?"+s:"")}function Xn({redirect:e}){const t=jn(e),n=jn(zn);return Bn()!==n&&t!==n}function Zn({api:e,redirect:t}={}){if(!t||!Xn({redirect:t}))return;const n=(o=t,"/"!==(s=zn).charAt(0)&&(s="/"+s),o?s.indexOf("?")>-1?s+`&uniIdRedirectUrl=${encodeURIComponent(o)}`:s+`?uniIdRedirectUrl=${encodeURIComponent(o)}`:s);var s,o;Qn?"navigateTo"!==e&&"redirectTo"!==e||(e="switchTab"):"switchTab"===e&&(e="navigateTo");const a={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((()=>{a[e]({url:n})}),0)}function es({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){const{token:e,tokenExpired:t}=we();let n;if(e){if(t<Date.now()){const e="uni-id-token-expired";n={errCode:e,errMsg:Mn[e]}}}else{const e="uni-id-check-token-failed";n={errCode:e,errMsg:Mn[e]}}return n}();if(function(e){const t=jn(Yn(e));return!(Gn.indexOf(t)>-1)&&(Wn.indexOf(t)>-1||Hn.some((t=>{return n=e,new RegExp(t).test(n);var n})))}(e)&&n){if(n.uniIdRedirectUrl=e,le(oe).length>0)return setTimeout((()=>{he(oe,n)}),0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function ts(){!function(){const e=Vn(),{abortLoginPageJump:t,autoToLoginPage:n}=es({url:e});t||n&&Zn({api:"redirectTo",redirect:e})}();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const n=e[t];uni.addInterceptor(n,{invoke(e){const{abortLoginPageJump:t,autoToLoginPage:s}=es({url:e.url});return t?e:s?(Zn({api:n,redirect:Yn(e.url)}),!1):e}})}}function ns(){this.onResponse((e=>{const{type:t,content:n}=e;let s=!1;switch(t){case"cloudobject":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in Mn}(n);break;case"clientdb":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in Rn}(n)}s&&function(e={}){const t=le(oe);fe().then((()=>{const n=Vn();if(n&&Xn({redirect:n}))return t.length>0?he(oe,Object.assign({uniIdRedirectUrl:n},e)):void(zn&&Zn({api:"navigateTo",redirect:n}))}))}(n)}))}function ss(e){var t;(t=e).onResponse=function(e){ue(se,e)},t.offResponse=function(e){de(se,e)},function(e){e.onNeedLogin=function(e){ue(oe,e)},e.offNeedLogin=function(e){de(oe,e)},Kn&&(W(pn).needLoginInit||(W(pn).needLoginInit=!0,fe().then((()=>{ts.call(e)})),Jn&&ns.call(e)))}(e),function(e){e.onRefreshToken=function(e){ue(ae,e)},e.offRefreshToken=function(e){de(ae,e)}}(e)}let os;const as="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",rs=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function is(){const e=we().token||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((s=t[1],decodeURIComponent(os(s).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}var s;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}os="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!rs.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,s,o="",a=0;a<e.length;)t=as.indexOf(e.charAt(a++))<<18|as.indexOf(e.charAt(a++))<<12|(n=as.indexOf(e.charAt(a++)))<<6|(s=as.indexOf(e.charAt(a++))),o+=64===n?String.fromCharCode(t>>16&255):64===s?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}:atob;var cs=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(S((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const n="chooseAndUploadFile:ok",s="chooseAndUploadFile:fail";function o(e,t){return e.tempFiles.forEach(((e,n)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}function a(e,t,{onChooseFile:s,onUploadProgress:o}){return t.then((e=>{if(s){const t=s(e);if(void 0!==t)return Promise.resolve(t).then((t=>void 0===t?e:t))}return e})).then((t=>!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t,s=5,o){(t=Object.assign({},t)).errMsg=n;const a=t.tempFiles,r=a.length;let i=0;return new Promise((n=>{for(;i<s;)c();function c(){const s=i++;if(s>=r)return void(!a.find((e=>!e.url&&!e.errMsg))&&n(t));const l=a[s];e.uploadFile({provider:l.provider,filePath:l.path,cloudPath:l.cloudPath,fileType:l.fileType,cloudPathAsRealPath:l.cloudPathAsRealPath,onUploadProgress(e){e.index=s,e.tempFile=l,e.tempFilePath=l.path,o&&o(e)}}).then((e=>{l.url=e.fileID,s<r&&c()})).catch((e=>{l.errMsg=e.errMsg||e.message,s<r&&c()}))}}))}(e,t,5,o)))}t.initChooseAndUploadFile=function(e){return function(t={type:"all"}){return"image"===t.type?a(e,function(e){const{count:t,sizeType:n,sourceType:a=["album","camera"],extension:r}=e;return new Promise(((e,i)=>{uni.chooseImage({count:t,sizeType:n,sourceType:a,extension:r,success(t){e(o(t,"image"))},fail(e){i({errMsg:e.errMsg.replace("chooseImage:fail",s)})}})}))}(t),t):"video"===t.type?a(e,function(e){const{camera:t,compressed:n,maxDuration:a,sourceType:r=["album","camera"],extension:i}=e;return new Promise(((e,c)=>{uni.chooseVideo({camera:t,compressed:n,maxDuration:a,sourceType:r,extension:i,success(t){const{tempFilePath:n,duration:s,size:a,height:r,width:i}=t;e(o({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:a,type:t.tempFile&&t.tempFile.type||"",width:i,height:r,duration:s,fileType:"video",cloudPath:""}]},"video"))},fail(e){c({errMsg:e.errMsg.replace("chooseVideo:fail",s)})}})}))}(t),t):a(e,function(e){const{count:t,extension:n}=e;return new Promise(((e,a)=>{let r=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(r=wx.chooseMessageFile),"function"!=typeof r)return a({errMsg:s+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});r({type:"all",count:t,extension:n,success(t){e(o(t))},fail(e){a({errMsg:e.errMsg.replace("chooseFile:fail",s)})}})}))}(t),t)}}})));const ls="manual";function us(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var e=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((t=>{e.push(this[t])})),e}),((e,t)=>{if(this.loadtime===ls)return;let n=!1;const s=[];for(let o=2;o<e.length;o++)e[o]!==t[o]&&(s.push(e[o]),n=!0);e[0]!==t[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(n,s)}))},methods:{onMixinDatacomPropsChange(e,t){},mixinDatacomEasyGet({getone:e=!1,success:t,fail:n}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((n=>{this.mixinDatacomLoading=!1;const{data:s,count:o}=n.result;this.getcount&&(this.mixinDatacomPage.count=o),this.mixinDatacomHasMore=s.length<this.pageSize;const a=e?s.length?s[0]:void 0:s;this.mixinDatacomResData=a,t&&t(a)})).catch((e=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e,this.mixinDatacomError=e,n&&n(e)})))},mixinDatacomGet(t={}){let n;t=t||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);const s=t.action||this.action;s&&(n=n.action(s));const o=t.collection||this.collection;n=Array.isArray(o)?n.collection(...o):n.collection(o);const a=t.where||this.where;a&&Object.keys(a).length&&(n=n.where(a));const r=t.field||this.field;r&&(n=n.field(r));const i=t.foreignKey||this.foreignKey;i&&(n=n.foreignKey(i));const c=t.groupby||this.groupby;c&&(n=n.groupBy(c));const l=t.groupField||this.groupField;l&&(n=n.groupField(l)),!0===(void 0!==t.distinct?t.distinct:this.distinct)&&(n=n.distinct());const u=t.orderby||this.orderby;u&&(n=n.orderBy(u));const d=void 0!==t.pageCurrent?t.pageCurrent:this.mixinDatacomPage.current,h=void 0!==t.pageSize?t.pageSize:this.mixinDatacomPage.size,p=void 0!==t.getcount?t.getcount:this.getcount,g=void 0!==t.gettree?t.gettree:this.gettree,f=void 0!==t.gettreepath?t.gettreepath:this.gettreepath,m={getCount:p},y={limitLevel:void 0!==t.limitlevel?t.limitlevel:this.limitlevel,startWith:void 0!==t.startwith?t.startwith:this.startwith};return g&&(m.getTree=y),f&&(m.getTreePath=y),n=n.skip(h*(d-1)).limit(h).get(m),n}}}}function ds(e){return W("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function hs({openid:e,callLoginByWeixin:t=!1}={}){throw ds(this),new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `app`")}async function ps(e){const t=ds(this);return t.initPromise||(t.initPromise=hs.call(this,e).then((e=>e)).catch((e=>{throw delete t.initPromise,e}))),t.initPromise}function gs(e){Ie=e}function fs(e){const t={getSystemInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(n){return new Promise(((s,o)=>{t[e]({...n,success(e){s(e)},fail(e){o(e)}})}))}}class ms extends class{constructor(){this._callback={}}addListener(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}on(e,t){return this.addListener(e,t)}removeListener(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');const n=this._callback[e];if(!n)return;const s=function(e,t){for(let n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(s,1)}off(e,t){return this.removeListener(e,t)}removeAllListener(e){delete this._callback[e]}emit(e,...t){const n=this._callback[e];if(n)for(let s=0;s<n.length;s++)n[s](...t)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([fs("getSystemInfo")(),fs("getPushClientId")()]).then((([{appId:e}={},{cid:t}={}]=[])=>{if(!e)throw new Error("Invalid appId, please check the manifest.json file");if(!t)throw new Error("Invalid push client id");this._appId=e,this._pushClientId=t,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(e=>{throw this.emit("error",e),this.close(),e}))}async open(){return this.init()}_isUniCloudSSE(e){if("receive"!==e.type)return!1;const t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}_receivePushMessage(e){if(!this._isUniCloudSSE(e))return;const t=e&&e.data&&e.data.payload,{action:n,messageId:s,message:o}=t;this._payloadQueue.push({action:n,messageId:s,message:o}),this._consumMessage()}_consumMessage(){for(;;){const e=this._payloadQueue.find((e=>e.messageId===this._currentMessageId+1));if(!e)break;this._currentMessageId++,this._parseMessagePayload(e)}}_parseMessagePayload(e){const{action:t,messageId:n,message:s}=e;"end"===t?this._end({messageId:n,message:s}):"message"===t&&this._appendMessage({messageId:n,message:s})}_appendMessage({messageId:e,message:t}={}){this.emit("message",t)}_end({messageId:e,message:t}={}){this.emit("end",t),this.close()}_initMessageListener(){uni.onPushMessage(this._uniPushMessageCallback)}_destroy(){uni.offPushMessage(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}const ys={tcb:zt,tencent:zt,aliyun:Ne,private:Wt,dcloud:Wt,alipay:sn};let vs=new class{init(e){let t={};const n=ys[e.provider];if(!n)throw new Error("未提供正确的provider参数");var s;return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new V({createPromise:function(){let t=Promise.resolve();t=new Promise((e=>{setTimeout((()=>{e()}),1)}));const n=e.auth();return t.then((()=>n.getLoginState())).then((e=>e?Promise.resolve():n.signInAnonymously()))}}))}(t),kn(t),function(e){const t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),(s=t).database=function(e){if(e&&Object.keys(e).length>0)return s.init(e).database();if(this._database)return this._database;const t=On(Ln,{uniClient:s});return this._database=t,t},s.databaseForJQL=function(e){if(e&&Object.keys(e).length>0)return s.init(e).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const t=On(Ln,{uniClient:s,isJQL:!0});return this._databaseForJQL=t,t},function(e){e.getCurrentUserInfo=is,e.chooseAndUploadFile=cs.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return us(e)}}),e.SSEChannel=ms,e.initSecureNetworkByWeixin=function(e){return function({openid:t,callLoginByWeixin:n=!1}={}){return ps.call(e,{openid:t,callLoginByWeixin:n})}}(e),e.setCustomClientInfo=gs,e.importObject=function(t){return function(n,s={}){s=function(e,t={}){return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==typeof t.secretMethods&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},s);const{customUI:o,loadingOptions:a,errorOptions:r,parseSystemError:i}=s,c=!o;return new Proxy({},{get(o,l){switch(l){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:e,interceptorName:t,getCallbackArgs:n}={}){return async function(...s){const o=n?n({params:s}):{};let a,r;try{return await Z(ee(t,"invoke"),{...o}),a=await e(...s),await Z(ee(t,"success"),{...o,result:a}),a}catch(i){throw r=i,await Z(ee(t,"fail"),{...o,error:r}),r}finally{await Z(ee(t,"complete"),r?{...o,error:r}:{...o,result:a})}}}({fn:async function o(...u){let d;c&&uni.showLoading({title:a.title,mask:a.mask});const h={name:n,type:N,data:{method:l,params:u}};"object"==typeof s.secretMethods&&function(e,t){const n=t.data.method,s=e.secretMethods||{},o=s[n]||s["*"];o&&(t.secretType=o)}(s,h);let p=!1;try{d=await t.callFunction(h)}catch(e){p=!0,d={result:new ye(e)}}const{errSubject:g,errCode:f,errMsg:m,newToken:y}=d.result||{};if(c&&uni.hideLoading(),y&&y.token&&y.tokenExpired&&(ke(y),he(ae,{...y})),f){let e=m;if(p&&i&&(e=(await i({objectName:n,methodName:l,params:u,errSubject:g,errCode:f,errMsg:m})).errMsg||m),c)if("toast"===r.type)uni.showToast({title:e,icon:"none"});else{if("modal"!==r.type)throw new Error(`Invalid errorOptions.type: ${r.type}`);{const{confirm:t}=await async function({title:e,content:t,showCancel:n,cancelText:s,confirmText:o}={}){return new Promise(((a,r)=>{uni.showModal({title:e,content:t,showCancel:n,cancelText:s,confirmText:o,success(e){a(e)},fail(){a({confirm:!1,cancel:!0})}})}))}({title:"提示",content:e,showCancel:r.retry,cancelText:"取消",confirmText:r.retry?"重试":"确定"});if(r.retry&&t)return o(...u)}}const t=new ye({subject:g,code:f,message:m,requestId:d.requestId});throw t.detail=d.result,he(se,{type:ce,content:t}),t}return he(se,{type:ce,content:d.result}),d.result},interceptorName:"callObject",getCallbackArgs:function({params:e}={}){return{objectName:n,methodName:l,params:e}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((e=>{if(!t[e])return;const n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){let s=!1;if("callFunction"===t){const e=n&&n.type||C;s=e!==C}const o="callFunction"===t&&!s,a=this._initPromiseHub.exec();n=n||{};const{success:r,fail:i,complete:c}=me(n),l=a.then((()=>s?Promise.resolve():Z(ee(t,"invoke"),n))).then((()=>e.call(this,n))).then((e=>s?Promise.resolve(e):Z(ee(t,"success"),e).then((()=>Z(ee(t,"complete"),e))).then((()=>(o&&he(se,{type:ie,content:e}),Promise.resolve(e))))),(e=>s?Promise.reject(e):Z(ee(t,"fail"),e).then((()=>Z(ee(t,"complete"),e))).then((()=>(he(se,{type:ie,content:e}),Promise.reject(e))))));if(!(r||i||c))return l;l.then((e=>{r&&r(e),c&&c(e),o&&he(se,{type:ie,content:e})}),(e=>{i&&i(e),c&&c(e),o&&he(se,{type:ie,content:e})}))}}(t[e],e).bind(t)})),t.init=this.init,t}};(()=>{const e=K;let t={};if(e&&1===e.length)t=e[0],vs=vs.init(t),vs._isDefault=!0;else{const t=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let n;n=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",t.forEach((e=>{vs[e]=function(){return console.error(n),Promise.reject(new ye({code:"SYS_ERR",message:n}))}}))}Object.assign(vs,{get mixinDatacom(){return us(vs)}}),ss(vs),vs.addInterceptor=Y,vs.removeInterceptor=X,vs.interceptObject=te,uni.__uniCloud=vs;{const e=H||(H=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),H);e.uniCloud=vs,e.UniCloudError=ye}})();const{app:_s,Vuex:ws,Pinia:ks}=function(){const t=e.createVueApp(m);return t.config.globalProperties.$request=w.request,t.config.globalProperties.$http=w.http,t.config.globalProperties.baseURL=BASE_URL,{app:t}}();uni.Vuex=ws,uni.Pinia=ks,_s.provide("__globalStyles",__uniConfig.styles),_s._component.mpType="app",_s._component.render=()=>{},_s.mount("#app")}(Vue);
