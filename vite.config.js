import { defineConfig } from 'vite';

export default defineConfig({
  // 环境变量前缀
  envPrefix: 'VITE_',
  
  // 构建配置
  build: {
    // 输出目录
    outDir: 'dist',
    // 是否生成source map
    sourcemap: false,
  },
  
  // 服务器配置
  server: {
    // 端口
    port: 3000,
    // 是否自动打开浏览器
    open: true,
    // 代理配置
    proxy: {
      // 如果需要代理API请求，可以在这里配置
      // '/api': {
      //   target: 'http://localhost:8080',
      //   changeOrigin: true,
      //   rewrite: (path) => path.replace(/^\/api/, '')
      // }
    }
  },
  
  // 插件配置
  plugins: [],
  
  // 解析配置
  resolve: {
    // 别名配置
    alias: {
      '@': '/src'
    }
  }
});