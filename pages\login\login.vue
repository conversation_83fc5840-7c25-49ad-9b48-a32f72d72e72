<template>
	<view class="login-page">
		<image class="bg-image" src="/static/pet/rabbit.png" mode="aspectFill"></image>
		<view class="login-container">
			<view class="logo-area">
				<image class="logo" src="/static/pet/rabbit.png"></image>
				<text class="app-title">GuiYuan</text>
			</view>

			<view class="form-container">
				<view class="input-item">
					<input type="number" maxlength="11" v-model="phone" placeholder="请输入手机号" placeholder-class="placeholder" @input="validatePhone" />
				</view>

				<view class="code-container">
					<view class="input-item code-input">
						<input type="number" maxlength="6" v-model="code" placeholder="请输入验证码" placeholder-class="placeholder" />
					</view>
					<button class="send-code-btn" @click="sendCode" :disabled="isCounting">
						{{ isCounting ? `${countdown}s后重试` : '发送验证码' }}
					</button>
				</view>

				<button class="login-btn" @click="login">登录</button>

				<!-- 测试网络连接按钮 -->
				<button class="test-btn" @click="testConnection">测试网络连接</button>

			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { BASE_URL } from '@/config/index.js';

const phone = ref('');
const code = ref('');
const isCounting = ref(false);
const countdown = ref(60);

// 验证手机号格式
const validatePhone = () => {
	// 限制只能输入数字
	phone.value = phone.value.replace(/\D/g, '');
	// 限制长度为11位
	if (phone.value.length > 11) {
		phone.value = phone.value.slice(0, 11);
	}
}

// 页面加载时检查Cookie
onMounted(async () => {
	console.log('登录页面加载完成');

	// 检查Cookie并验证
	await checkCookieAndNavigate();
});

const checkCookieAndNavigate = async () => {
	const authorization = uni.getStorageSync('authorization');

	if (!authorization) {
		console.log('未找到Cookie，停留在登录页');
		return;
	}

	console.log('检测到Cookie，验证中...');

	// 设置较短的超时时间，避免长时间等待
	const timeoutPromise = new Promise((_, reject) => {
		setTimeout(() => reject(new Error('请求超时')), 5000);
	});

	const requestPromise = uni.request({
		url: BASE_URL + 'user',
		method: 'GET',
		header: {
			'Cookie': `Authorization=${authorization}`,
			'Content-Type': 'application/json'
		},
		withCredentials: true
	});

	try {
		const res = await Promise.race([requestPromise, timeoutPromise]);
		console.log('Cookie验证结果:', res);

		if (res.data && res.data.code === 0) {
			// Cookie有效，跳转到首页
			console.log('Cookie有效，跳转到首页');
			setTimeout(() => {
				uni.switchTab({
					url: '/pages/tabbar/index/index'
				});
			}, 100);
		} else {
			// Cookie无效，清除并停留在登录页
			console.log('Cookie无效，清除本地数据');
			uni.removeStorageSync('authorization');
		}
	} catch (error) {
		console.log('Cookie验证失败:', error.message || error);
		// 验证失败，清除Cookie，停留在登录页
		uni.removeStorageSync('authorization');

		// 如果是网络错误，给用户提示
		if (error.message === '请求超时') {
			uni.showToast({
				title: '网络连接超时',
				icon: 'none',
				duration: 2000
			});
		}
	}
};

// 测试网络连接
const testConnection = async () => {
	console.log('测试网络连接...');
	uni.showLoading({
		title: '测试中...'
	});

	try {
		const res = await uni.request({
			url: BASE_URL + 'user',
			method: 'GET',
			header: {
				'Content-Type': 'application/json'
			},
			timeout: 5000
		});

		uni.hideLoading();
		console.log('网络测试结果:', res);

		uni.showModal({
			title: '网络测试结果',
			content: `状态码: ${res.statusCode}\n响应: ${JSON.stringify(res.data)}`,
			showCancel: false
		});
	} catch (error) {
		uni.hideLoading();
		console.log('网络测试失败:', error);

		uni.showModal({
			title: '网络测试失败',
			content: `错误: ${error.message || error}`,
			showCancel: false
		});
	}
};;

// 开始倒计时
const startCountdown = () => {
	isCounting.value = true;
	countdown.value = 60;

	const timer = setInterval(() => {
		countdown.value--;
		if (countdown.value <= 0) {
			clearInterval(timer);
			isCounting.value = false;
		}
	}, 1000);
};

const sendCode = () => {
	// 验证手机号
	if (!phone.value) {
		uni.showToast({
			title: '请输入手机号',
			icon: 'none'
		});
		return;
	}

	if (phone.value.length !== 11) {
		uni.showToast({
			title: '请输入11位手机号',
			icon: 'none'
		});
		return;
	}

	if (isCounting.value) {
		return;
	}

	// 启动倒计时
	startCountdown();

	uni.request({
		url: BASE_URL + 'user',
		method: 'POST',
		data: {
			phone: phone.value,
		},
		header:{
			'Content-Type': 'application/json'
		}
	}).then(res => {
		console.log(res);
		uni.showToast({
			title: '验证码已发送',
			icon: 'success'
		});
	}).catch(err => {
		console.log(err);
		uni.showToast({
			title: '发送失败，请重试',
			icon: 'none'
		});
		// 发送失败时停止倒计时
		isCounting.value = false;
	});
}

const login = () => {
	// 验证输入
	if (!phone.value || !code.value) {
		uni.showToast({
			title: '请输入手机号和验证码',
			icon: 'none'
		});
		return;
	}

	if (phone.value.length !== 11) {
		uni.showToast({
			title: '请输入11位手机号',
			icon: 'none'
		});
		return;
	}

	if (code.value.length !==6 ) {
		uni.showToast({
			title: '验证码格式错误',
			icon: 'none'
		});
		return;
	}

	uni.request({
		url: BASE_URL + 'user',
		method: 'POST',
		data: {
			phone: phone.value,
			code: code.value
		},
		header:{
			'Content-Type': 'application/json'
		}
	}).then(res => {
		console.log(res);
		// 保存token
		uni.setStorageSync('authorization', res.data.data.authorization);
		// 先打印一下保存的token值
		console.log('保存的authorization:', uni.getStorageSync('authorization'));

		// 使用reLaunch强制跳转到首页
		uni.reLaunch({
			url: '/pages/tabbar/index/index',
			success: function() {
				// 延迟显示Toast确保跳转成功
				setTimeout(() => {
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					});
				}, 200);
			},
			fail: function(err) {
				console.error('跳转到首页失败:', err);
			}
		});
	}).catch(err => {
		console.log(err);
		uni.showToast({
			title: '登录失败，请重试',
			icon: 'none'
		});
	});
}
</script>

<style>
.login-page {
	position: relative;
	width: 100%;
	height: 100vh;
	overflow: hidden;
}

.bg-image {
	position: absolute;
	width: 100%;
	height: 100%;
	object-fit: cover;
	opacity: 0.85;
	filter: blur(3px);
	z-index: -1;
}

.login-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 50rpx;
	height: 100%;
	box-sizing: border-box;
}

.logo-area {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 60rpx;
	margin-bottom: 80rpx;
}

.logo {
	width: 180rpx;
	height: 180rpx;
	border-radius: 20rpx;
	box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
}

.app-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #ffffff;
	margin-top: 30rpx;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.form-container {
	width: 100%;
	background-color: rgba(255, 255, 255, 0.85);
	border-radius: 30rpx;
	padding: 50rpx 40rpx;
	box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-item {
	display: flex;
	align-items: center;
	height: 100rpx;
	background-color: #f6f6f6;
	border-radius: 50rpx;
	padding: 0 30rpx;
	margin-bottom: 30rpx;
}

.placeholder {
	color: #999;
	font-size: 28rpx;
}

.code-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 50rpx;
}

.code-input {
	flex: 1;
	margin-right: 20rpx;
	margin-bottom: 0;
}

.send-code-btn {
	width: 220rpx;
	height: 100rpx;
	background: linear-gradient(45deg, #ff9a9e, #fad0c4);
	color: #fff;
	border-radius: 50rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 10rpx 20rpx rgba(255, 154, 158, 0.3);
}

.send-code-btn[disabled] {
	background: #cccccc;
	opacity: 0.8;
}

.login-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(45deg, #a6c1ee, #fbc2eb);
	color: #fff;
	border-radius: 50rpx;
	font-size: 36rpx;
	font-weight: bold;
	box-shadow: 0 10rpx 20rpx rgba(166, 193, 238, 0.3);
	margin-bottom: 20rpx;
}

.test-btn {
	width: 100%;
	height: 80rpx;
	background: #666;
	color: #fff;
	border-radius: 40rpx;
	font-size: 28rpx;
	margin-bottom: 20rpx;
}



.other-login {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 50rpx;
}

.other-text {
	color: #999;
	font-size: 26rpx;
	margin-bottom: 30rpx;
	position: relative;
}

.other-text::before,
.other-text::after {
	content: '';
	position: absolute;
	top: 50%;
	width: 80rpx;
	height: 1px;
	background-color: #e0e0e0;
}

.other-text::before {
	left: -100rpx;
}

.other-text::after {
	right: -100rpx;
}

.icon-group {
	display: flex;
	justify-content: center;
}

.icon-item {
	width: 80rpx;
	height: 80rpx;
	background-color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 30rpx;
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
}

.iconfont {
	color: #666;
	font-size: 40rpx;
}

.icon-wechat {
	color: #07c160;
}

.icon-qq {
	color: #12b7f5;
}
</style>
