/**
 * 配置和登录测试工具
 * 用于验证配置是否正确加载和测试登录流程
 */

import { BASE_URL, API_TIMEOUT, ENV } from '@/config/index.js';

export const testConfig = () => {
  console.log('=== 配置测试 ===');
  console.log('BASE_URL:', BASE_URL);
  console.log('API_TIMEOUT:', API_TIMEOUT);
  console.log('ENV:', ENV);
  console.log('===============');

  return {
    BASE_URL,
    API_TIMEOUT,
    ENV
  };
};

/**
 * 测试Cookie认证
 */
export const testCookieAuth = async () => {
  const authorization = uni.getStorageSync('authorization');

  if (!authorization) {
    console.log('❌ 未找到authorization cookie');
    return {
      success: false,
      message: '未找到authorization cookie'
    };
  }

  console.log('🔍 测试Cookie认证...');
  console.log('Cookie值:', authorization);

  try {
    const res = await uni.request({
      url: BASE_URL + 'user',
      method: 'GET',
      header: {
        'Cookie': `Authorization=${authorization}`,
        'Content-Type': 'application/json'
      },
      withCredentials: true
    });

    console.log('认证响应:', res);

    if (res.statusCode === 200 && res.data && res.data.code === 0) {
      console.log('✅ Cookie认证成功');
      console.log('用户信息:', res.data.data);
      return {
        success: true,
        message: 'Cookie认证成功',
        userInfo: res.data.data,
        statusCode: res.statusCode
      };
    } else if (res.statusCode === 404) {
      console.log('❌ Cookie已过期(404)');
      return {
        success: false,
        message: 'Cookie已过期',
        statusCode: res.statusCode
      };
    } else {
      console.log('❌ Cookie认证失败');
      return {
        success: false,
        message: 'Cookie认证失败',
        statusCode: res.statusCode,
        response: res.data
      };
    }
  } catch (error) {
    console.log('❌ 请求失败:', error);
    return {
      success: false,
      message: '请求失败',
      error: error
    };
  }
};

/**
 * 完整的登录流程测试
 */
export const testLoginFlow = async () => {
  console.log('=== 登录流程测试 ===');

  // 1. 配置测试
  const config = testConfig();

  // 2. Cookie认证测试
  const authResult = await testCookieAuth();

  // 3. 模拟App启动逻辑
  console.log('🚀 模拟App启动逻辑...');

  if (authResult.success) {
    console.log('✅ 应该跳转到首页');
    return {
      shouldNavigateTo: 'index',
      reason: 'Cookie有效',
      userInfo: authResult.userInfo
    };
  } else {
    console.log('✅ 应该跳转到登录页');
    return {
      shouldNavigateTo: 'login',
      reason: authResult.message
    };
  }
};

export default {
  testConfig,
  testCookieAuth,
  testLoginFlow
};
