/**
 * 环境配置文件
 */

// 环境类型
const ENV_TYPE = {
  DEV: 'development',  // 开发环境
  PROD: 'production'   // 生产环境
};

// 当前环境，默认为开发环境
// 可以根据需要修改此变量来切换环境
// 也可以通过其他方式动态设置（如构建时的环境变量）
let currentEnv = ENV_TYPE.DEV;

// 是否是开发环境
export const isDev = () => currentEnv === ENV_TYPE.DEV;

// 是否是生产环境
export const isProd = () => currentEnv === ENV_TYPE.PROD;

// 设置当前环境
export const setEnv = (env) => {
  if (Object.values(ENV_TYPE).includes(env)) {
    currentEnv = env;
    console.log(`环境已切换为: ${env}`);
    uni.setStorageSync('app_env', env);
    return true;
  }
  console.error(`无效的环境类型: ${env}`);
  return false;
};

// 获取当前环境
export const getEnv = () => {
  // 如果本地存储中有环境设置，则使用本地存储的设置
  const storedEnv = uni.getStorageSync('app_env');
  if (storedEnv && Object.values(ENV_TYPE).includes(storedEnv)) {
    currentEnv = storedEnv;
  }
  return currentEnv;
};

// 环境配置
const ENV_CONFIG = {
  [ENV_TYPE.DEV]: {
    // 从环境变量获取开发环境API基础URL
    baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/',
    apiTimeout: 10000,
  },
  [ENV_TYPE.PROD]: {
    // 从环境变量获取生产环境API基础URL
    baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://api.example.com/',
    apiTimeout: 15000,
  }
};

// 获取当前环境的配置
export const getConfig = () => {
  return ENV_CONFIG[getEnv()];
};

// 导出环境类型常量
export const ENV_TYPES = ENV_TYPE;

export default {
  ENV_TYPES,
  getEnv,
  setEnv,
  isDev,
  isProd,
  getConfig
};