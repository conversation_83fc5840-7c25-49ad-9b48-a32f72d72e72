<template>
  <view class="container">
    <view class="content">
      <text class="title">应用测试页面</text>
      <text class="subtitle">如果您看到这个页面，说明应用可以正常启动</text>
      
      <button class="btn" @click="goToLogin">进入登录页</button>
      <button class="btn" @click="testNetwork">测试网络</button>
      
      <view class="info">
        <text>当前时间: {{ currentTime }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const currentTime = ref('');

onMounted(() => {
  updateTime();
  setInterval(updateTime, 1000);
});

const updateTime = () => {
  currentTime.value = new Date().toLocaleString();
};

const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/login'
  });
};

const testNetwork = async () => {
  uni.showLoading({ title: '测试中...' });
  
  try {
    const res = await uni.request({
      url: 'http://**************:8080/user',
      method: 'GET',
      timeout: 5000
    });
    
    uni.hideLoading();
    uni.showModal({
      title: '网络测试成功',
      content: `状态码: ${res.statusCode}`,
      showCancel: false
    });
  } catch (error) {
    uni.hideLoading();
    uni.showModal({
      title: '网络测试失败',
      content: error.message || '网络连接失败',
      showCancel: false
    });
  }
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 40rpx;
  background: #f5f5f5;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  padding: 60rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.btn {
  width: 400rpx;
  height: 80rpx;
  background: #007aff;
  color: white;
  border-radius: 40rpx;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.info {
  margin-top: 40rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
}
</style>
